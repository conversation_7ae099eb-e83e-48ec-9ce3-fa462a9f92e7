import { CommandEnvelopePrimitives } from '../../envelopes/types/command-envelope.type';
import { CommandEnvelope } from '../command-envelope';
import { ActorRole } from '../../envelopes/types/actor-role.enum';

describe('CommandEnvelope', () => {
  const validPrimitives: CommandEnvelopePrimitives = {
    commandId: '550e8400-e29b-41d4-a716-************',
    correlationId: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
    causationId: '6ba7b810-9dad-41d1-80b4-00c04fd430c8',
    issuedAt: 1640995200000,
    actor: {
      role:   ActorRole.HOST,
      participantId: '6ba7b811-9dad-41d1-80b4-00c04fd430c8'
    },
    payload: {
      kind: 'START_SESSION',
      payload: { sessionId: '550e8401-e29b-41d4-a716-************' }
    }
  };

  describe('fromPrimitives', () => {
    it('should construct envelope with valid UUIDs', () => {
      const envelope = CommandEnvelope.fromPrimitives(validPrimitives);
      
      expect(envelope.commandId.toPrimitives()).toBe(validPrimitives.commandId);
      expect(envelope.correlationId.toPrimitives()).toBe(validPrimitives.correlationId);
      expect(envelope.causationId?.toPrimitives()).toBe(validPrimitives.causationId);
      expect(envelope.issuedAt.toPrimitives()).toBe(validPrimitives.issuedAt);
      expect(envelope.actor.role).toBe(validPrimitives.actor.role);
      expect(envelope.actor.participantId?.toPrimitives()).toBe(validPrimitives.actor.participantId);
      expect(envelope.payload).toEqual(validPrimitives.payload);
    });

    it('should default correlationId to commandId when omitted', () => {
      const primitivesWithoutCorrelationId = {
        ...validPrimitives,
        correlationId: undefined
      };
      
      const envelope = CommandEnvelope.fromPrimitives(primitivesWithoutCorrelationId);
      
      expect(envelope.correlationId.toPrimitives()).toBe(validPrimitives.commandId);
    });

    it('should handle optional fields as undefined', () => {
      const minimalPrimitives: CommandEnvelopePrimitives = {
        commandId: '550e8400-e29b-41d4-a716-************',
        issuedAt: 1640995200000,
        actor: {
          role: ActorRole.SYSTEM
        },
        payload: {
          kind: 'SYSTEM_COMMAND',
          payload: {}
        }
      };
      
      const envelope = CommandEnvelope.fromPrimitives(minimalPrimitives);
      
      expect(envelope.causationId).toBeUndefined();
      expect(envelope.actor.participantId).toBeUndefined();
      expect(envelope.correlationId.toPrimitives()).toBe(minimalPrimitives.commandId);
    });

    it('should throw error for invalid UUID', () => {
      const invalidPrimitives = {
        ...validPrimitives,
        commandId: 'invalid-uuid'
      };
      
      expect(() => CommandEnvelope.fromPrimitives(invalidPrimitives)).toThrow();
    });

    it('should throw error for invalid Instant', () => {
      const invalidPrimitives = {
        ...validPrimitives,
        issuedAt: -1
      };
      
      expect(() => CommandEnvelope.fromPrimitives(invalidPrimitives)).toThrow();
    });
  });

  describe('toPrimitives', () => {
    it('should serialize correctly', () => {
      const envelope = CommandEnvelope.fromPrimitives(validPrimitives);
      const serialized = envelope.toPrimitives();
      
      expect(serialized).toEqual(validPrimitives);
    });

    it('should roundtrip correctly', () => {
      const envelope = CommandEnvelope.fromPrimitives(validPrimitives);
      const roundtrip = CommandEnvelope.fromPrimitives(envelope.toPrimitives());
      
      expect(roundtrip.toPrimitives()).toEqual(validPrimitives);
    });
  });
});
