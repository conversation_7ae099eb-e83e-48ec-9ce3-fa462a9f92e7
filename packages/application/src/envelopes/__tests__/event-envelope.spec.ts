import { EventEnvelopePrimitives } from 'envelopes/types/event-envelope.type';
import { EventEnvelope } from '../event-envelope';

describe('EventEnvelope', () => {
  const validPrimitives: EventEnvelopePrimitives = {
    eventId: '550e8400-e29b-41d4-a716-************',
    sessionId: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
    roundId: '6ba7b810-9dad-41d1-80b4-00c04fd430c8',
    roomId: '6ba7b811-9dad-41d1-80b4-00c04fd430c8',
    occurredAt: 1640995200000,
    causationId: '550e8401-e29b-41d4-a716-************',
    correlationId: '550e8402-e29b-41d4-a716-************',
    kind: 'SESSION_STARTED',
    payload: {
      kind: 'SESSION_STARTED',
      payload: { sessionId: 'f47ac10b-58cc-4372-a567-0e02b2c3d479' }
    },
    version: 1
  };

  describe('fromPrimitives', () => {
    it('should construct envelope with valid UUIDs', () => {
      const envelope = EventEnvelope.fromPrimitives(validPrimitives);
      
      expect(envelope.eventId.toPrimitives()).toBe(validPrimitives.eventId);
      expect(envelope.sessionId.toPrimitives()).toBe(validPrimitives.sessionId);
      expect(envelope.roundId?.toPrimitives()).toBe(validPrimitives.roundId);
      expect(envelope.roomId?.toPrimitives()).toBe(validPrimitives.roomId);
      expect(envelope.occurredAt.toPrimitives()).toBe(validPrimitives.occurredAt);
      expect(envelope.causationId?.toPrimitives()).toBe(validPrimitives.causationId);
      expect(envelope.correlationId.toPrimitives()).toBe(validPrimitives.correlationId);
      expect(envelope.kind).toBe(validPrimitives.kind);
      expect(envelope.payload).toEqual(validPrimitives.payload);
      expect(envelope.version).toBe(1);
    });

    it('should handle optional fields as undefined', () => {
      const minimalPrimitives: EventEnvelopePrimitives = {
        eventId: '550e8400-e29b-41d4-a716-************',
        sessionId: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
        occurredAt: 1640995200000,
        correlationId: '550e8402-e29b-41d4-a716-************',
        kind: 'SESSION_STARTED',
        payload: {
          kind: 'SESSION_STARTED',
          payload: {}
        },
        version: 1
      };
      
      const envelope = EventEnvelope.fromPrimitives(minimalPrimitives);
      
      expect(envelope.roundId).toBeUndefined();
      expect(envelope.roomId).toBeUndefined();
      expect(envelope.causationId).toBeUndefined();
    });

    it('should throw error for invalid UUID', () => {
      const invalidPrimitives = {
        ...validPrimitives,
        eventId: 'invalid-uuid'
      };
      
      expect(() => EventEnvelope.fromPrimitives(invalidPrimitives)).toThrow();
    });

    it('should throw error for invalid Instant', () => {
      const invalidPrimitives = {
        ...validPrimitives,
        occurredAt: -1
      };
      
      expect(() => EventEnvelope.fromPrimitives(invalidPrimitives)).toThrow();
    });
  });

  describe('toPrimitives', () => {
    it('should serialize correctly', () => {
      const envelope = EventEnvelope.fromPrimitives(validPrimitives);
      const serialized = envelope.toPrimitives();
      
      expect(serialized).toEqual(validPrimitives);
    });

    it('should roundtrip correctly', () => {
      const envelope = EventEnvelope.fromPrimitives(validPrimitives);
      const roundtrip = EventEnvelope.fromPrimitives(envelope.toPrimitives());
      
      expect(roundtrip.toPrimitives()).toEqual(validPrimitives);
    });
  });
});
