import { Instant, Uuid } from '@repo/domain';
import { ActorRole } from './types/actor-role.enum';
import { DomainCommand } from './types/domain-comman.type';
import { Actor } from './types/actor.type';
import { CommandEnvelopePrimitives } from './types/command-envelope.type';

export class CommandEnvelope {
  private constructor(
    private readonly _commandId: Uuid,
    private readonly _correlationId: Uuid,
    private readonly _causationId: Uuid | undefined,
    private readonly _issuedAt: Instant,
    private readonly _actor: Actor,
    private readonly _payload: DomainCommand,
  ) {}

  static fromPrimitives(raw: CommandEnvelopePrimitives): CommandEnvelope {
    const commandId = Uuid.fromPrimitives(raw.commandId);
    const correlationId = raw.correlationId
      ? Uuid.fromPrimitives(raw.correlationId)
      : commandId; // Default: if correlationId missing → set to commandId
    const causationId = raw.causationId
      ? Uuid.fromPrimitives(raw.causationId)
      : undefined;
    const issuedAt = Instant.fromPrimitives(raw.issuedAt);

    const actor: Actor = {
      role: raw.actor.role,
      participantId: raw.actor.participantId
        ? Uuid.fromPrimitives(raw.actor.participantId)
        : undefined,
    };

    return new CommandEnvelope(
      commandId,
      correlationId,
      causationId,
      issuedAt,
      actor,
      raw.payload,
    );
  }

  toPrimitives(): CommandEnvelopePrimitives {
    return {
      commandId: this._commandId.toPrimitives(),
      correlationId: this._correlationId.toPrimitives(),
      causationId: this._causationId?.toPrimitives(),
      issuedAt: this._issuedAt.toPrimitives(),
      actor: {
        role: this._actor.role,
        participantId: this._actor.participantId?.toPrimitives(),
      },
      payload: this._payload,
    };
  }

  get commandId(): Uuid {
    return this._commandId;
  }

  get correlationId(): Uuid {
    return this._correlationId;
  }

  get causationId(): Uuid | undefined {
    return this._causationId;
  }

  get issuedAt(): Instant {
    return this._issuedAt;
  }

  get actor(): Actor {
    return this._actor;
  }

  get payload(): DomainCommand {
    return this._payload;
  }
}
