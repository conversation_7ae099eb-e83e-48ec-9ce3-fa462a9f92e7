import { Uuid, Instant } from "@repo/domain";
import { DomainEvent } from "./types/domain-event.type";
import { EventEnvelopePrimitives } from "./types/event-envelope.type";

export class EventEnvelope {
  private constructor(
    private readonly _eventId: Uuid,
    private readonly _sessionId: Uuid,
    private readonly _roundId: Uuid | undefined,
    private readonly _roomId: Uuid | undefined,
    private readonly _occurredAt: Instant,
    private readonly _causationId: Uuid | undefined,
    private readonly _correlationId: Uuid,
    private readonly _kind: string, // Define a Kind enum
    private readonly _payload: DomainEvent,
    private readonly _version: 1
  ) {}

  static fromPrimitives(raw: EventEnvelopePrimitives): EventEnvelope {
    const eventId = Uuid.fromPrimitives(raw.eventId);
    const sessionId = Uuid.fromPrimitives(raw.sessionId);
    const roundId = raw.roundId ? Uuid.fromPrimitives(raw.roundId) : undefined;
    const roomId = raw.roomId ? Uuid.fromPrimitives(raw.roomId) : undefined;
    const occurredAt = Instant.fromPrimitives(raw.occurredAt);
    const causationId = raw.causationId ? Uuid.fromPrimitives(raw.causationId) : undefined;
    const correlationId = Uuid.fromPrimitives(raw.correlationId);

    return new EventEnvelope(
      eventId,
      sessionId,
      roundId,
      roomId,
      occurredAt,
      causationId,
      correlationId,
      raw.kind,
      raw.payload,
      raw.version
    );
  }

  toPrimitives(): EventEnvelopePrimitives {
    return {
      eventId: this._eventId.toPrimitives(),
      sessionId: this._sessionId.toPrimitives(),
      roundId: this._roundId?.toPrimitives(),
      roomId: this._roomId?.toPrimitives(),
      occurredAt: this._occurredAt.toPrimitives(),
      causationId: this._causationId?.toPrimitives(),
      correlationId: this._correlationId.toPrimitives(),
      kind: this._kind,
      payload: this._payload,
      version: this._version
    };
  }

  get eventId(): Uuid {
    return this._eventId;
  }

  get sessionId(): Uuid {
    return this._sessionId;
  }

  get roundId(): Uuid | undefined {
    return this._roundId;
  }

  get roomId(): Uuid | undefined {
    return this._roomId;
  }

  get occurredAt(): Instant {
    return this._occurredAt;
  }

  get causationId(): Uuid | undefined {
    return this._causationId;
  }

  get correlationId(): Uuid {
    return this._correlationId;
  }

  get kind(): string {
    return this._kind;
  }

  get payload(): DomainEvent {
    return this._payload;
  }

  get version(): 1 {
    return this._version;
  }
}
