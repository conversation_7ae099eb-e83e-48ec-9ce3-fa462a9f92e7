import { ensure } from '../../../support/ensure';
import { Duration } from '../../../primitives/duration/duration.primitive';
import { PersistenceMappingError } from '../../../errors/persistence-mapping-error';

export type RoundKind = 'ICE_BREAKER' | 'MAIN_TOPIC' | 'FREE_TALK';

export type RoundSpecPrimitives = {
  kind: RoundKind;
  durationMs: number;
  autoCloseGraceMs?: number;
  questions?: string[];
};

export class RoundSpec {
  private constructor(
    public readonly kind: RoundKind,
    public readonly duration: Duration,
    public readonly autoCloseGrace?: Duration,
    public readonly questions: string[] = []
  ) {}

  static fromPrimitives(dto: RoundSpecPrimitives): RoundSpec {
    try {
      ensure(dto != null, new PersistenceMappingError('RoundSpec DTO is null or undefined'));
      
      ensure(
        ['ICE_BREAKER', 'MAIN_TOPIC', 'FREE_TALK'].includes(dto.kind),
        new PersistenceMappingError('Invalid round kind', { kind: dto.kind })
      );

      const duration = Duration.fromPrimitives(dto.durationMs);
      const autoCloseGrace = dto.autoCloseGraceMs ? Duration.fromPrimitives(dto.autoCloseGraceMs) : undefined;
      const questions = dto.questions || [];

      return new RoundSpec(dto.kind, duration, autoCloseGrace, questions);
    } catch (error) {
      if (error instanceof PersistenceMappingError) {
        throw error;
      }
      throw new PersistenceMappingError('Failed to map round spec from primitives', { originalError: error });
    }
  }

  toPrimitives(): RoundSpecPrimitives {
    return {
      kind: this.kind,
      durationMs: this.duration.toPrimitives(),
      autoCloseGraceMs: this.autoCloseGrace?.toPrimitives(),
      questions: this.questions.length > 0 ? [...this.questions] : undefined,
    };
  }
}
