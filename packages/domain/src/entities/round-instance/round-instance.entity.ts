import { ensure } from '../../support/ensure';
import { Uuid } from '../../primitives/uuid/uuid.primitive';
import { NonNegativeInt } from '../../primitives/non-negative-int/non-negative-int.primitive';
import { Instant } from '../../primitives/instant/instant.primitive';
import { RoundSpec } from '../../rounds/value-objects/round-spec/round-spec.vo';
import { PersistenceMappingError } from '../../errors/persistence-mapping-error';
import { RoundInstanceStateError } from './round-instance.errors';
import { RoundInstanceState } from './contracts/round-instance-state.enum';
import { RoundInstancePrimitives } from './contracts/round-instance.type';




export class RoundInstance {
  private constructor(
    public readonly roundId: Uuid,
    public readonly index: NonNegativeInt,
    public readonly spec: RoundSpec,
    private state: RoundInstanceState,
    private startedAt?: Instant,
    private endedAt?: Instant,
    private closedAt?: Instant
  ) {}

  static fromPrimitives(dto: RoundInstancePrimitives): RoundInstance {
    try {
      ensure(dto != null, new PersistenceMappingError('RoundInstance DTO is null or undefined'));
      
      const roundId = Uuid.fromPrimitives(dto.roundId);
      const index = NonNegativeInt.fromPrimitives(dto.index);
      const spec = RoundSpec.fromPrimitives(dto.spec);
      
      ensure(
        Object.values(RoundInstanceState).includes(dto.state),
        new PersistenceMappingError('Invalid round instance state', { state: dto.state })
      );

      const startedAt = dto.startedAt ? Instant.fromPrimitives(dto.startedAt) : undefined;
      const endedAt = dto.endedAt ? Instant.fromPrimitives(dto.endedAt) : undefined;
      const closedAt = dto.closedAt ? Instant.fromPrimitives(dto.closedAt) : undefined;

      return new RoundInstance(roundId, index, spec, dto.state, startedAt, endedAt, closedAt);
    } catch (error) {
      if (error instanceof PersistenceMappingError) {
        throw error;
      }
      throw new PersistenceMappingError('Failed to map round instance from primitives', { originalError: error });
    }
  }

  static create(roundId: Uuid, index: NonNegativeInt, spec: RoundSpec): RoundInstance {
    return new RoundInstance(roundId, index, spec, RoundInstanceState.PENDING);
  }

  toPrimitives(): RoundInstancePrimitives {
    return {
      roundId: this.roundId.toPrimitives(),
      index: this.index.toPrimitives(),
      spec: this.spec.toPrimitives(),
      state: this.state,
      startedAt: this.startedAt?.toPrimitives(),
      endedAt: this.endedAt?.toPrimitives(),
      closedAt: this.closedAt?.toPrimitives(),
    };
  }

  start(at: Instant): RoundInstance {
    ensure(
      this.state === RoundInstanceState.PENDING,
      new RoundInstanceStateError('Cannot start round unless in PENDING state', {
        currentState: this.state,
        roundId: this.roundId.toPrimitives(),
        index: this.index.toPrimitives()
      })
    );

    // Validate timestamp progression
    if (this.endedAt) {
      ensure(
        at.toPrimitives() >= this.endedAt.toPrimitives(),
        new RoundInstanceStateError('Start time cannot be before previous end time', {
          startTime: at.toPrimitives(),
          previousEndTime: this.endedAt.toPrimitives(),
          roundId: this.roundId.toPrimitives()
        })
      );
    }

    // Calculate end time based on spec duration
    const endTime = Instant.fromPrimitives(at.toPrimitives() + this.spec.duration.toPrimitives());

    return new RoundInstance(
      this.roundId,
      this.index,
      this.spec,
      RoundInstanceState.ACTIVE,
      at,
      endTime,
      this.closedAt
    );
  }

  beginClosing(at: Instant): RoundInstance {
    ensure(
      RoundInstanceState.ACTIVE,
      new RoundInstanceStateError('Cannot begin closing round unless in ACTIVE state', {
        currentState: this.state,
        roundId: this.roundId.toPrimitives(),
        index: this.index.toPrimitives()
      })
    );

    // Validate timestamp progression
    if (this.startedAt) {
      ensure(
        at.toPrimitives() >= this.startedAt.toPrimitives(),
        new RoundInstanceStateError('Closing time cannot be before start time', {
          closingTime: at.toPrimitives(),
          startTime: this.startedAt.toPrimitives(),
          roundId: this.roundId.toPrimitives()
        })
      );
    }

    return new RoundInstance(
      this.roundId,
      this.index,
      this.spec,
      RoundInstanceState.CLOSING,
      this.startedAt,
      at, // Update end time to actual closing time
      this.closedAt
    );
  }

  close(at: Instant): RoundInstance {
    ensure(
      this.state === RoundInstanceState.CLOSING,
      new RoundInstanceStateError('Cannot close round unless in CLOSING state', {
        currentState: this.state,
        roundId: this.roundId.toPrimitives(),
        index: this.index.toPrimitives()
      })
    );

    // Validate timestamp progression
    if (this.endedAt) {
      ensure(
        at.toPrimitives() >= this.endedAt.toPrimitives(),
        new RoundInstanceStateError('Close time cannot be before end time', {
          closeTime: at.toPrimitives(),
          endTime: this.endedAt.toPrimitives(),
          roundId: this.roundId.toPrimitives()
        })
      );
    }

    return new RoundInstance(
      this.roundId,
      this.index,
      this.spec,
      RoundInstanceState.CLOSED,
      this.startedAt,
      this.endedAt,
      at
    );
  }

  // Getters for state inspection
  get currentState(): RoundInstanceState {
    return this.state;
  }

  get isPending(): boolean {
    return this.state === RoundInstanceState.PENDING;
  }

  get isActive(): boolean {
    return this.state === RoundInstanceState.ACTIVE;
  }

  get isClosing(): boolean {
    return this.state === RoundInstanceState.CLOSING;
  }

  get isClosed(): boolean {
    return this.state === RoundInstanceState.CLOSED;
  }

  get startTime(): number | undefined {
    return this.startedAt?.toPrimitives();
  }

  get endTime(): number | undefined {
    return this.endedAt?.toPrimitives();
  }

  get closeTime(): number | undefined {
    return this.closedAt?.toPrimitives();
  }

  get roundIndex(): number {
    return this.index.toPrimitives();
  }
}
