/* eslint-disable @typescript-eslint/no-explicit-any */
import { RoundInstance } from '../round-instance.entity';
import { RoundInstanceStateError, ERR_ENTITY_ROUND_INSTANCE_STATE } from '../round-instance.errors';
import { PersistenceMappingError } from '../../../errors/persistence-mapping-error';
import { Uuid } from '../../../primitives/uuid/uuid.primitive';
import { NonNegativeInt } from '../../../primitives/non-negative-int/non-negative-int.primitive';
import { Instant } from '../../../primitives/instant/instant.primitive';
import { RoundSpec, RoundSpecPrimitives } from '../../../rounds/value-objects/round-spec/round-spec.vo';
import { RoundInstancePrimitives } from '../contracts/round-instance.type';
import { RoundInstanceState } from '../contracts/round-instance-state.enum';

describe('RoundInstance', () => {
  const validRoundId = '550e8400-e29b-41d4-a716-************';
  const validIndex = 0;
  const baseTimestamp = 1640995200000; // 2022-01-01 00:00:00 UTC
  const laterTimestamp = 1640995260000; // 1 minute later
  const evenLaterTimestamp = 1640995320000; // 2 minutes later
  const muchLaterTimestamp = 1640996100000; // 15 minutes later

  const validRoundSpec: RoundSpecPrimitives = {
    kind: 'MAIN_TOPIC',
    durationMs: 900000, // 15 minutes
    questions: ['What is your favorite color?'],
  };

  const validRoundInstanceDto: RoundInstancePrimitives = {
    roundId: validRoundId,
    index: validIndex,
    spec: validRoundSpec,
    state: RoundInstanceState.PENDING,
  };

  describe('fromPrimitives', () => {
    it('creates round instance from valid DTO', () => {
      const roundInstance = RoundInstance.fromPrimitives(validRoundInstanceDto);
      
      expect(roundInstance).toBeInstanceOf(RoundInstance);
      expect(roundInstance.roundId.toPrimitives()).toBe(validRoundId);
      expect(roundInstance.roundIndex).toBe(validIndex);
      expect(roundInstance.currentState).toBe(RoundInstanceState.PENDING);
      expect(roundInstance.isPending).toBe(true);
      expect(roundInstance.isActive).toBe(false);
      expect(roundInstance.isClosing).toBe(false);
      expect(roundInstance.isClosed).toBe(false);
      expect(roundInstance.startTime).toBeUndefined();
      expect(roundInstance.endTime).toBeUndefined();
      expect(roundInstance.closeTime).toBeUndefined();
    });

    it('creates round instance with all timestamps', () => {
      const fullDto: RoundInstancePrimitives = {
        ...validRoundInstanceDto,
        state: RoundInstanceState.CLOSED,
        startedAt: baseTimestamp,
        endedAt: laterTimestamp,
        closedAt: evenLaterTimestamp,
      };
      
      const roundInstance = RoundInstance.fromPrimitives(fullDto);
      
      expect(roundInstance.currentState).toBe(RoundInstanceState.CLOSED);
      expect(roundInstance.isClosed).toBe(true);
      expect(roundInstance.startTime).toBe(baseTimestamp);
      expect(roundInstance.endTime).toBe(laterTimestamp);
      expect(roundInstance.closeTime).toBe(evenLaterTimestamp);
    });

    it('throws PersistenceMappingError for null DTO', () => {
      expect(() => RoundInstance.fromPrimitives(null as any)).toThrow(PersistenceMappingError);
    });

    it('throws PersistenceMappingError for invalid roundId', () => {
      const invalidDto = { ...validRoundInstanceDto, roundId: 'invalid-uuid' };
      expect(() => RoundInstance.fromPrimitives(invalidDto)).toThrow(PersistenceMappingError);
    });

    it('throws PersistenceMappingError for invalid index', () => {
      const invalidDto = { ...validRoundInstanceDto, index: -1 };
      expect(() => RoundInstance.fromPrimitives(invalidDto)).toThrow(PersistenceMappingError);
    });

    it('throws PersistenceMappingError for invalid state', () => {
      const invalidDto = { ...validRoundInstanceDto, state: 'INVALID_STATE' as any };
      expect(() => RoundInstance.fromPrimitives(invalidDto)).toThrow(PersistenceMappingError);
    });

    it('throws PersistenceMappingError for invalid timestamp', () => {
      const invalidDto = { ...validRoundInstanceDto, startedAt: -1 };
      expect(() => RoundInstance.fromPrimitives(invalidDto)).toThrow(PersistenceMappingError);
    });
  });

  describe('create', () => {
    it('creates new round instance in PENDING state', () => {
      const roundId = Uuid.fromPrimitives(validRoundId);
      const index = NonNegativeInt.fromPrimitives(validIndex);
      const spec = RoundSpec.fromPrimitives(validRoundSpec);
      
      const roundInstance = RoundInstance.create(roundId, index, spec);
      
      expect(roundInstance.roundId).toBe(roundId);
      expect(roundInstance.index).toBe(index);
      expect(roundInstance.spec).toBe(spec);
      expect(roundInstance.currentState).toBe(RoundInstanceState.PENDING);
      expect(roundInstance.isPending).toBe(true);
      expect(roundInstance.startTime).toBeUndefined();
      expect(roundInstance.endTime).toBeUndefined();
      expect(roundInstance.closeTime).toBeUndefined();
    });
  });

  describe('toPrimitives', () => {
    it('roundtrip preserves data', () => {
      const original = validRoundInstanceDto;
      const roundInstance = RoundInstance.fromPrimitives(original);
      const result = roundInstance.toPrimitives();
      
      expect(result.roundId).toBe(original.roundId);
      expect(result.index).toBe(original.index);
      expect(result.state).toBe(original.state);
      expect(result.spec).toEqual(original.spec);
      expect(result.startedAt).toBeUndefined();
      expect(result.endedAt).toBeUndefined();
      expect(result.closedAt).toBeUndefined();
    });

    it('includes all timestamps when present', () => {
      const fullDto: RoundInstancePrimitives = {
        ...validRoundInstanceDto,
        state: RoundInstanceState.CLOSED,
        startedAt: baseTimestamp,
        endedAt: laterTimestamp,
        closedAt: evenLaterTimestamp,
      };
      
      const roundInstance = RoundInstance.fromPrimitives(fullDto);
      const result = roundInstance.toPrimitives();
      
      expect(result.startedAt).toBe(baseTimestamp);
      expect(result.endedAt).toBe(laterTimestamp);
      expect(result.closedAt).toBe(evenLaterTimestamp);
    });
  });

  describe('start', () => {
    it('transitions from PENDING to ACTIVE and sets timestamps', () => {
      const roundInstance = RoundInstance.fromPrimitives(validRoundInstanceDto);
      const at = Instant.fromPrimitives(baseTimestamp);
      
      const startedInstance = roundInstance.start(at);
      
      expect(startedInstance.currentState).toBe(RoundInstanceState.ACTIVE);
      expect(startedInstance.isActive).toBe(true);
      expect(startedInstance.startTime).toBe(baseTimestamp);
      expect(startedInstance.endTime).toBe(baseTimestamp + 900000); // Start + 15 minutes
      expect(startedInstance.closeTime).toBeUndefined();
      expect(startedInstance).not.toBe(roundInstance); // Immutable
    });

    it('throws RoundInstanceStateError when not in PENDING state', () => {
      const activeDto = { ...validRoundInstanceDto, state: RoundInstanceState.ACTIVE };
      const roundInstance = RoundInstance.fromPrimitives(activeDto);
      const at = Instant.fromPrimitives(baseTimestamp);
      
      expect(() => roundInstance.start(at)).toThrow(RoundInstanceStateError);
      expect(() => roundInstance.start(at)).toThrow('Cannot start round unless in PENDING state');
    });

    it('throws RoundInstanceStateError when start time is before previous end time', () => {
      const dtoWithPreviousEnd = { 
        ...validRoundInstanceDto, 
        endedAt: laterTimestamp 
      };
      const roundInstance = RoundInstance.fromPrimitives(dtoWithPreviousEnd);
      const invalidStartTime = Instant.fromPrimitives(baseTimestamp); // Before previous end
      
      expect(() => roundInstance.start(invalidStartTime)).toThrow(RoundInstanceStateError);
      expect(() => roundInstance.start(invalidStartTime)).toThrow('Start time cannot be before previous end time');
    });
  });

  describe('beginClosing', () => {
    it('transitions from ACTIVE to CLOSING and updates end time', () => {
      const activeDto: RoundInstancePrimitives = {
        ...validRoundInstanceDto,
        state: RoundInstanceState.ACTIVE,
        startedAt: baseTimestamp,
        endedAt: muchLaterTimestamp, // Original planned end time
      };
      const roundInstance = RoundInstance.fromPrimitives(activeDto);
      const at = Instant.fromPrimitives(laterTimestamp); // Earlier than planned
      
      const closingInstance = roundInstance.beginClosing(at);
      
      expect(closingInstance.currentState).toBe(RoundInstanceState.CLOSING);
      expect(closingInstance.isClosing).toBe(true);
      expect(closingInstance.startTime).toBe(baseTimestamp);
      expect(closingInstance.endTime).toBe(laterTimestamp); // Updated to actual closing time
      expect(closingInstance.closeTime).toBeUndefined();
      expect(closingInstance).not.toBe(roundInstance); // Immutable
    });

    it('throws RoundInstanceStateError when not in ACTIVE state', () => {
      const roundInstance = RoundInstance.fromPrimitives(validRoundInstanceDto); // PENDING
      const at = Instant.fromPrimitives(baseTimestamp);
      
      expect(() => roundInstance.beginClosing(at)).toThrow(RoundInstanceStateError);
      expect(() => roundInstance.beginClosing(at)).toThrow('Cannot begin closing round unless in ACTIVE state');
    });

    it('throws RoundInstanceStateError when closing time is before start time', () => {
      const activeDto: RoundInstancePrimitives = {
        ...validRoundInstanceDto,
        state: RoundInstanceState.ACTIVE,
        startedAt: laterTimestamp,
      };
      const roundInstance = RoundInstance.fromPrimitives(activeDto);
      const invalidClosingTime = Instant.fromPrimitives(baseTimestamp); // Before start time
      
      expect(() => roundInstance.beginClosing(invalidClosingTime)).toThrow(RoundInstanceStateError);
      expect(() => roundInstance.beginClosing(invalidClosingTime)).toThrow('Closing time cannot be before start time');
    });
  });

  describe('close', () => {
    it('transitions from CLOSING to CLOSED and sets close time', () => {
      const closingDto: RoundInstancePrimitives = {
        ...validRoundInstanceDto,
        state: RoundInstanceState.CLOSING,
        startedAt: baseTimestamp,
        endedAt: laterTimestamp,
      };
      const roundInstance = RoundInstance.fromPrimitives(closingDto);
      const at = Instant.fromPrimitives(evenLaterTimestamp);

      const closedInstance = roundInstance.close(at);

      expect(closedInstance.currentState).toBe(RoundInstanceState.CLOSED);
      expect(closedInstance.isClosed).toBe(true);
      expect(closedInstance.startTime).toBe(baseTimestamp);
      expect(closedInstance.endTime).toBe(laterTimestamp);
      expect(closedInstance.closeTime).toBe(evenLaterTimestamp);
      expect(closedInstance).not.toBe(roundInstance); // Immutable
    });

    it('throws RoundInstanceStateError when not in CLOSING state', () => {
      const roundInstance = RoundInstance.fromPrimitives(validRoundInstanceDto); // PENDING
      const at = Instant.fromPrimitives(baseTimestamp);

      expect(() => roundInstance.close(at)).toThrow(RoundInstanceStateError);
      expect(() => roundInstance.close(at)).toThrow('Cannot close round unless in CLOSING state');
    });

    it('throws RoundInstanceStateError when close time is before end time', () => {
      const closingDto: RoundInstancePrimitives = {
        ...validRoundInstanceDto,
        state: RoundInstanceState.CLOSING,
        endedAt: laterTimestamp,
      };
      const roundInstance = RoundInstance.fromPrimitives(closingDto);
      const invalidCloseTime = Instant.fromPrimitives(baseTimestamp); // Before end time

      expect(() => roundInstance.close(invalidCloseTime)).toThrow(RoundInstanceStateError);
      expect(() => roundInstance.close(invalidCloseTime)).toThrow('Close time cannot be before end time');
    });
  });

  describe('state getters', () => {
    it('isPending returns correct state', () => {
      const pending = RoundInstance.fromPrimitives(validRoundInstanceDto);
      expect(pending.isPending).toBe(true);
      expect(pending.isActive).toBe(false);
      expect(pending.isClosing).toBe(false);
      expect(pending.isClosed).toBe(false);
    });

    it('isActive returns correct state', () => {
      const activeDto = { ...validRoundInstanceDto, state: RoundInstanceState.ACTIVE };
      const active = RoundInstance.fromPrimitives(activeDto);
      expect(active.isPending).toBe(false);
      expect(active.isActive).toBe(true);
      expect(active.isClosing).toBe(false);
      expect(active.isClosed).toBe(false);
    });

    it('isClosing returns correct state', () => {
      const closingDto = { ...validRoundInstanceDto, state: RoundInstanceState.CLOSING };
      const closing = RoundInstance.fromPrimitives(closingDto);
      expect(closing.isPending).toBe(false);
      expect(closing.isActive).toBe(false);
      expect(closing.isClosing).toBe(true);
      expect(closing.isClosed).toBe(false);
    });

    it('isClosed returns correct state', () => {
      const closedDto = { ...validRoundInstanceDto, state: RoundInstanceState.CLOSED };
      const closed = RoundInstance.fromPrimitives(closedDto);
      expect(closed.isPending).toBe(false);
      expect(closed.isActive).toBe(false);
      expect(closed.isClosing).toBe(false);
      expect(closed.isClosed).toBe(true);
    });
  });

  describe('error context preservation', () => {
    it('preserves context in RoundInstanceStateError', () => {
      const roundInstance = RoundInstance.fromPrimitives(validRoundInstanceDto);
      const at = Instant.fromPrimitives(baseTimestamp);

      try {
        roundInstance.beginClosing(at); // Should fail because state is PENDING
      } catch (error) {
        expect(error).toBeInstanceOf(RoundInstanceStateError);
        expect((error as RoundInstanceStateError).code).toBe(ERR_ENTITY_ROUND_INSTANCE_STATE);
        expect((error as RoundInstanceStateError).ctx?.currentState).toBe(RoundInstanceState.PENDING);
        expect((error as RoundInstanceStateError).ctx?.roundId).toBe(validRoundId);
        expect((error as RoundInstanceStateError).ctx?.index).toBe(validIndex);
      }
    });
  });

  describe('complete state machine lifecycle', () => {
    it('follows complete PENDING → ACTIVE → CLOSING → CLOSED lifecycle', () => {
      const roundId = Uuid.fromPrimitives(validRoundId);
      const index = NonNegativeInt.fromPrimitives(validIndex);
      const spec = RoundSpec.fromPrimitives(validRoundSpec);

      // Create in PENDING state
      let roundInstance = RoundInstance.create(roundId, index, spec);
      expect(roundInstance.isPending).toBe(true);

      // Start round: PENDING → ACTIVE
      roundInstance = roundInstance.start(Instant.fromPrimitives(baseTimestamp));
      expect(roundInstance.isActive).toBe(true);
      expect(roundInstance.startTime).toBe(baseTimestamp);
      expect(roundInstance.endTime).toBe(baseTimestamp + 900000);

      // Begin closing: ACTIVE → CLOSING
      roundInstance = roundInstance.beginClosing(Instant.fromPrimitives(laterTimestamp));
      expect(roundInstance.isClosing).toBe(true);
      expect(roundInstance.endTime).toBe(laterTimestamp); // Updated to actual end time

      // Close round: CLOSING → CLOSED
      roundInstance = roundInstance.close(Instant.fromPrimitives(evenLaterTimestamp));
      expect(roundInstance.isClosed).toBe(true);
      expect(roundInstance.closeTime).toBe(evenLaterTimestamp);

      // Verify all timestamps are preserved
      expect(roundInstance.startTime).toBe(baseTimestamp);
      expect(roundInstance.endTime).toBe(laterTimestamp);
      expect(roundInstance.closeTime).toBe(evenLaterTimestamp);
    });
  });
});
