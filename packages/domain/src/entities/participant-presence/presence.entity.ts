import { ensure } from '../../support/ensure';
import { Instant } from '../../primitives/instant/instant.primitive';
import { RoomId } from '../../rooms/value-objects/room-id/room-id.vo';
import { PersistenceMappingError } from '../../errors/persistence-mapping-error';
import { PresenceTimeError } from './presence.errors';
import { ParticipantPresencePrimitives } from './contracts/participant-presence.type';
import { ParticipantRole } from '../../participants/types/participant-role.enum';
import { ParticipantId } from '../../participants/value-objects/participant-id/participant-id.vo';

export class ParticipantPresence {
  private constructor(
    public readonly participantId: ParticipantId,
    private joinedAt?: Instant,
    private leftAt?: Instant,
    private roomId?: RoomId,
    // TODO: Change this to metadata, and make it a record of key-value pairs (string, string)
    private tags: string[] = [],
  ) {}

  static fromPrimitives(
    dto: ParticipantPresencePrimitives,
  ): ParticipantPresence {
    try {
      ensure(
        dto != null,
        new PersistenceMappingError(
          'ParticipantPresence DTO is null or undefined',
        ),
      );

      const participantId = ParticipantId.fromPrimitives(dto.participantId);
      const joinedAt = dto.joinedAt
        ? Instant.fromPrimitives(dto.joinedAt)
        : undefined;
      const leftAt = dto.leftAt
        ? Instant.fromPrimitives(dto.leftAt)
        : undefined;
      const currentRoomId = dto.currentRoomId
        ? RoomId.fromPrimitives(dto.currentRoomId)
        : undefined;

      // Dedupe and sort tags for deterministic behavior
      const tags = dto.tags ? [...new Set(dto.tags)].sort() : [];

      return new ParticipantPresence(
        participantId,
        joinedAt,
        leftAt,
        currentRoomId,
        tags,
      );
    } catch (error) {
      if (error instanceof PersistenceMappingError) {
        throw error;
      }
      throw new PersistenceMappingError(
        'Failed to map participant presence from primitives',
        { originalError: error },
      );
    }
  }

  static create(
    participantId: ParticipantId,
    role?: ParticipantRole,
  ): ParticipantPresence {
    const tags = role ? [`role-${role}`] : [];
    return new ParticipantPresence(participantId, undefined, undefined, undefined, tags);
  }

  toPrimitives(): ParticipantPresencePrimitives {
    return {
      participantId: this.participantId.toPrimitives(),
      joinedAt: this.joinedAt?.toPrimitives(),
      leftAt: this.leftAt?.toPrimitives(),
      currentRoomId: this.roomId?.toPrimitives(),
      tags: this.tags.length > 0 ? [...this.tags] : undefined,
    };
  }

  onJoin(at: Instant): ParticipantPresence {
    // Validate time progression if we have a previous timestamp
    if (this.leftAt) {
      ensure(
        at.toPrimitives() >= this.leftAt.toPrimitives(),
        new PresenceTimeError('Join time cannot be before last leave time', {
          joinTime: at.toPrimitives(),
          lastLeaveTime: this.leftAt.toPrimitives(),
          participantId: this.participantId.toPrimitives(),
        }),
      );
    }

    return new ParticipantPresence(
      this.participantId,
      this.joinedAt || at, // Set join time on first join, preserve on subsequent joins
      undefined, // Clear leftAt
      this.roomId,
      this.tags,
    );
  }

  onLeave(at: Instant): ParticipantPresence {
    // Validate time progression
    if (this.joinedAt) {
      ensure(
        at.toPrimitives() >= this.joinedAt.toPrimitives(),
        new PresenceTimeError('Leave time cannot be before join time', {
          leaveTime: at.toPrimitives(),
          joinTime: this.joinedAt.toPrimitives(),
          participantId: this.participantId.toPrimitives(),
        }),
      );
    }

    return new ParticipantPresence(
      this.participantId,
      this.joinedAt,
      at,
      undefined, // Clear current room when leaving
      this.tags,
    );
  }

  enterRoom(roomId: RoomId, at: Instant): ParticipantPresence {
    // Validate time progression against join time
    if (this.joinedAt) {
      ensure(
        at.toPrimitives() >= this.joinedAt.toPrimitives(),
        new PresenceTimeError('Room enter time cannot be before join time', {
          enterTime: at.toPrimitives(),
          joinTime: this.joinedAt.toPrimitives(),
          participantId: this.participantId.toPrimitives(),
        }),
      );
    }

    // Validate time progression against leave time
    if (this.leftAt) {
      ensure(
        at.toPrimitives() <= this.leftAt.toPrimitives(),
        new PresenceTimeError('Room enter time cannot be after leave time', {
          enterTime: at.toPrimitives(),
          leaveTime: this.leftAt.toPrimitives(),
          participantId: this.participantId.toPrimitives(),
        }),
      );
    }

    return new ParticipantPresence(
      this.participantId,
      this.joinedAt,
      null,
      roomId,
      this.tags,
    );
  }

  exitRoom(at: Instant): ParticipantPresence {
    // Validate time progression against join time
    if (this.joinedAt) {
      ensure(
        at.toPrimitives() >= this.joinedAt.toPrimitives(),
        new PresenceTimeError('Room exit time cannot be before join time', {
          exitTime: at.toPrimitives(),
          joinTime: this.joinedAt.toPrimitives(),
          participantId: this.participantId.toPrimitives(),
        }),
      );
    }

    // Validate time progression against leave time
    if (this.leftAt) {
      ensure(
        at.toPrimitives() <= this.leftAt.toPrimitives(),
        new PresenceTimeError('Room exit time cannot be after leave time', {
          exitTime: at.toPrimitives(),
          leaveTime: this.leftAt.toPrimitives(),
          participantId: this.participantId.toPrimitives(),
        }),
      );
    }

    return new ParticipantPresence(
      this.participantId,
      this.joinedAt,
      at,
      undefined, // Clear current room
      this.tags,
    );
  }

  updateTags(tags: string[]): ParticipantPresence {
    // Dedupe and sort for deterministic behavior
    const deduped = [...new Set(tags)].sort();

    // Return same instance if tags haven't changed
    if (JSON.stringify(this.tags) === JSON.stringify(deduped)) {
      return this;
    }

    return new ParticipantPresence(
      this.participantId,
      this.joinedAt,
      this.leftAt,
      this.roomId,
      deduped,
    );
  }

  // Getters for state inspection
  get isConnected(): boolean {
    return this.joinedAt != null && this.leftAt == null;
  }

  get hasJoined(): boolean {
    return this.joinedAt != null;
  }

  get hasLeft(): boolean {
    return this.leftAt != null;
  }

  get isInRoom(): boolean {
    return this.roomId != null;
  }

  get currentRoomId(): string | undefined {
    return this.roomId?.toPrimitives();
  }

  get joinTime(): number | undefined {
    return this.joinedAt?.toPrimitives();
  }

  get leaveTime(): number | undefined {
    return this.leftAt?.toPrimitives();
  }

  get currentTags(): string[] {
    return [...this.tags];
  }
}
