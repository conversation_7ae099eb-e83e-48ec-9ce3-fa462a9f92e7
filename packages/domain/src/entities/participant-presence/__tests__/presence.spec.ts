/* eslint-disable @typescript-eslint/no-explicit-any */
import { ParticipantPresence } from '../presence.entity';
import { PresenceTimeError, ERR_ENTITY_PRESENCE_TIME } from '../presence.errors';
import { PersistenceMappingError } from '../../../errors/persistence-mapping-error';
import { Uuid } from '../../../primitives/uuid/uuid.primitive';
import { Instant } from '../../../primitives/instant/instant.primitive';
import { RoomId } from '../../../rooms/value-objects/room-id/room-id.vo';
import { ParticipantPresencePrimitives } from '../contracts/participant-presence.type';

describe('ParticipantPresence', () => {
  const validParticipantId = '550e8400-e29b-41d4-a716-************';
  const validRoomId = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
  const baseTimestamp = 1640995200000; // 2022-01-01 00:00:00 UTC
  const laterTimestamp = 1640995260000; // 1 minute later
  const evenLaterTimestamp = 1640995320000; // 2 minutes later

  const validPresenceDto: ParticipantPresencePrimitives = {
    participantId: validParticipantId,
    joinedAt: baseTimestamp,
    currentRoomId: validRoomId,
    tags: ['tag1', 'tag2'],
  };

  describe('fromPrimitives', () => {
    it('creates presence from valid DTO', () => {
      const presence = ParticipantPresence.fromPrimitives(validPresenceDto);
      
      expect(presence).toBeInstanceOf(ParticipantPresence);
      expect(presence.participantId.toPrimitives()).toBe(validParticipantId);
      expect(presence.isConnected).toBe(true);
      expect(presence.hasJoined).toBe(true);
      expect(presence.hasLeft).toBe(false);
      expect(presence.isInRoom).toBe(true);
      expect(presence.currentRoomId).toBe(validRoomId);
      expect(presence.joinTime).toBe(baseTimestamp);
      expect(presence.currentTags).toEqual(['tag1', 'tag2']);
    });

    it('creates presence without optional fields', () => {
      const minimalDto: ParticipantPresencePrimitives = {
        participantId: validParticipantId,
      };
      
      const presence = ParticipantPresence.fromPrimitives(minimalDto);
      
      expect(presence.isConnected).toBe(false);
      expect(presence.hasJoined).toBe(false);
      expect(presence.hasLeft).toBe(false);
      expect(presence.isInRoom).toBe(false);
      expect(presence.currentRoomId).toBeUndefined();
      expect(presence.joinTime).toBeUndefined();
      expect(presence.leaveTime).toBeUndefined();
      expect(presence.currentTags).toEqual([]);
    });

    it('deduplicates and sorts tags', () => {
      const dtoWithDuplicates = { 
        ...validPresenceDto, 
        tags: ['tag2', 'tag1', 'tag2', 'tag3'] 
      };
      
      const presence = ParticipantPresence.fromPrimitives(dtoWithDuplicates);
      expect(presence.currentTags).toEqual(['tag1', 'tag2', 'tag3']);
    });

    it('throws PersistenceMappingError for null DTO', () => {
      expect(() => ParticipantPresence.fromPrimitives(null as any)).toThrow(PersistenceMappingError);
    });

    it('throws PersistenceMappingError for invalid participantId', () => {
      const invalidDto = { ...validPresenceDto, participantId: 'invalid-uuid' };
      expect(() => ParticipantPresence.fromPrimitives(invalidDto)).toThrow(PersistenceMappingError);
    });

    it('throws PersistenceMappingError for invalid timestamp', () => {
      const invalidDto = { ...validPresenceDto, joinedAt: -1 };
      expect(() => ParticipantPresence.fromPrimitives(invalidDto)).toThrow(PersistenceMappingError);
    });

    it('throws PersistenceMappingError for invalid roomId', () => {
      const invalidDto = { ...validPresenceDto, currentRoomId: 'invalid-uuid' };
      expect(() => ParticipantPresence.fromPrimitives(invalidDto)).toThrow(PersistenceMappingError);
    });
  });

  describe('create', () => {
    it('creates new presence with only participantId', () => {
      const participantId = Uuid.fromPrimitives(validParticipantId);
      const presence = ParticipantPresence.create(participantId);
      
      expect(presence.participantId).toBe(participantId);
      expect(presence.isConnected).toBe(false);
      expect(presence.hasJoined).toBe(false);
      expect(presence.hasLeft).toBe(false);
      expect(presence.isInRoom).toBe(false);
      expect(presence.currentTags).toEqual([]);
    });
  });

  describe('toPrimitives', () => {
    it('roundtrip preserves data', () => {
      const original = validPresenceDto;
      const presence = ParticipantPresence.fromPrimitives(original);
      const result = presence.toPrimitives();
      
      expect(result.participantId).toBe(original.participantId);
      expect(result.joinedAt).toBe(original.joinedAt);
      expect(result.currentRoomId).toBe(original.currentRoomId);
      expect(result.tags).toEqual(original.tags);
    });

    it('omits undefined fields', () => {
      const minimalDto: ParticipantPresencePrimitives = {
        participantId: validParticipantId,
      };
      
      const presence = ParticipantPresence.fromPrimitives(minimalDto);
      const result = presence.toPrimitives();
      
      expect(result.joinedAt).toBeUndefined();
      expect(result.leftAt).toBeUndefined();
      expect(result.currentRoomId).toBeUndefined();
      expect(result.tags).toBeUndefined();
    });
  });

  describe('onJoin', () => {
    it('sets joinedAt on first join', () => {
      const participantId = Uuid.fromPrimitives(validParticipantId);
      const presence = ParticipantPresence.create(participantId);
      const at = Instant.fromPrimitives(baseTimestamp);
      
      const joinedPresence = presence.onJoin(at);
      
      expect(joinedPresence.isConnected).toBe(true);
      expect(joinedPresence.hasJoined).toBe(true);
      expect(joinedPresence.hasLeft).toBe(false);
      expect(joinedPresence.joinTime).toBe(baseTimestamp);
      expect(joinedPresence.leaveTime).toBeUndefined();
      expect(joinedPresence).not.toBe(presence); // Immutable
    });

    it('preserves original joinedAt on subsequent joins', () => {
      const presence = ParticipantPresence.fromPrimitives(validPresenceDto);
      const laterJoin = Instant.fromPrimitives(laterTimestamp);
      
      const rejoinedPresence = presence.onJoin(laterJoin);
      
      expect(rejoinedPresence.joinTime).toBe(baseTimestamp); // Original time preserved
      expect(rejoinedPresence.isConnected).toBe(true);
    });

    it('clears leftAt when rejoining', () => {
      const presence = ParticipantPresence.fromPrimitives({
        ...validPresenceDto,
        leftAt: laterTimestamp,
      });
      
      const rejoinedPresence = presence.onJoin(Instant.fromPrimitives(evenLaterTimestamp));
      
      expect(rejoinedPresence.leaveTime).toBeUndefined();
      expect(rejoinedPresence.isConnected).toBe(true);
    });

    it('throws PresenceTimeError when join time is before leave time', () => {
      const presence = ParticipantPresence.fromPrimitives({
        ...validPresenceDto,
        leftAt: laterTimestamp,
      });
      
      const invalidJoinTime = Instant.fromPrimitives(baseTimestamp); // Before leave time
      
      expect(() => presence.onJoin(invalidJoinTime)).toThrow(PresenceTimeError);
      expect(() => presence.onJoin(invalidJoinTime)).toThrow('Join time cannot be before last leave time');
    });
  });

  describe('onLeave', () => {
    it('sets leftAt and clears currentRoomId', () => {
      const presence = ParticipantPresence.fromPrimitives(validPresenceDto);
      const at = Instant.fromPrimitives(laterTimestamp);
      
      const leftPresence = presence.onLeave(at);
      
      expect(leftPresence.isConnected).toBe(false);
      expect(leftPresence.hasJoined).toBe(true);
      expect(leftPresence.hasLeft).toBe(true);
      expect(leftPresence.leaveTime).toBe(laterTimestamp);
      expect(leftPresence.isInRoom).toBe(false);
      expect(leftPresence.currentRoomId).toBeUndefined();
      expect(leftPresence).not.toBe(presence); // Immutable
    });

    it('throws PresenceTimeError when leave time is before join time', () => {
      const presence = ParticipantPresence.fromPrimitives(validPresenceDto);
      const invalidLeaveTime = Instant.fromPrimitives(baseTimestamp - 1000); // Before join time
      
      expect(() => presence.onLeave(invalidLeaveTime)).toThrow(PresenceTimeError);
      expect(() => presence.onLeave(invalidLeaveTime)).toThrow('Leave time cannot be before join time');
    });
  });

  describe('enterRoom', () => {
    it('sets currentRoomId', () => {
      const presence = ParticipantPresence.fromPrimitives(validPresenceDto);
      const newRoomId = RoomId.fromPrimitives('6ba7b810-9dad-41d1-80b4-00c04fd430c8');
      const at = Instant.fromPrimitives(laterTimestamp);

      const enteredPresence = presence.enterRoom(newRoomId, at);

      expect(enteredPresence.isInRoom).toBe(true);
      expect(enteredPresence.currentRoomId).toBe('6ba7b810-9dad-41d1-80b4-00c04fd430c8');
      expect(enteredPresence).not.toBe(presence); // Immutable
    });

    it('throws PresenceTimeError when enter time is before join time', () => {
      const presence = ParticipantPresence.fromPrimitives(validPresenceDto);
      const roomId = RoomId.fromPrimitives(validRoomId);
      const invalidEnterTime = Instant.fromPrimitives(baseTimestamp - 1000); // Before join time

      expect(() => presence.enterRoom(roomId, invalidEnterTime)).toThrow(PresenceTimeError);
      expect(() => presence.enterRoom(roomId, invalidEnterTime)).toThrow('Room enter time cannot be before join time');
    });

    it('throws PresenceTimeError when enter time is after leave time', () => {
      const presence = ParticipantPresence.fromPrimitives({
        ...validPresenceDto,
        leftAt: laterTimestamp,
      });
      const roomId = RoomId.fromPrimitives(validRoomId);
      const invalidEnterTime = Instant.fromPrimitives(evenLaterTimestamp); // After leave time

      expect(() => presence.enterRoom(roomId, invalidEnterTime)).toThrow(PresenceTimeError);
      expect(() => presence.enterRoom(roomId, invalidEnterTime)).toThrow('Room enter time cannot be after leave time');
    });
  });

  describe('exitRoom', () => {
    it('clears currentRoomId', () => {
      const presence = ParticipantPresence.fromPrimitives(validPresenceDto);
      const at = Instant.fromPrimitives(laterTimestamp);

      const exitedPresence = presence.exitRoom(at);

      expect(exitedPresence.isInRoom).toBe(false);
      expect(exitedPresence.currentRoomId).toBeUndefined();
      expect(exitedPresence).not.toBe(presence); // Immutable
    });

    it('throws PresenceTimeError when exit time is before join time', () => {
      const presence = ParticipantPresence.fromPrimitives(validPresenceDto);
      const invalidExitTime = Instant.fromPrimitives(baseTimestamp - 1000); // Before join time

      expect(() => presence.exitRoom(invalidExitTime)).toThrow(PresenceTimeError);
      expect(() => presence.exitRoom(invalidExitTime)).toThrow('Room exit time cannot be before join time');
    });

    it('throws PresenceTimeError when exit time is after leave time', () => {
      const presence = ParticipantPresence.fromPrimitives({
        ...validPresenceDto,
        leftAt: laterTimestamp,
      });
      const invalidExitTime = Instant.fromPrimitives(evenLaterTimestamp); // After leave time

      expect(() => presence.exitRoom(invalidExitTime)).toThrow(PresenceTimeError);
      expect(() => presence.exitRoom(invalidExitTime)).toThrow('Room exit time cannot be after leave time');
    });
  });

  describe('updateTags', () => {
    it('updates tags with deduplication and sorting', () => {
      const presence = ParticipantPresence.fromPrimitives(validPresenceDto);
      const newTags = ['tag3', 'tag1', 'tag3', 'tag4']; // Duplicates and unsorted

      const updatedPresence = presence.updateTags(newTags);

      expect(updatedPresence.currentTags).toEqual(['tag1', 'tag3', 'tag4']);
      expect(updatedPresence).not.toBe(presence); // Immutable
    });

    it('returns same instance when tags unchanged', () => {
      const presence = ParticipantPresence.fromPrimitives(validPresenceDto);
      const sameTags = ['tag1', 'tag2']; // Same as current

      const updatedPresence = presence.updateTags(sameTags);

      expect(updatedPresence).toBe(presence); // Same instance when no change
    });

    it('handles empty tags', () => {
      const presence = ParticipantPresence.fromPrimitives(validPresenceDto);
      const emptyTags: string[] = [];

      const updatedPresence = presence.updateTags(emptyTags);

      expect(updatedPresence.currentTags).toEqual([]);
      expect(updatedPresence).not.toBe(presence); // Different instance
    });
  });

  describe('state getters', () => {
    it('isConnected returns correct state', () => {
      const notJoined = ParticipantPresence.create(Uuid.fromPrimitives(validParticipantId));
      expect(notJoined.isConnected).toBe(false);

      const joined = ParticipantPresence.fromPrimitives(validPresenceDto);
      expect(joined.isConnected).toBe(true);

      const left = ParticipantPresence.fromPrimitives({
        ...validPresenceDto,
        leftAt: laterTimestamp,
      });
      expect(left.isConnected).toBe(false);
    });

    it('hasJoined returns correct state', () => {
      const notJoined = ParticipantPresence.create(Uuid.fromPrimitives(validParticipantId));
      expect(notJoined.hasJoined).toBe(false);

      const joined = ParticipantPresence.fromPrimitives(validPresenceDto);
      expect(joined.hasJoined).toBe(true);
    });

    it('hasLeft returns correct state', () => {
      const joined = ParticipantPresence.fromPrimitives(validPresenceDto);
      expect(joined.hasLeft).toBe(false);

      const left = ParticipantPresence.fromPrimitives({
        ...validPresenceDto,
        leftAt: laterTimestamp,
      });
      expect(left.hasLeft).toBe(true);
    });

    it('isInRoom returns correct state', () => {
      const notInRoom = ParticipantPresence.fromPrimitives({
        participantId: validParticipantId,
        joinedAt: baseTimestamp,
      });
      expect(notInRoom.isInRoom).toBe(false);

      const inRoom = ParticipantPresence.fromPrimitives(validPresenceDto);
      expect(inRoom.isInRoom).toBe(true);
    });
  });

  describe('error context preservation', () => {
    it('preserves context in PresenceTimeError', () => {
      const presence = ParticipantPresence.fromPrimitives(validPresenceDto);
      const invalidLeaveTime = Instant.fromPrimitives(baseTimestamp - 1000);

      try {
        presence.onLeave(invalidLeaveTime);
      } catch (error) {
        expect(error).toBeInstanceOf(PresenceTimeError);
        expect((error as PresenceTimeError).code).toBe(ERR_ENTITY_PRESENCE_TIME);
        expect((error as PresenceTimeError).ctx?.leaveTime).toBe(baseTimestamp - 1000);
        expect((error as PresenceTimeError).ctx?.joinTime).toBe(baseTimestamp);
        expect((error as PresenceTimeError).ctx?.participantId).toBe(validParticipantId);
      }
    });
  });
});
