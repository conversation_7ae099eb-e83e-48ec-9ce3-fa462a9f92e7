/* eslint-disable @typescript-eslint/no-explicit-any */
import { Seat } from '../seat.entity';
import { InvalidSeatError, SeatStateError, ERR_ENTITY_SEAT_INVALID, ERR_ENTITY_SEAT_STATE } from '../seat.errors';
import { PersistenceMappingError } from '../../../errors/persistence-mapping-error';
import { Uuid } from '../../../primitives/uuid/uuid.primitive';
import { Instant } from '../../../primitives/instant/instant.primitive';
import { DisconnectionPolicy } from '../../../policies/disconnection/disconnection-policy.vo';
import { SeatPrimitives } from '../contracts/seat.type';
import { SeatState } from '../contracts/seat-state.enum';

describe('Seat', () => {
  const validSeatNo = 0;
  const validParticipantId = '550e8400-e29b-41d4-a716-************';
  const validTimestamp = 1640995200000; // 2022-01-01 00:00:00 UTC
  const laterTimestamp = 1640995260000; // 1 minute later

  const validEmptySeatDto: SeatPrimitives = {
    seatNo: validSeatNo,
    state: SeatState.EMPTY,
  };

  const validOccupiedSeatDto: SeatPrimitives = {
    seatNo: validSeatNo,
    state: SeatState.OCCUPIED,
    participantId: validParticipantId,
    assignedAt: validTimestamp,
  };

  const validReservedSeatDto: SeatPrimitives = {
    seatNo: validSeatNo,
    state: SeatState.RESERVED_FOR_RECONNECT,
    participantId: validParticipantId,
    reservedSince: validTimestamp,
    assignedAt: validTimestamp - 60000, // Assigned 1 minute before reservation
  };

  describe('fromPrimitives', () => {
    it('creates empty seat from valid DTO', () => {
      const seat = Seat.fromPrimitives(validEmptySeatDto);
      
      expect(seat).toBeInstanceOf(Seat);
      expect(seat.currentState).toBe(SeatState.EMPTY);
      expect(seat.isEmpty).toBe(true);
      expect(seat.isOccupied).toBe(false);
      expect(seat.isReservedForReconnect).toBe(false);
      expect(seat.currentParticipantId).toBeUndefined();
    });

    it('creates occupied seat from valid DTO', () => {
      const seat = Seat.fromPrimitives(validOccupiedSeatDto);
      
      expect(seat).toBeInstanceOf(Seat);
      expect(seat.currentState).toBe(SeatState.OCCUPIED);
      expect(seat.isEmpty).toBe(false);
      expect(seat.isOccupied).toBe(true);
      expect(seat.isReservedForReconnect).toBe(false);
      expect(seat.currentParticipantId).toBe(validParticipantId);
    });

    it('creates reserved seat from valid DTO', () => {
      const seat = Seat.fromPrimitives(validReservedSeatDto);
      
      expect(seat).toBeInstanceOf(Seat);
      expect(seat.currentState).toBe(SeatState.RESERVED_FOR_RECONNECT);
      expect(seat.isEmpty).toBe(false);
      expect(seat.isOccupied).toBe(false);
      expect(seat.isReservedForReconnect).toBe(true);
      expect(seat.currentParticipantId).toBe(validParticipantId);
    });

    it('throws PersistenceMappingError for null DTO', () => {
      expect(() => Seat.fromPrimitives(null as any)).toThrow(PersistenceMappingError);
    });

    it('throws PersistenceMappingError for invalid state', () => {
      const invalidDto = { ...validEmptySeatDto, state: 'INVALID_STATE' as any };
      expect(() => Seat.fromPrimitives(invalidDto)).toThrow(PersistenceMappingError);
    });

    it('throws PersistenceMappingError for invalid seatNo', () => {
      const invalidDto = { ...validEmptySeatDto, seatNo: -1 };
      expect(() => Seat.fromPrimitives(invalidDto)).toThrow(PersistenceMappingError);
    });

    it('throws PersistenceMappingError for invalid participantId', () => {
      const invalidDto = { ...validOccupiedSeatDto, participantId: 'invalid-uuid' };
      expect(() => Seat.fromPrimitives(invalidDto)).toThrow(PersistenceMappingError);
    });
  });

  describe('toPrimitives', () => {
    it('roundtrip preserves empty seat data', () => {
      const original = validEmptySeatDto;
      const seat = Seat.fromPrimitives(original);
      const result = seat.toPrimitives();
      
      expect(result.seatNo).toBe(original.seatNo);
      expect(result.state).toBe(original.state);
      expect(result.participantId).toBeUndefined();
      expect(result.reservedSince).toBeUndefined();
      expect(result.assignedAt).toBeUndefined();
      expect(result.releasedAt).toBeUndefined();
    });

    it('roundtrip preserves occupied seat data', () => {
      const original = validOccupiedSeatDto;
      const seat = Seat.fromPrimitives(original);
      const result = seat.toPrimitives();
      
      expect(result.seatNo).toBe(original.seatNo);
      expect(result.state).toBe(original.state);
      expect(result.participantId).toBe(original.participantId);
      expect(result.assignedAt).toBe(original.assignedAt);
    });

    it('roundtrip preserves reserved seat data', () => {
      const original = validReservedSeatDto;
      const seat = Seat.fromPrimitives(original);
      const result = seat.toPrimitives();
      
      expect(result.seatNo).toBe(original.seatNo);
      expect(result.state).toBe(original.state);
      expect(result.participantId).toBe(original.participantId);
      expect(result.reservedSince).toBe(original.reservedSince);
      expect(result.assignedAt).toBe(original.assignedAt);
    });
  });

  describe('assign', () => {
    it('assigns participant to empty seat', () => {
      const emptySeat = Seat.fromPrimitives(validEmptySeatDto);
      const participantId = Uuid.fromPrimitives(validParticipantId);
      const at = Instant.fromPrimitives(validTimestamp);
      
      const assignedSeat = emptySeat.assign(participantId, at);
      
      expect(assignedSeat.currentState).toBe(SeatState.OCCUPIED);
      expect(assignedSeat.currentParticipantId).toBe(validParticipantId);
      expect(assignedSeat.isOccupied).toBe(true);
      
      const primitives = assignedSeat.toPrimitives();
      expect(primitives.assignedAt).toBe(validTimestamp);
      expect(primitives.reservedSince).toBeUndefined();
      expect(primitives.releasedAt).toBeUndefined();
    });

    it('throws SeatStateError when assigning to occupied seat', () => {
      const occupiedSeat = Seat.fromPrimitives(validOccupiedSeatDto);
      const participantId = Uuid.fromPrimitives(validParticipantId);
      const at = Instant.fromPrimitives(validTimestamp);
      
      expect(() => occupiedSeat.assign(participantId, at)).toThrow(SeatStateError);
      expect(() => occupiedSeat.assign(participantId, at)).toThrow('Cannot assign participant to non-empty seat');
    });

    it('throws SeatStateError when assigning to reserved seat', () => {
      const reservedSeat = Seat.fromPrimitives(validReservedSeatDto);
      const participantId = Uuid.fromPrimitives(validParticipantId);
      const at = Instant.fromPrimitives(validTimestamp);
      
      expect(() => reservedSeat.assign(participantId, at)).toThrow(SeatStateError);
    });
  });

  describe('release', () => {
    it('releases occupied seat', () => {
      const occupiedSeat = Seat.fromPrimitives(validOccupiedSeatDto);
      const at = Instant.fromPrimitives(laterTimestamp);
      
      const releasedSeat = occupiedSeat.release(at);
      
      expect(releasedSeat.currentState).toBe(SeatState.EMPTY);
      expect(releasedSeat.currentParticipantId).toBeUndefined();
      expect(releasedSeat.isEmpty).toBe(true);
      
      const primitives = releasedSeat.toPrimitives();
      expect(primitives.releasedAt).toBe(laterTimestamp);
      expect(primitives.participantId).toBeUndefined();
      expect(primitives.reservedSince).toBeUndefined();
      expect(primitives.assignedAt).toBe(validOccupiedSeatDto.assignedAt); // Preserved
    });

    it('throws SeatStateError when releasing empty seat', () => {
      const emptySeat = Seat.fromPrimitives(validEmptySeatDto);
      const at = Instant.fromPrimitives(validTimestamp);
      
      expect(() => emptySeat.release(at)).toThrow(SeatStateError);
      expect(() => emptySeat.release(at)).toThrow('Cannot release non-occupied seat');
    });

    it('throws SeatStateError when releasing reserved seat', () => {
      const reservedSeat = Seat.fromPrimitives(validReservedSeatDto);
      const at = Instant.fromPrimitives(validTimestamp);
      
      expect(() => reservedSeat.release(at)).toThrow(SeatStateError);
    });
  });

  describe('reserveForReconnect', () => {
    it('reserves occupied seat for reconnect', () => {
      const occupiedSeat = Seat.fromPrimitives(validOccupiedSeatDto);
      const disconnectedAt = Instant.fromPrimitives(laterTimestamp);

      const reservedSeat = occupiedSeat.reserveForReconnect(disconnectedAt);

      expect(reservedSeat.currentState).toBe(SeatState.RESERVED_FOR_RECONNECT);
      expect(reservedSeat.currentParticipantId).toBe(validParticipantId);
      expect(reservedSeat.isReservedForReconnect).toBe(true);

      const primitives = reservedSeat.toPrimitives();
      expect(primitives.reservedSince).toBe(laterTimestamp);
      expect(primitives.participantId).toBe(validParticipantId);
      expect(primitives.assignedAt).toBe(validOccupiedSeatDto.assignedAt); // Preserved
    });

    it('throws SeatStateError when reserving empty seat', () => {
      const emptySeat = Seat.fromPrimitives(validEmptySeatDto);
      const disconnectedAt = Instant.fromPrimitives(validTimestamp);

      expect(() => emptySeat.reserveForReconnect(disconnectedAt)).toThrow(SeatStateError);
      expect(() => emptySeat.reserveForReconnect(disconnectedAt)).toThrow('Cannot reserve seat for reconnect unless currently occupied');
    });

    it('throws SeatStateError when reserving already reserved seat', () => {
      const reservedSeat = Seat.fromPrimitives(validReservedSeatDto);
      const disconnectedAt = Instant.fromPrimitives(validTimestamp);

      expect(() => reservedSeat.reserveForReconnect(disconnectedAt)).toThrow(SeatStateError);
    });
  });

  describe('restoreAfterReconnect', () => {
    const policy = DisconnectionPolicy.fromPrimitives({ holdSeatForMs: 120000 }); // 2 minutes
    const reconnectTimestamp = validTimestamp + 60000; // 1 minute after reservation

    it('restores reserved seat within policy window', () => {
      const reservedSeat = Seat.fromPrimitives(validReservedSeatDto);
      const participantId = Uuid.fromPrimitives(validParticipantId);
      const at = Instant.fromPrimitives(reconnectTimestamp);

      const restoredSeat = reservedSeat.restoreAfterReconnect(participantId, at, policy);

      expect(restoredSeat.currentState).toBe(SeatState.OCCUPIED);
      expect(restoredSeat.currentParticipantId).toBe(validParticipantId);
      expect(restoredSeat.isOccupied).toBe(true);

      const primitives = restoredSeat.toPrimitives();
      expect(primitives.reservedSince).toBeUndefined(); // Cleared
      expect(primitives.participantId).toBe(validParticipantId);
      expect(primitives.assignedAt).toBe(validReservedSeatDto.assignedAt); // Preserved
    });

    it('throws SeatStateError when restoring non-reserved seat', () => {
      const occupiedSeat = Seat.fromPrimitives(validOccupiedSeatDto);
      const participantId = Uuid.fromPrimitives(validParticipantId);
      const at = Instant.fromPrimitives(validTimestamp);

      expect(() => occupiedSeat.restoreAfterReconnect(participantId, at, policy)).toThrow(SeatStateError);
      expect(() => occupiedSeat.restoreAfterReconnect(participantId, at, policy)).toThrow('Cannot restore seat unless reserved for reconnect');
    });

    it('throws InvalidSeatError when participant ID does not match', () => {
      const reservedSeat = Seat.fromPrimitives(validReservedSeatDto);
      const wrongParticipantId = Uuid.fromPrimitives('f47ac10b-58cc-4372-a567-0e02b2c3d479');
      const at = Instant.fromPrimitives(reconnectTimestamp);

      expect(() => reservedSeat.restoreAfterReconnect(wrongParticipantId, at, policy)).toThrow(InvalidSeatError);
      expect(() => reservedSeat.restoreAfterReconnect(wrongParticipantId, at, policy)).toThrow('Participant ID does not match reserved seat');
    });

    it('throws SeatStateError when reconnecting past deadline', () => {
      const reservedSeat = Seat.fromPrimitives(validReservedSeatDto);
      const participantId = Uuid.fromPrimitives(validParticipantId);
      const pastDeadline = validTimestamp + 180000; // 3 minutes after reservation (past 2-minute policy)
      const at = Instant.fromPrimitives(pastDeadline);

      expect(() => reservedSeat.restoreAfterReconnect(participantId, at, policy)).toThrow(SeatStateError);
      expect(() => reservedSeat.restoreAfterReconnect(participantId, at, policy)).toThrow('Reconnection attempt past deadline');
    });
  });

  describe('error context preservation', () => {
    it('preserves context in SeatStateError', () => {
      const occupiedSeat = Seat.fromPrimitives(validOccupiedSeatDto);
      const participantId = Uuid.fromPrimitives(validParticipantId);
      const at = Instant.fromPrimitives(validTimestamp);

      try {
        occupiedSeat.assign(participantId, at);
      } catch (error) {
        expect(error).toBeInstanceOf(SeatStateError);
        expect((error as SeatStateError).code).toBe(ERR_ENTITY_SEAT_STATE);
        expect((error as SeatStateError).ctx?.currentState).toBe('OCCUPIED');
        expect((error as SeatStateError).ctx?.seatNo).toBe(validSeatNo);
      }
    });

    it('preserves context in InvalidSeatError', () => {
      const reservedSeat = Seat.fromPrimitives(validReservedSeatDto);
      const wrongParticipantId = Uuid.fromPrimitives('f47ac10b-58cc-4372-a567-0e02b2c3d479');
      const at = Instant.fromPrimitives(validTimestamp + 60000);
      const policy = DisconnectionPolicy.fromPrimitives({ holdSeatForMs: 120000 });

      try {
        reservedSeat.restoreAfterReconnect(wrongParticipantId, at, policy);
      } catch (error) {
        expect(error).toBeInstanceOf(InvalidSeatError);
        expect((error as InvalidSeatError).code).toBe(ERR_ENTITY_SEAT_INVALID);
        expect((error as InvalidSeatError).ctx?.expectedParticipantId).toBe(validParticipantId);
        expect((error as InvalidSeatError).ctx?.providedParticipantId).toBe('f47ac10b-58cc-4372-a567-0e02b2c3d479');
        expect((error as InvalidSeatError).ctx?.seatNo).toBe(validSeatNo);
      }
    });
  });
});
