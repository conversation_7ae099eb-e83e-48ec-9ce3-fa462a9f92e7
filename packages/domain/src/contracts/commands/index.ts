/**
 * Domain Commands - Public API exports
 * 
 * This module exports all domain command types and utilities.
 * These are payload-only types with no infrastructure concerns.
 */

export type {
  // Session Lifecycle Commands
  OpenSessionCommand,
  StartSessionCommand,
  PauseSessionCommand,
  ResumeSessionCommand,
  CompleteSessionCommand,
  CancelSessionCommand,
  
  // Participant Management Commands
  JoinParticipantCommand,
  LeaveParticipantCommand,
  SeatNewParticipantCommand,
  HandleParticipantDisconnectCommand,
  RestoreReservedSeatCommand,
  PlaceLateJoinerCommand,
  
  // Room Management Commands
  CreateRoomCommand,
  AssignParticipantToRoomCommand,
  CreateOrFillBreakoutRoomCommand,
  MoveParticipantBetweenRoomsCommand,
  MakeRoomReadyCommand,
  MakeRoomClosedCommand,
  
  // Room Rebalancing Commands
  RebalanceRoomsSoftCommand,
  RebalanceRoomsHardCommand,
  ReturnAllParticipantsToMainCommand,
  
  // Round Management Commands
  StartRoundCommand,
  EndCurrentRoundCommand,
  CloseCurrentRoundCommand,
  
  // Room-Level Commands
  AssignParticipantCommand,
  ReleaseSeatByParticipantCommand,
  ReserveSeatForReconnectCommand,
  RestoreSeatAfterReconnectCommand,
  MakeRoomReadyDirectCommand,
  MakeRoomClosedDirectCommand,
  
  // Union and Utility Types
  DomainCommand,
  CommandKind,
  CommandPayload
} from './commands';

export {
  // Type Guards and Utilities
  isDomainCommand,
  isCommandOfKind,
  createCommand
} from './commands';
