/**
 * Domain Command Intents - Type definitions for all domain commands in the system
 * 
 * These are payload-only types with no infrastructure concerns.
 * Each command has a discriminated union structure with:
 * - kind: string literal for command type identification
 * - version: number for forward compatibility
 * - payload: command-specific data using primitives and value object IDs
 */

// ============================================================================
// Session Lifecycle Commands
// ============================================================================

export interface OpenSessionCommand {
  readonly kind: 'OpenSession';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
    readonly at: number; // Instant primitive
  };
}

export interface StartSessionCommand {
  readonly kind: 'StartSession';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
    readonly at: number; // Instant primitive
  };
}

export interface PauseSessionCommand {
  readonly kind: 'PauseSession';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
  };
}

export interface ResumeSessionCommand {
  readonly kind: 'ResumeSession';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
  };
}

export interface CompleteSessionCommand {
  readonly kind: 'CompleteSession';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
  };
}

export interface CancelSessionCommand {
  readonly kind: 'CancelSession';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
  };
}

// ============================================================================
// Participant Management Commands
// ============================================================================

export interface JoinParticipantCommand {
  readonly kind: 'JoinParticipant';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
    readonly participantId: string; // Uuid primitive
    readonly at: number; // Instant primitive
  };
}

export interface LeaveParticipantCommand {
  readonly kind: 'LeaveParticipant';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
    readonly participantId: string; // Uuid primitive
    readonly at: number; // Instant primitive
  };
}

export interface SeatNewParticipantCommand {
  readonly kind: 'SeatNewParticipant';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
    readonly participantId: string; // Uuid primitive
    readonly at: number; // Instant primitive
  };
}

export interface HandleParticipantDisconnectCommand {
  readonly kind: 'HandleParticipantDisconnect';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
    readonly participantId: string; // Uuid primitive
    readonly at: number; // Instant primitive
  };
}

export interface RestoreReservedSeatCommand {
  readonly kind: 'RestoreReservedSeat';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
    readonly participantId: string; // Uuid primitive
    readonly at: number; // Instant primitive
  };
}

export interface PlaceLateJoinerCommand {
  readonly kind: 'PlaceLateJoiner';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
    readonly participantId: string; // Uuid primitive
    readonly at: number; // Instant primitive
  };
}

// ============================================================================
// Room Management Commands
// ============================================================================

export interface CreateRoomCommand {
  readonly kind: 'CreateRoom';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
    readonly roomId: string; // Uuid primitive
    readonly at: number; // Instant primitive
  };
}

export interface AssignParticipantToRoomCommand {
  readonly kind: 'AssignParticipantToRoom';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
    readonly participantId: string; // Uuid primitive
    readonly roomId: string; // Uuid primitive
    readonly at: number; // Instant primitive
  };
}

export interface CreateOrFillBreakoutRoomCommand {
  readonly kind: 'CreateOrFillBreakoutRoom';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
    readonly at: number; // Instant primitive
  };
}

export interface MoveParticipantBetweenRoomsCommand {
  readonly kind: 'MoveParticipantBetweenRooms';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
    readonly participantId: string; // Uuid primitive
    readonly fromRoomId: string; // Uuid primitive
    readonly toRoomId: string; // Uuid primitive
    readonly at: number; // Instant primitive
  };
}

export interface MakeRoomReadyCommand {
  readonly kind: 'MakeRoomReady';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
    readonly roomId: string; // Uuid primitive
    readonly at: number; // Instant primitive
  };
}

export interface MakeRoomClosedCommand {
  readonly kind: 'MakeRoomClosed';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
    readonly roomId: string; // Uuid primitive
    readonly at: number; // Instant primitive
  };
}

// ============================================================================
// Room Rebalancing Commands
// ============================================================================

export interface RebalanceRoomsSoftCommand {
  readonly kind: 'RebalanceRoomsSoft';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
    readonly at: number; // Instant primitive
  };
}

export interface RebalanceRoomsHardCommand {
  readonly kind: 'RebalanceRoomsHard';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
    readonly at: number; // Instant primitive
  };
}

export interface ReturnAllParticipantsToMainCommand {
  readonly kind: 'ReturnAllParticipantsToMain';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
    readonly at: number; // Instant primitive
  };
}

// ============================================================================
// Round Management Commands
// ============================================================================

export interface StartRoundCommand {
  readonly kind: 'StartRound';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
    readonly at: number; // Instant primitive
  };
}

export interface EndCurrentRoundCommand {
  readonly kind: 'EndCurrentRound';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
    readonly at: number; // Instant primitive
  };
}

export interface CloseCurrentRoundCommand {
  readonly kind: 'CloseCurrentRound';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
    readonly at: number; // Instant primitive
  };
}

// ============================================================================
// Room-Level Commands (for Room aggregate)
// ============================================================================

export interface AssignParticipantCommand {
  readonly kind: 'AssignParticipant';
  readonly version: 1;
  readonly payload: {
    readonly roomId: string; // Uuid primitive
    readonly participantId: string; // Uuid primitive
    readonly at: number; // Instant primitive
  };
}

export interface ReleaseSeatByParticipantCommand {
  readonly kind: 'ReleaseSeatByParticipant';
  readonly version: 1;
  readonly payload: {
    readonly roomId: string; // Uuid primitive
    readonly participantId: string; // Uuid primitive
    readonly at: number; // Instant primitive
  };
}

export interface ReserveSeatForReconnectCommand {
  readonly kind: 'ReserveSeatForReconnect';
  readonly version: 1;
  readonly payload: {
    readonly roomId: string; // Uuid primitive
    readonly participantId: string; // Uuid primitive
    readonly at: number; // Instant primitive
  };
}

export interface RestoreSeatAfterReconnectCommand {
  readonly kind: 'RestoreSeatAfterReconnect';
  readonly version: 1;
  readonly payload: {
    readonly roomId: string; // Uuid primitive
    readonly participantId: string; // Uuid primitive
    readonly at: number; // Instant primitive
  };
}

export interface MakeRoomReadyDirectCommand {
  readonly kind: 'MakeRoomReadyDirect';
  readonly version: 1;
  readonly payload: {
    readonly roomId: string; // Uuid primitive
  };
}

export interface MakeRoomClosedDirectCommand {
  readonly kind: 'MakeRoomClosedDirect';
  readonly version: 1;
  readonly payload: {
    readonly roomId: string; // Uuid primitive
  };
}

// ============================================================================
// Union Type for All Domain Commands
// ============================================================================

export type DomainCommand =
  // Session Lifecycle
  | OpenSessionCommand
  | StartSessionCommand
  | PauseSessionCommand
  | ResumeSessionCommand
  | CompleteSessionCommand
  | CancelSessionCommand
  // Participant Management
  | JoinParticipantCommand
  | LeaveParticipantCommand
  | SeatNewParticipantCommand
  | HandleParticipantDisconnectCommand
  | RestoreReservedSeatCommand
  | PlaceLateJoinerCommand
  // Room Management
  | CreateRoomCommand
  | AssignParticipantToRoomCommand
  | CreateOrFillBreakoutRoomCommand
  | MoveParticipantBetweenRoomsCommand
  | MakeRoomReadyCommand
  | MakeRoomClosedCommand
  // Room Rebalancing
  | RebalanceRoomsSoftCommand
  | RebalanceRoomsHardCommand
  | ReturnAllParticipantsToMainCommand
  // Round Management
  | StartRoundCommand
  | EndCurrentRoundCommand
  | CloseCurrentRoundCommand
  // Room-Level Commands
  | AssignParticipantCommand
  | ReleaseSeatByParticipantCommand
  | ReserveSeatForReconnectCommand
  | RestoreSeatAfterReconnectCommand
  | MakeRoomReadyDirectCommand
  | MakeRoomClosedDirectCommand;

// ============================================================================
// Utility Types
// ============================================================================

/**
 * Extract the command kind from a domain command type
 */
export type CommandKind = DomainCommand['kind'];

/**
 * Extract the payload type for a specific command kind
 */
export type CommandPayload<K extends CommandKind> = Extract<DomainCommand, { kind: K }>['payload'];

// ============================================================================
// Type Guards and Utilities
// ============================================================================

/**
 * Type guard to check if an object is a valid domain command
 */
export function isDomainCommand(obj: unknown): obj is DomainCommand {
  if (typeof obj !== 'object' || obj === null) {
    return false;
  }

  const candidate = obj as Record<string, unknown>;

  // Check required structure
  if (
    typeof candidate.kind !== 'string' ||
    typeof candidate.version !== 'number' ||
    typeof candidate.payload !== 'object' ||
    candidate.payload === null
  ) {
    return false;
  }

  // Check version is 1 (current version)
  if (candidate.version !== 1) {
    return false;
  }

  // Check if kind is a valid command kind
  const validKinds: CommandKind[] = [
    // Session Lifecycle
    'OpenSession', 'StartSession', 'PauseSession', 'ResumeSession', 'CompleteSession', 'CancelSession',
    // Participant Management
    'JoinParticipant', 'LeaveParticipant', 'SeatNewParticipant', 'HandleParticipantDisconnect',
    'RestoreReservedSeat', 'PlaceLateJoiner',
    // Room Management
    'CreateRoom', 'AssignParticipantToRoom', 'CreateOrFillBreakoutRoom', 'MoveParticipantBetweenRooms',
    'MakeRoomReady', 'MakeRoomClosed',
    // Room Rebalancing
    'RebalanceRoomsSoft', 'RebalanceRoomsHard', 'ReturnAllParticipantsToMain',
    // Round Management
    'StartRound', 'EndCurrentRound', 'CloseCurrentRound',
    // Room-Level Commands
    'AssignParticipant', 'ReleaseSeatByParticipant', 'ReserveSeatForReconnect',
    'RestoreSeatAfterReconnect', 'MakeRoomReadyDirect', 'MakeRoomClosedDirect'
  ];

  return validKinds.includes(candidate.kind as CommandKind);
}

/**
 * Type guard to check if a command is of a specific kind
 */
export function isCommandOfKind<K extends CommandKind>(
  command: DomainCommand,
  kind: K
): command is Extract<DomainCommand, { kind: K }> {
  return command.kind === kind;
}

/**
 * Helper function to create a command with proper typing
 */
export function createCommand<K extends CommandKind>(
  kind: K,
  payload: CommandPayload<K>
): Extract<DomainCommand, { kind: K }> {
  return {
    kind,
    version: 1,
    payload
  } as Extract<DomainCommand, { kind: K }>;
}
