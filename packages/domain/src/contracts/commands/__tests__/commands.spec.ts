import {
  DomainCommand,
  CommandKind,
  CommandPayload,
  isDomainCommand,
  isCommandOfKind,
  createCommand,
  OpenSessionCommand,
  StartSessionCommand,
  JoinParticipantCommand,
  MoveParticipantBetweenRoomsCommand,
  AssignParticipantCommand
} from '../commands';

describe('Domain Commands', () => {
  describe('Type-level tests', () => {
    it('should have correct command kinds', () => {
      // This test ensures the CommandKind type includes all expected values
      const kinds: CommandKind[] = [
        'OpenSession',
        'StartSession',
        'JoinParticipant',
        'MoveParticipantBetweenRooms',
        'AssignParticipant'
      ];

      // TypeScript compilation ensures these are valid
      expect(kinds).toHaveLength(5);
    });

    it('should extract correct payload types', () => {
      // Test payload type extraction
      type OpenSessionPayload = CommandPayload<'OpenSession'>;
      type JoinParticipantPayload = CommandPayload<'JoinParticipant'>;

      const openPayload: OpenSessionPayload = {
        sessionId: '550e8400-e29b-41d4-a716-446655440000',
        at: 1640995200000
      };

      const joinPayload: JoinParticipantPayload = {
        sessionId: '550e8400-e29b-41d4-a716-446655440000',
        participantId: '6ba7b810-9dad-41d1-80b4-00c04fd430c8',
        at: 1640995200000
      };

      expect(openPayload.sessionId).toBe('550e8400-e29b-41d4-a716-446655440000');
      expect(joinPayload.participantId).toBe('6ba7b810-9dad-41d1-80b4-00c04fd430c8');
    });
  });

  describe('isDomainCommand', () => {
    it('should return true for valid domain commands', () => {
      const validCommand: OpenSessionCommand = {
        kind: 'OpenSession',
        version: 1,
        payload: {
          sessionId: '550e8400-e29b-41d4-a716-446655440000',
          at: 1640995200000
        }
      };

      expect(isDomainCommand(validCommand)).toBe(true);
    });

    it('should return false for null or undefined', () => {
      expect(isDomainCommand(null)).toBe(false);
      expect(isDomainCommand(undefined)).toBe(false);
    });

    it('should return false for non-objects', () => {
      expect(isDomainCommand('string')).toBe(false);
      expect(isDomainCommand(123)).toBe(false);
      expect(isDomainCommand(true)).toBe(false);
    });

    it('should return false for objects missing required fields', () => {
      expect(isDomainCommand({})).toBe(false);
      expect(isDomainCommand({ kind: 'OpenSession' })).toBe(false);
      expect(isDomainCommand({ kind: 'OpenSession', version: 1 })).toBe(false);
    });

    it('should return false for invalid field types', () => {
      expect(isDomainCommand({
        kind: 123, // should be string
        version: 1,
        payload: {}
      })).toBe(false);

      expect(isDomainCommand({
        kind: 'OpenSession',
        version: '1', // should be number
        payload: {}
      })).toBe(false);

      expect(isDomainCommand({
        kind: 'OpenSession',
        version: 1,
        payload: null // should be object
      })).toBe(false);
    });

    it('should return false for invalid version', () => {
      expect(isDomainCommand({
        kind: 'OpenSession',
        version: 2, // only version 1 is supported
        payload: {}
      })).toBe(false);
    });

    it('should return false for invalid command kind', () => {
      expect(isDomainCommand({
        kind: 'InvalidCommand',
        version: 1,
        payload: {}
      })).toBe(false);
    });
  });

  describe('isCommandOfKind', () => {
    const openCommand: OpenSessionCommand = {
      kind: 'OpenSession',
      version: 1,
      payload: {
        sessionId: '550e8400-e29b-41d4-a716-446655440000',
        at: 1640995200000
      }
    };

    const joinCommand: JoinParticipantCommand = {
      kind: 'JoinParticipant',
      version: 1,
      payload: {
        sessionId: '550e8400-e29b-41d4-a716-446655440000',
        participantId: '6ba7b810-9dad-41d1-80b4-00c04fd430c8',
        at: 1640995200000
      }
    };

    it('should return true for matching command kind', () => {
      expect(isCommandOfKind(openCommand, 'OpenSession')).toBe(true);
      expect(isCommandOfKind(joinCommand, 'JoinParticipant')).toBe(true);
    });

    it('should return false for non-matching command kind', () => {
      expect(isCommandOfKind(openCommand, 'JoinParticipant')).toBe(false);
      expect(isCommandOfKind(joinCommand, 'OpenSession')).toBe(false);
    });

    it('should provide correct type narrowing', () => {
      const command: DomainCommand = openCommand;

      if (isCommandOfKind(command, 'OpenSession')) {
        // TypeScript should narrow the type here
        expect(command.payload.sessionId).toBe('550e8400-e29b-41d4-a716-446655440000');
        expect(command.payload.at).toBe(1640995200000);
        // participantId should not exist on OpenSession payload
      }
    });
  });

  describe('createCommand', () => {
    it('should create valid commands with proper typing', () => {
      const openCommand = createCommand('OpenSession', {
        sessionId: '550e8400-e29b-41d4-a716-446655440000',
        at: 1640995200000
      });

      expect(openCommand).toEqual({
        kind: 'OpenSession',
        version: 1,
        payload: {
          sessionId: '550e8400-e29b-41d4-a716-446655440000',
          at: 1640995200000
        }
      });

      expect(isDomainCommand(openCommand)).toBe(true);
      expect(isCommandOfKind(openCommand, 'OpenSession')).toBe(true);
    });

    it('should create complex commands correctly', () => {
      const moveCommand = createCommand('MoveParticipantBetweenRooms', {
        sessionId: '550e8400-e29b-41d4-a716-446655440000',
        participantId: '6ba7b810-9dad-41d1-80b4-00c04fd430c8',
        fromRoomId: '7c9e6679-7425-40de-944b-e07fc1f90ae7',
        toRoomId: '8d0f7780-8536-51ef-a55c-f18fd2f01bf8',
        at: 1640995200000
      });

      expect(moveCommand.kind).toBe('MoveParticipantBetweenRooms');
      expect(moveCommand.version).toBe(1);
      expect(moveCommand.payload.fromRoomId).toBe('7c9e6679-7425-40de-944b-e07fc1f90ae7');
      expect(moveCommand.payload.toRoomId).toBe('8d0f7780-8536-51ef-a55c-f18fd2f01bf8');
    });
  });

  describe('Exhaustive command kind handling', () => {
    it('should handle all command kinds in switch statements', () => {
      const commands: DomainCommand[] = [
        createCommand('OpenSession', { sessionId: 'test', at: 123 }),
        createCommand('StartSession', { sessionId: 'test', at: 123 }),
        createCommand('JoinParticipant', { sessionId: 'test', participantId: 'test', at: 123 }),
        createCommand('AssignParticipant', { roomId: 'test', participantId: 'test', at: 123 })
      ];

      for (const command of commands) {
        let handled = false;

        switch (command.kind) {
          case 'OpenSession':
          case 'StartSession':
          case 'PauseSession':
          case 'ResumeSession':
          case 'CompleteSession':
          case 'CancelSession':
          case 'JoinParticipant':
          case 'LeaveParticipant':
          case 'SeatNewParticipant':
          case 'HandleParticipantDisconnect':
          case 'RestoreReservedSeat':
          case 'PlaceLateJoiner':
          case 'CreateRoom':
          case 'AssignParticipantToRoom':
          case 'CreateOrFillBreakoutRoom':
          case 'MoveParticipantBetweenRooms':
          case 'MakeRoomReady':
          case 'MakeRoomClosed':
          case 'RebalanceRoomsSoft':
          case 'RebalanceRoomsHard':
          case 'ReturnAllParticipantsToMain':
          case 'StartRound':
          case 'EndCurrentRound':
          case 'CloseCurrentRound':
          case 'AssignParticipant':
          case 'ReleaseSeatByParticipant':
          case 'ReserveSeatForReconnect':
          case 'RestoreSeatAfterReconnect':
          case 'MakeRoomReadyDirect':
          case 'MakeRoomClosedDirect':
            handled = true;
            break;
          default:
            // This should cause a TypeScript error if we miss any command kinds
            const _exhaustiveCheck: never = command;
            break;
        }

        expect(handled).toBe(true);
      }
    });
  });

  describe('Command structure validation', () => {
    it('should validate all commands have consistent structure', () => {
      const sampleCommands = [
        createCommand('OpenSession', { sessionId: 'test', at: 123 }),
        createCommand('JoinParticipant', { sessionId: 'test', participantId: 'test', at: 123 }),
        createCommand('MoveParticipantBetweenRooms', {
          sessionId: 'test',
          participantId: 'test',
          fromRoomId: 'test',
          toRoomId: 'test',
          at: 123
        })
      ];

      for (const command of sampleCommands) {
        expect(command).toHaveProperty('kind');
        expect(command).toHaveProperty('version');
        expect(command).toHaveProperty('payload');
        expect(typeof command.kind).toBe('string');
        expect(command.version).toBe(1);
        expect(typeof command.payload).toBe('object');
        expect(command.payload).not.toBeNull();
      }
    });
  });
});
