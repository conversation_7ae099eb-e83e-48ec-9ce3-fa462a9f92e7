/**
 * Domain Events - Type definitions for all domain events in the system
 * 
 * These are payload-only types with no infrastructure concerns.
 * Each event has a discriminated union structure with:
 * - kind: string literal for event type identification
 * - version: number for forward compatibility
 * - payload: event-specific data using primitives and value object IDs
 */

import { Uuid } from '../../primitives/uuid/uuid.primitive';
import { Instant } from '../../primitives/instant/instant.primitive';
import { SeatNo } from '../../primitives/seat-no/seat-no.primitive';
import { PositiveInt } from '../../primitives/positive-int/positive-int.primitive';
import { NonEmptyString } from '../../primitives/non-empty-string/non-empty-string.primitive';

// ============================================================================
// Session Events
// ============================================================================

export interface SessionStartedEvent {
  readonly kind: 'SessionStarted';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
    readonly hostId: string; // Uuid primitive
    readonly startedAt: number; // Instant primitive
    readonly config: {
      readonly maxRounds: number; // PositiveInt primitive
      readonly roundDurationMs: number; // PositiveInt primitive
      readonly gracePeriodMs: number; // PositiveInt primitive
      readonly reconnectTimeoutMs: number; // PositiveInt primitive
    };
  };
}

export interface SessionEndedEvent {
  readonly kind: 'SessionEnded';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
    readonly endedAt: number; // Instant primitive
    readonly reason: 'COMPLETED' | 'HOST_ENDED' | 'TIMEOUT' | 'ERROR';
    readonly finalRoundNumber?: number; // PositiveInt primitive, optional if ended before any rounds
  };
}

// ============================================================================
// Round Events
// ============================================================================

export interface RoundStartedEvent {
  readonly kind: 'RoundStarted';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
    readonly roundId: string; // Uuid primitive
    readonly roundNumber: number; // PositiveInt primitive
    readonly startedAt: number; // Instant primitive
    readonly scheduledEndAt: number; // Instant primitive
  };
}

export interface RoundEndedEvent {
  readonly kind: 'RoundEnded';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
    readonly roundId: string; // Uuid primitive
    readonly roundNumber: number; // PositiveInt primitive
    readonly endedAt: number; // Instant primitive
    readonly reason: 'COMPLETED' | 'HOST_ENDED' | 'TIMEOUT';
  };
}

// ============================================================================
// Room Events
// ============================================================================

export interface RoomReadyEvent {
  readonly kind: 'RoomReady';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
    readonly roomId: string; // Uuid primitive
    readonly readyAt: number; // Instant primitive
    readonly participantCount: number; // PositiveInt primitive
  };
}

export interface RoomClosedEvent {
  readonly kind: 'RoomClosed';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
    readonly roomId: string; // Uuid primitive
    readonly closedAt: number; // Instant primitive
    readonly reason: 'ROUND_ENDED' | 'MANUAL' | 'EMPTY' | 'ERROR';
  };
}

// ============================================================================
// Participant Events
// ============================================================================

export interface ParticipantSeatedEvent {
  readonly kind: 'ParticipantSeated';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
    readonly roomId: string; // Uuid primitive
    readonly participantId: string; // Uuid primitive
    readonly seatNo: number; // SeatNo primitive
    readonly seatedAt: number; // Instant primitive
  };
}

export interface ParticipantMovedEvent {
  readonly kind: 'ParticipantMoved';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
    readonly fromRoomId: string; // Uuid primitive
    readonly toRoomId: string; // Uuid primitive
    readonly participantId: string; // Uuid primitive
    readonly fromSeatNo: number; // SeatNo primitive
    readonly toSeatNo: number; // SeatNo primitive
    readonly movedAt: number; // Instant primitive
    readonly reason: 'REBALANCE_SOFT' | 'REBALANCE_HARD' | 'MANUAL' | 'LATE_JOIN';
  };
}

export interface ParticipantDisconnectedEvent {
  readonly kind: 'ParticipantDisconnected';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
    readonly roomId: string; // Uuid primitive
    readonly participantId: string; // Uuid primitive
    readonly seatNo: number; // SeatNo primitive
    readonly disconnectedAt: number; // Instant primitive
    readonly reason: 'NETWORK' | 'CLIENT_CLOSED' | 'TIMEOUT' | 'ERROR';
  };
}

export interface ParticipantRestoredEvent {
  readonly kind: 'ParticipantRestored';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
    readonly roomId: string; // Uuid primitive
    readonly participantId: string; // Uuid primitive
    readonly seatNo: number; // SeatNo primitive
    readonly restoredAt: number; // Instant primitive
    readonly wasDisconnectedFor: number; // Duration in milliseconds
  };
}

// ============================================================================
// Room Rebalancing Events
// ============================================================================

export interface RoomsRebalancedSoftEvent {
  readonly kind: 'RoomsRebalancedSoft';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
    readonly rebalancedAt: number; // Instant primitive
    readonly moves: ReadonlyArray<{
      readonly participantId: string; // Uuid primitive
      readonly fromRoomId: string; // Uuid primitive
      readonly toRoomId: string; // Uuid primitive
      readonly fromSeatNo: number; // SeatNo primitive
      readonly toSeatNo: number; // SeatNo primitive
    }>;
    readonly reason: 'PARTICIPANT_LEFT' | 'ROOM_UNDERUTILIZED' | 'OPTIMIZATION';
  };
}

export interface RoomsRebalancedHardEvent {
  readonly kind: 'RoomsRebalancedHard';
  readonly version: 1;
  readonly payload: {
    readonly sessionId: string; // Uuid primitive
    readonly rebalancedAt: number; // Instant primitive
    readonly moves: ReadonlyArray<{
      readonly participantId: string; // Uuid primitive
      readonly fromRoomId: string; // Uuid primitive
      readonly toRoomId: string; // Uuid primitive
      readonly fromSeatNo: number; // SeatNo primitive
      readonly toSeatNo: number; // SeatNo primitive
    }>;
    readonly closedRooms: ReadonlyArray<string>; // Uuid primitives
    readonly reason: 'ROUND_TRANSITION' | 'MAJOR_DISCONNECTIONS' | 'MANUAL';
  };
}

// ============================================================================
// Union Type for All Domain Events
// ============================================================================

export type DomainEvent =
  | SessionStartedEvent
  | SessionEndedEvent
  | RoundStartedEvent
  | RoundEndedEvent
  | RoomReadyEvent
  | RoomClosedEvent
  | ParticipantSeatedEvent
  | ParticipantMovedEvent
  | ParticipantDisconnectedEvent
  | ParticipantRestoredEvent
  | RoomsRebalancedSoftEvent
  | RoomsRebalancedHardEvent;

// ============================================================================
// Type Utilities
// ============================================================================

/**
 * Extract event kind from a domain event type
 */
export type EventKind = DomainEvent['kind'];

/**
 * Extract payload type for a specific event kind
 */
export type EventPayload<K extends EventKind> = Extract<DomainEvent, { kind: K }>['payload'];

/**
 * Type guard to check if an object is a valid domain event
 */
export function isDomainEvent(obj: unknown): obj is DomainEvent {
  if (typeof obj !== 'object' || obj === null) {
    return false;
  }
  
  const event = obj as Record<string, unknown>;
  
  return (
    typeof event.kind === 'string' &&
    event.version === 1 &&
    typeof event.payload === 'object' &&
    event.payload !== null
  );
}

/**
 * Type guard for specific event kinds
 */
export function isEventOfKind<K extends EventKind>(
  event: DomainEvent,
  kind: K
): event is Extract<DomainEvent, { kind: K }> {
  return event.kind === kind;
}
