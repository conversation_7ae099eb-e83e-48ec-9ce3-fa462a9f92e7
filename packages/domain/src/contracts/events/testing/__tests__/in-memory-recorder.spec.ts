import { InMemoryEventRecorder } from '../in-memory-recorder';
import {
  DomainEvent,
  SessionStartedEvent,
  ParticipantSeatedEvent,
  RoomReadyEvent,
  RoundStartedEvent
} from '../../events';

describe('InMemoryEventRecorder', () => {
  let recorder: InMemoryEventRecorder;

  // Sample events for testing
  const sessionStartedEvent: SessionStartedEvent = {
    kind: 'SessionStarted',
    version: 1,
    payload: {
      sessionId: '550e8400-e29b-41d4-a716-446655440000',
      hostId: '6ba7b810-9dad-41d1-80b4-00c04fd430c8',
      startedAt: 1640995200000,
      config: {
        maxRounds: 5,
        roundDurationMs: 300000,
        gracePeriodMs: 30000,
        reconnectTimeoutMs: 60000
      }
    }
  };

  const participantSeatedEvent: ParticipantSeatedEvent = {
    kind: 'ParticipantSeated',
    version: 1,
    payload: {
      sessionId: '550e8400-e29b-41d4-a716-446655440000',
      roomId: '6ba7b810-9dad-41d1-80b4-00c04fd430c8',
      participantId: '7c9e6679-7425-40de-944b-e07fc1f90ae7',
      seatNo: 0,
      seatedAt: 1640995260000
    }
  };

  const roomReadyEvent: RoomReadyEvent = {
    kind: 'RoomReady',
    version: 1,
    payload: {
      sessionId: '550e8400-e29b-41d4-a716-446655440000',
      roomId: '6ba7b810-9dad-41d1-80b4-00c04fd430c8',
      readyAt: 1640995320000,
      participantCount: 2
    }
  };

  const roundStartedEvent: RoundStartedEvent = {
    kind: 'RoundStarted',
    version: 1,
    payload: {
      sessionId: '550e8400-e29b-41d4-a716-446655440000',
      roundId: '8d0f7780-8536-51ef-a55c-f18fd2f01bf8',
      roundNumber: 1,
      startedAt: 1640995380000,
      scheduledEndAt: 1640995680000
    }
  };

  beforeEach(() => {
    recorder = new InMemoryEventRecorder();
  });

  describe('Basic operations', () => {
    it('should start empty', () => {
      expect(recorder.all()).toEqual([]);
      expect(recorder.count).toBe(0);
      expect(recorder.isEmpty).toBe(true);
      expect(recorder.first).toBeUndefined();
      expect(recorder.last).toBeUndefined();
    });

    it('should push events and maintain order', () => {
      recorder.push(sessionStartedEvent);
      recorder.push(participantSeatedEvent);
      recorder.push(roomReadyEvent);

      const events = recorder.all();
      expect(events).toHaveLength(3);
      expect(events[0]).toEqual(sessionStartedEvent);
      expect(events[1]).toEqual(participantSeatedEvent);
      expect(events[2]).toEqual(roomReadyEvent);
    });

    it('should clear all events', () => {
      recorder.push(sessionStartedEvent);
      recorder.push(participantSeatedEvent);
      
      expect(recorder.count).toBe(2);
      expect(recorder.isEmpty).toBe(false);

      recorder.clear();

      expect(recorder.all()).toEqual([]);
      expect(recorder.count).toBe(0);
      expect(recorder.isEmpty).toBe(true);
      expect(recorder.first).toBeUndefined();
      expect(recorder.last).toBeUndefined();
    });

    it('should return a copy of events array to prevent mutation', () => {
      recorder.push(sessionStartedEvent);
      
      const events1 = recorder.all();
      const events2 = recorder.all();

      // Should be different array instances
      expect(events1).not.toBe(events2);
      
      // But with same content
      expect(events1).toEqual(events2);

      // Mutating returned array should not affect recorder
      events1.push(participantSeatedEvent);
      expect(recorder.count).toBe(1);
      expect(recorder.all()).toHaveLength(1);
    });
  });

  describe('Getters and properties', () => {
    it('should track count correctly', () => {
      expect(recorder.count).toBe(0);

      recorder.push(sessionStartedEvent);
      expect(recorder.count).toBe(1);

      recorder.push(participantSeatedEvent);
      expect(recorder.count).toBe(2);

      recorder.clear();
      expect(recorder.count).toBe(0);
    });

    it('should track isEmpty correctly', () => {
      expect(recorder.isEmpty).toBe(true);

      recorder.push(sessionStartedEvent);
      expect(recorder.isEmpty).toBe(false);

      recorder.clear();
      expect(recorder.isEmpty).toBe(true);
    });

    it('should return first and last events correctly', () => {
      // Empty recorder
      expect(recorder.first).toBeUndefined();
      expect(recorder.last).toBeUndefined();

      // Single event
      recorder.push(sessionStartedEvent);
      expect(recorder.first).toEqual(sessionStartedEvent);
      expect(recorder.last).toEqual(sessionStartedEvent);

      // Multiple events
      recorder.push(participantSeatedEvent);
      recorder.push(roomReadyEvent);
      
      expect(recorder.first).toEqual(sessionStartedEvent);
      expect(recorder.last).toEqual(roomReadyEvent);
    });
  });

  describe('Event filtering and querying', () => {
    beforeEach(() => {
      recorder.push(sessionStartedEvent);
      recorder.push(participantSeatedEvent);
      recorder.push(roomReadyEvent);
      recorder.push(roundStartedEvent);
    });

    it('should filter events by kind', () => {
      const sessionEvents = recorder.ofKind('SessionStarted');
      expect(sessionEvents).toHaveLength(1);
      expect(sessionEvents[0]).toEqual(sessionStartedEvent);

      const participantEvents = recorder.ofKind('ParticipantSeated');
      expect(participantEvents).toHaveLength(1);
      expect(participantEvents[0]).toEqual(participantSeatedEvent);

      const nonExistentEvents = recorder.ofKind('SessionEnded');
      expect(nonExistentEvents).toHaveLength(0);
    });

    it('should check if event kind exists', () => {
      expect(recorder.hasEventOfKind('SessionStarted')).toBe(true);
      expect(recorder.hasEventOfKind('ParticipantSeated')).toBe(true);
      expect(recorder.hasEventOfKind('SessionEnded')).toBe(false);
      expect(recorder.hasEventOfKind('ParticipantMoved')).toBe(false);
    });

    it('should get event kinds in order', () => {
      const kinds = recorder.getEventKinds();
      expect(kinds).toEqual([
        'SessionStarted',
        'ParticipantSeated',
        'RoomReady',
        'RoundStarted'
      ]);
    });

    it('should get events since a specific index', () => {
      const eventsSince0 = recorder.since(0);
      expect(eventsSince0).toHaveLength(4);
      expect(eventsSince0[0]).toEqual(sessionStartedEvent);

      const eventsSince1 = recorder.since(1);
      expect(eventsSince1).toHaveLength(3);
      expect(eventsSince1[0]).toEqual(participantSeatedEvent);

      const eventsSince2 = recorder.since(2);
      expect(eventsSince2).toHaveLength(2);
      expect(eventsSince2[0]).toEqual(roomReadyEvent);

      const eventsSince10 = recorder.since(10);
      expect(eventsSince10).toHaveLength(0);
    });
  });

  describe('Multiple events of same kind', () => {
    it('should handle multiple events of the same kind', () => {
      const participant1Event: ParticipantSeatedEvent = {
        kind: 'ParticipantSeated',
        version: 1,
        payload: {
          sessionId: '550e8400-e29b-41d4-a716-446655440000',
          roomId: '6ba7b810-9dad-41d1-80b4-00c04fd430c8',
          participantId: '7c9e6679-7425-40de-944b-e07fc1f90ae7',
          seatNo: 0,
          seatedAt: 1640995260000
        }
      };

      const participant2Event: ParticipantSeatedEvent = {
        kind: 'ParticipantSeated',
        version: 1,
        payload: {
          sessionId: '550e8400-e29b-41d4-a716-446655440000',
          roomId: '6ba7b810-9dad-41d1-80b4-00c04fd430c8',
          participantId: '8d0f7780-8536-51ef-a55c-f18fd2f01bf8',
          seatNo: 1,
          seatedAt: 1640995320000
        }
      };

      recorder.push(sessionStartedEvent);
      recorder.push(participant1Event);
      recorder.push(participant2Event);

      const participantEvents = recorder.ofKind('ParticipantSeated');
      expect(participantEvents).toHaveLength(2);
      expect(participantEvents[0]).toEqual(participant1Event);
      expect(participantEvents[1]).toEqual(participant2Event);

      expect(recorder.hasEventOfKind('ParticipantSeated')).toBe(true);
      expect(recorder.count).toBe(3);
    });
  });

  describe('Type safety', () => {
    it('should maintain type safety with ofKind method', () => {
      recorder.push(sessionStartedEvent);
      recorder.push(participantSeatedEvent);

      const sessionEvents = recorder.ofKind('SessionStarted');
      // TypeScript should infer the correct type
      expect(sessionEvents[0].payload.sessionId).toBe('550e8400-e29b-41d4-a716-446655440000');
      expect(sessionEvents[0].payload.startedAt).toBe(1640995200000);

      const participantEvents = recorder.ofKind('ParticipantSeated');
      // TypeScript should infer the correct type
      expect(participantEvents[0].payload.participantId).toBe('7c9e6679-7425-40de-944b-e07fc1f90ae7');
      expect(participantEvents[0].payload.seatNo).toBe(0);
    });
  });

  describe('Usage patterns for testing', () => {
    it('should support common testing patterns', () => {
      // Arrange - setup initial state
      const initialCount = recorder.count;
      
      // Act - perform operations that emit events
      recorder.push(sessionStartedEvent);
      recorder.push(participantSeatedEvent);
      
      // Assert - check events were emitted correctly
      expect(recorder.count).toBe(initialCount + 2);
      expect(recorder.hasEventOfKind('SessionStarted')).toBe(true);
      expect(recorder.hasEventOfKind('ParticipantSeated')).toBe(true);
      
      // Assert event order
      const kinds = recorder.getEventKinds();
      expect(kinds).toEqual(['SessionStarted', 'ParticipantSeated']);
      
      // Assert specific event details
      const sessionEvents = recorder.ofKind('SessionStarted');
      expect(sessionEvents[0].payload.sessionId).toBe('550e8400-e29b-41d4-a716-446655440000');
    });

    it('should support checking events emitted after a specific point', () => {
      // Record some initial events
      recorder.push(sessionStartedEvent);
      recorder.push(participantSeatedEvent);
      
      const checkpointIndex = recorder.count;
      
      // Perform more operations
      recorder.push(roomReadyEvent);
      recorder.push(roundStartedEvent);
      
      // Check only events emitted after checkpoint
      const newEvents = recorder.since(checkpointIndex);
      expect(newEvents).toHaveLength(2);
      expect(newEvents[0]).toEqual(roomReadyEvent);
      expect(newEvents[1]).toEqual(roundStartedEvent);
    });
  });
});
