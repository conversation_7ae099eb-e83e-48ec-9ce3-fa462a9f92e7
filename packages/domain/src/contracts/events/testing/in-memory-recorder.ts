import { DomainEvent } from '../events';

/**
 * In-memory event recorder for unit tests
 * 
 * This is a simple utility class that records domain events in memory
 * for testing purposes. It has zero dependencies on application infrastructure
 * and provides basic operations to push, retrieve, and clear events.
 * 
 * Usage in tests:
 * ```typescript
 * const recorder = new InMemoryEventRecorder();
 * 
 * // Record events
 * recorder.push(sessionStartedEvent);
 * recorder.push(participantSeatedEvent);
 * 
 * // Assert events
 * const events = recorder.all();
 * expect(events).toHaveLength(2);
 * expect(events[0].kind).toBe('SessionStarted');
 * 
 * // Clear for next test
 * recorder.clear();
 * ```
 */
export class InMemoryEventRecorder {
  private events: DomainEvent[] = [];

  /**
   * Push a domain event to the recorder
   * Events are stored in the order they are pushed
   */
  push(event: DomainEvent): void {
    this.events.push(event);
  }

  /**
   * Get all recorded events in the order they were pushed
   * Returns a copy of the events array to prevent external mutation
   */
  all(): DomainEvent[] {
    return [...this.events];
  }

  /**
   * Clear all recorded events
   * Useful for resetting state between tests
   */
  clear(): void {
    this.events = [];
  }

  /**
   * Get the number of recorded events
   */
  get count(): number {
    return this.events.length;
  }

  /**
   * Check if any events have been recorded
   */
  get isEmpty(): boolean {
    return this.events.length === 0;
  }

  /**
   * Get the last recorded event, or undefined if no events
   */
  get last(): DomainEvent | undefined {
    return this.events[this.events.length - 1];
  }

  /**
   * Get the first recorded event, or undefined if no events
   */
  get first(): DomainEvent | undefined {
    return this.events[0];
  }

  /**
   * Filter events by kind
   * Returns all events of the specified kind in order
   */
  ofKind<K extends DomainEvent['kind']>(kind: K): Extract<DomainEvent, { kind: K }>[] {
    return this.events.filter(event => event.kind === kind) as Extract<DomainEvent, { kind: K }>[];
  }

  /**
   * Get events starting from a specific index
   * Useful for checking events emitted after a certain point
   */
  since(index: number): DomainEvent[] {
    return this.events.slice(index);
  }

  /**
   * Check if a specific event kind was recorded
   */
  hasEventOfKind(kind: DomainEvent['kind']): boolean {
    return this.events.some(event => event.kind === kind);
  }

  /**
   * Get event kinds in the order they were recorded
   * Useful for asserting event sequences
   */
  getEventKinds(): DomainEvent['kind'][] {
    return this.events.map(event => event.kind);
  }
}
