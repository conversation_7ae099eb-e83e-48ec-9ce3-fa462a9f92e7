/**
 * Domain Events - Type and Runtime Tests
 * 
 * Tests for domain event types, type guards, and utilities.
 * Includes compile-time type checking and runtime validation.
 */

import {
  DomainEvent,
  EventKind,
  EventPayload,
  SessionStartedEvent,
  SessionEndedEvent,
  RoundStartedEvent,
  RoundEndedEvent,
  RoomReadyEvent,
  RoomClosedEvent,
  ParticipantSeatedEvent,
  ParticipantMovedEvent,
  ParticipantDisconnectedEvent,
  ParticipantRestoredEvent,
  RoomsRebalancedSoftEvent,
  RoomsRebalancedHardEvent,
  isDomainEvent,
  isEventOfKind,
} from '../events';

describe('Domain Events', () => {
  // ============================================================================
  // Type-level Tests (Compile-time)
  // ============================================================================

  describe('Type System', () => {
    it('should have exhaustive EventKind union', () => {
      // This test ensures all event kinds are included in the union
      const allEventKinds: EventKind[] = [
        'SessionStarted',
        'SessionEnded',
        'RoundStarted',
        'RoundEnded',
        'RoomReady',
        'RoomClosed',
        'ParticipantSeated',
        'ParticipantMoved',
        'ParticipantDisconnected',
        'ParticipantRestored',
        'RoomsRebalancedSoft',
        'RoomsRebalancedHard',
      ];

      // TypeScript will error if we miss any event kinds
      expect(allEventKinds).toHaveLength(12);
    });

    it('should extract correct payload types', () => {
      // Compile-time test: these should not cause TypeScript errors
      type SessionStartedPayload = EventPayload<'SessionStarted'>;
      type ParticipantSeatedPayload = EventPayload<'ParticipantSeated'>;
      
      // Runtime verification that the types are correctly structured
      const sessionPayload: SessionStartedPayload = {
        sessionId: '550e8400-e29b-41d4-a716-446655440001',
        hostId: '550e8400-e29b-41d4-a716-446655440002',
        startedAt: 1693747200000,
        config: {
          maxRounds: 5,
          roundDurationMs: 300000,
          gracePeriodMs: 30000,
          reconnectTimeoutMs: 60000,
        },
      };

      const participantPayload: ParticipantSeatedPayload = {
        sessionId: '550e8400-e29b-41d4-a716-446655440001',
        roomId: '550e8400-e29b-41d4-a716-446655440003',
        participantId: '550e8400-e29b-41d4-a716-446655440004',
        seatNo: 0,
        seatedAt: 1693747200000,
      };

      expect(sessionPayload.sessionId).toBeDefined();
      expect(participantPayload.participantId).toBeDefined();
    });
  });

  // ============================================================================
  // Runtime Tests
  // ============================================================================

  describe('isDomainEvent', () => {
    it('should return true for valid domain events', () => {
      const validEvent: SessionStartedEvent = {
        kind: 'SessionStarted',
        version: 1,
        payload: {
          sessionId: '550e8400-e29b-41d4-a716-446655440001',
          hostId: '550e8400-e29b-41d4-a716-446655440002',
          startedAt: 1693747200000,
          config: {
            maxRounds: 5,
            roundDurationMs: 300000,
            gracePeriodMs: 30000,
            reconnectTimeoutMs: 60000,
          },
        },
      };

      expect(isDomainEvent(validEvent)).toBe(true);
    });

    it('should return false for invalid objects', () => {
      expect(isDomainEvent(null)).toBe(false);
      expect(isDomainEvent(undefined)).toBe(false);
      expect(isDomainEvent('string')).toBe(false);
      expect(isDomainEvent(123)).toBe(false);
      expect(isDomainEvent({})).toBe(false);
      
      // Missing required fields
      expect(isDomainEvent({ kind: 'SessionStarted' })).toBe(false);
      expect(isDomainEvent({ kind: 'SessionStarted', version: 1 })).toBe(false);
      expect(isDomainEvent({ version: 1, payload: {} })).toBe(false);
      
      // Wrong version
      expect(isDomainEvent({
        kind: 'SessionStarted',
        version: 2,
        payload: {},
      })).toBe(false);
      
      // Null payload
      expect(isDomainEvent({
        kind: 'SessionStarted',
        version: 1,
        payload: null,
      })).toBe(false);
    });
  });

  describe('isEventOfKind', () => {
    const sessionEvent: SessionStartedEvent = {
      kind: 'SessionStarted',
      version: 1,
      payload: {
        sessionId: '550e8400-e29b-41d4-a716-446655440001',
        hostId: '550e8400-e29b-41d4-a716-446655440002',
        startedAt: 1693747200000,
        config: {
          maxRounds: 5,
          roundDurationMs: 300000,
          gracePeriodMs: 30000,
          reconnectTimeoutMs: 60000,
        },
      },
    };

    const participantEvent: ParticipantSeatedEvent = {
      kind: 'ParticipantSeated',
      version: 1,
      payload: {
        sessionId: '550e8400-e29b-41d4-a716-446655440001',
        roomId: '550e8400-e29b-41d4-a716-446655440003',
        participantId: '550e8400-e29b-41d4-a716-446655440004',
        seatNo: 0,
        seatedAt: 1693747200000,
      },
    };

    it('should return true for matching event kinds', () => {
      expect(isEventOfKind(sessionEvent, 'SessionStarted')).toBe(true);
      expect(isEventOfKind(participantEvent, 'ParticipantSeated')).toBe(true);
    });

    it('should return false for non-matching event kinds', () => {
      expect(isEventOfKind(sessionEvent, 'SessionEnded')).toBe(false);
      expect(isEventOfKind(sessionEvent, 'ParticipantSeated')).toBe(false);
      expect(isEventOfKind(participantEvent, 'SessionStarted')).toBe(false);
      expect(isEventOfKind(participantEvent, 'RoomReady')).toBe(false);
    });

    it('should provide correct type narrowing', () => {
      // This test verifies that TypeScript correctly narrows the type
      if (isEventOfKind(sessionEvent, 'SessionStarted')) {
        // TypeScript should know this is SessionStartedEvent
        expect(sessionEvent.payload.hostId).toBeDefined();
        expect(sessionEvent.payload.config.maxRounds).toBeDefined();
      }

      if (isEventOfKind(participantEvent, 'ParticipantSeated')) {
        // TypeScript should know this is ParticipantSeatedEvent
        expect(participantEvent.payload.participantId).toBeDefined();
        expect(participantEvent.payload.seatNo).toBeDefined();
      }
    });
  });

  // ============================================================================
  // Event Structure Tests
  // ============================================================================

  describe('Event Structures', () => {
    it('should create valid SessionStartedEvent', () => {
      const event: SessionStartedEvent = {
        kind: 'SessionStarted',
        version: 1,
        payload: {
          sessionId: '550e8400-e29b-41d4-a716-446655440001',
          hostId: '550e8400-e29b-41d4-a716-446655440002',
          startedAt: 1693747200000,
          config: {
            maxRounds: 5,
            roundDurationMs: 300000,
            gracePeriodMs: 30000,
            reconnectTimeoutMs: 60000,
          },
        },
      };

      expect(event.kind).toBe('SessionStarted');
      expect(event.version).toBe(1);
      expect(event.payload.sessionId).toBe('550e8400-e29b-41d4-a716-446655440001');
      expect(event.payload.config.maxRounds).toBe(5);
    });

    it('should create valid RoomsRebalancedSoftEvent', () => {
      const event: RoomsRebalancedSoftEvent = {
        kind: 'RoomsRebalancedSoft',
        version: 1,
        payload: {
          sessionId: '550e8400-e29b-41d4-a716-446655440001',
          rebalancedAt: 1693747200000,
          moves: [
            {
              participantId: '550e8400-e29b-41d4-a716-446655440002',
              fromRoomId: '550e8400-e29b-41d4-a716-446655440003',
              toRoomId: '550e8400-e29b-41d4-a716-446655440004',
              fromSeatNo: 0,
              toSeatNo: 1,
            },
          ],
          reason: 'PARTICIPANT_LEFT',
        },
      };

      expect(event.kind).toBe('RoomsRebalancedSoft');
      expect(event.payload.moves).toHaveLength(1);
      expect(event.payload.moves[0].participantId).toBe('550e8400-e29b-41d4-a716-446655440002');
    });

    it('should create valid ParticipantDisconnectedEvent', () => {
      const event: ParticipantDisconnectedEvent = {
        kind: 'ParticipantDisconnected',
        version: 1,
        payload: {
          sessionId: '550e8400-e29b-41d4-a716-446655440001',
          roomId: '550e8400-e29b-41d4-a716-446655440002',
          participantId: '550e8400-e29b-41d4-a716-446655440003',
          seatNo: 2,
          disconnectedAt: 1693747200000,
          reason: 'NETWORK',
        },
      };

      expect(event.kind).toBe('ParticipantDisconnected');
      expect(event.payload.reason).toBe('NETWORK');
      expect(event.payload.seatNo).toBe(2);
    });
  });

  // ============================================================================
  // Discriminated Union Tests
  // ============================================================================

  describe('Discriminated Union', () => {
    it('should handle all event types in switch statement', () => {
      const events: DomainEvent[] = [
        {
          kind: 'SessionStarted',
          version: 1,
          payload: {
            sessionId: '550e8400-e29b-41d4-a716-446655440001',
            hostId: '550e8400-e29b-41d4-a716-446655440002',
            startedAt: 1693747200000,
            config: {
              maxRounds: 5,
              roundDurationMs: 300000,
              gracePeriodMs: 30000,
              reconnectTimeoutMs: 60000,
            },
          },
        },
        {
          kind: 'ParticipantSeated',
          version: 1,
          payload: {
            sessionId: '550e8400-e29b-41d4-a716-446655440001',
            roomId: '550e8400-e29b-41d4-a716-446655440003',
            participantId: '550e8400-e29b-41d4-a716-446655440004',
            seatNo: 0,
            seatedAt: 1693747200000,
          },
        },
      ];

      const results = events.map(event => {
        switch (event.kind) {
          case 'SessionStarted':
            return `Session ${event.payload.sessionId} started`;
          case 'SessionEnded':
            return `Session ${event.payload.sessionId} ended`;
          case 'RoundStarted':
            return `Round ${event.payload.roundNumber} started`;
          case 'RoundEnded':
            return `Round ${event.payload.roundNumber} ended`;
          case 'RoomReady':
            return `Room ${event.payload.roomId} ready`;
          case 'RoomClosed':
            return `Room ${event.payload.roomId} closed`;
          case 'ParticipantSeated':
            return `Participant ${event.payload.participantId} seated`;
          case 'ParticipantMoved':
            return `Participant ${event.payload.participantId} moved`;
          case 'ParticipantDisconnected':
            return `Participant ${event.payload.participantId} disconnected`;
          case 'ParticipantRestored':
            return `Participant ${event.payload.participantId} restored`;
          case 'RoomsRebalancedSoft':
            return `Rooms soft rebalanced with ${event.payload.moves.length} moves`;
          case 'RoomsRebalancedHard':
            return `Rooms hard rebalanced with ${event.payload.moves.length} moves`;
          default:
            // TypeScript should ensure this is never reached
            const _exhaustive: never = event;
            throw new Error(`Unhandled event kind: ${(_exhaustive as any).kind}`);
        }
      });

      expect(results).toEqual([
        'Session 550e8400-e29b-41d4-a716-446655440001 started',
        'Participant 550e8400-e29b-41d4-a716-446655440004 seated',
      ]);
    });
  });
});
