/**
 * Domain Events - Public API exports
 * 
 * This module exports all domain event types and utilities.
 * These are payload-only types with no infrastructure concerns.
 */

export type {
  // Session Events
  SessionStartedEvent,
  SessionEndedEvent,
  
  // Round Events
  RoundStartedEvent,
  RoundEndedEvent,
  
  // Room Events
  RoomReadyEvent,
  RoomClosedEvent,
  
  // Participant Events
  ParticipantSeatedEvent,
  ParticipantMovedEvent,
  ParticipantDisconnectedEvent,
  ParticipantRestoredEvent,
  
  // Rebalancing Events
  RoomsRebalancedSoftEvent,
  RoomsRebalancedHardEvent,
  
  // Union and Utility Types
  DomainEvent,
  EventKind,
  EventPayload,
} from './events';

export {
  // Type Guards and Utilities
  isDomainEvent,
  isEventOfKind,
} from './events';

// Testing utilities
export * from './testing';
