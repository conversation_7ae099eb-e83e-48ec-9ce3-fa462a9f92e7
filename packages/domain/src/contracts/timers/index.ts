/**
 * Timer Contracts - Public API exports
 * 
 * This module exports timer scheduling contracts for the domain layer.
 * These are intent-only types that define the structure of timer requests
 * and responses without implementing the actual scheduling mechanism.
 * 
 * The timer system supports three types of timers:
 * - ROUND_TIMEOUT: When a round should end
 * - ROUND_GRACE_TIMEOUT: When grace period for late participants expires
 * - RECONNECT_HOLD_EXPIRES: When reconnection hold period expires
 */

export type {
  TimerKind,
  TimerIntent,
  TimerRequest,
  TimerAck
} from './timers';

export {
  isTimerIntent,
  isTimerRequest,
  isTimerAck,
  createTimerIntent,
  createTimerRequest,
  createTimerAck
} from './timers';
