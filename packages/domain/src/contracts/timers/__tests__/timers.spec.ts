import {
  TimerK<PERSON>,
  TimerIntent,
  TimerRequest,
  TimerAck,
  isTimerIntent,
  isTimerRequest,
  isTimerAck,
  createTimerIntent,
  createTimerRequest,
  createTimerAck
} from '../index';

describe('Timer Contracts', () => {
  describe('TimerKind type', () => {
    it('should include all expected timer kinds', () => {
      const kinds: TimerKind[] = [
        'ROUND_TIMEOUT',
        'ROUND_GRACE_TIMEOUT',
        'RECONNECT_HOLD_EXPIRES'
      ];

      // This test ensures the type includes all expected values
      // TypeScript will catch if we're missing any required kinds
      expect(kinds).toHaveLength(3);
      expect(kinds).toContain('ROUND_TIMEOUT');
      expect(kinds).toContain('ROUND_GRACE_TIMEOUT');
      expect(kinds).toContain('RECONNECT_HOLD_EXPIRES');
    });
  });

  describe('TimerIntent interface', () => {
    it('should compile with required properties', () => {
      const intent: TimerIntent = {
        kind: 'ROUND_TIMEOUT',
        at: 1640995200000,
        correlationId: 'test-correlation-id'
      };

      expect(intent.kind).toBe('ROUND_TIMEOUT');
      expect(intent.at).toBe(1640995200000);
      expect(intent.correlationId).toBe('test-correlation-id');
      expect(intent.payload).toBeUndefined();
    });

    it('should compile with optional payload', () => {
      const intent: TimerIntent = {
        kind: 'ROUND_GRACE_TIMEOUT',
        at: 1640995200000,
        correlationId: 'test-correlation-id',
        payload: { roomId: 'room-123', roundId: 'round-456' }
      };

      expect(intent.payload).toEqual({ roomId: 'room-123', roundId: 'round-456' });
    });

    it('should support all timer kinds', () => {
      const roundTimeout: TimerIntent = {
        kind: 'ROUND_TIMEOUT',
        at: 1640995200000,
        correlationId: 'test-1'
      };

      const graceTimeout: TimerIntent = {
        kind: 'ROUND_GRACE_TIMEOUT',
        at: 1640995200000,
        correlationId: 'test-2'
      };

      const reconnectExpires: TimerIntent = {
        kind: 'RECONNECT_HOLD_EXPIRES',
        at: 1640995200000,
        correlationId: 'test-3'
      };

      expect(roundTimeout.kind).toBe('ROUND_TIMEOUT');
      expect(graceTimeout.kind).toBe('ROUND_GRACE_TIMEOUT');
      expect(reconnectExpires.kind).toBe('RECONNECT_HOLD_EXPIRES');
    });
  });

  describe('TimerRequest interface', () => {
    it('should compile with array of intents', () => {
      const request: TimerRequest = {
        intents: [
          {
            kind: 'ROUND_TIMEOUT',
            at: 1640995200000,
            correlationId: 'test-1'
          },
          {
            kind: 'ROUND_GRACE_TIMEOUT',
            at: 1640995300000,
            correlationId: 'test-2',
            payload: { roomId: 'room-123' }
          }
        ]
      };

      expect(request.intents).toHaveLength(2);
      expect(request.intents[0].kind).toBe('ROUND_TIMEOUT');
      expect(request.intents[1].kind).toBe('ROUND_GRACE_TIMEOUT');
    });

    it('should compile with empty intents array', () => {
      const request: TimerRequest = {
        intents: []
      };

      expect(request.intents).toHaveLength(0);
    });
  });

  describe('TimerAck interface', () => {
    it('should compile with accepted true', () => {
      const ack: TimerAck = {
        accepted: true
      };

      expect(ack.accepted).toBe(true);
      expect(ack.reason).toBeUndefined();
    });

    it('should compile with accepted false and reason', () => {
      const ack: TimerAck = {
        accepted: false,
        reason: 'Invalid timer configuration'
      };

      expect(ack.accepted).toBe(false);
      expect(ack.reason).toBe('Invalid timer configuration');
    });
  });

  describe('isTimerIntent type guard', () => {
    it('should return true for valid timer intent', () => {
      const intent = {
        kind: 'ROUND_TIMEOUT',
        at: 1640995200000,
        correlationId: 'test-correlation-id'
      };

      expect(isTimerIntent(intent)).toBe(true);
    });

    it('should return true for valid timer intent with payload', () => {
      const intent = {
        kind: 'ROUND_GRACE_TIMEOUT',
        at: 1640995200000,
        correlationId: 'test-correlation-id',
        payload: { roomId: 'room-123' }
      };

      expect(isTimerIntent(intent)).toBe(true);
    });

    it('should return false for null or undefined', () => {
      expect(isTimerIntent(null)).toBe(false);
      expect(isTimerIntent(undefined)).toBe(false);
    });

    it('should return false for non-object values', () => {
      expect(isTimerIntent('string')).toBe(false);
      expect(isTimerIntent(123)).toBe(false);
      expect(isTimerIntent(true)).toBe(false);
    });

    it('should return false for invalid kind', () => {
      const intent = {
        kind: 'INVALID_KIND',
        at: 1640995200000,
        correlationId: 'test-correlation-id'
      };

      expect(isTimerIntent(intent)).toBe(false);
    });

    it('should return false for missing required properties', () => {
      expect(isTimerIntent({ kind: 'ROUND_TIMEOUT' })).toBe(false);
      expect(isTimerIntent({ at: 1640995200000 })).toBe(false);
      expect(isTimerIntent({ correlationId: 'test' })).toBe(false);
    });

    it('should return false for invalid property types', () => {
      expect(isTimerIntent({
        kind: 123,
        at: 1640995200000,
        correlationId: 'test'
      })).toBe(false);

      expect(isTimerIntent({
        kind: 'ROUND_TIMEOUT',
        at: 'invalid',
        correlationId: 'test'
      })).toBe(false);

      expect(isTimerIntent({
        kind: 'ROUND_TIMEOUT',
        at: 1640995200000,
        correlationId: 123
      })).toBe(false);
    });

    it('should return false for invalid payload type', () => {
      expect(isTimerIntent({
        kind: 'ROUND_TIMEOUT',
        at: 1640995200000,
        correlationId: 'test',
        payload: 'invalid'
      })).toBe(false);

      expect(isTimerIntent({
        kind: 'ROUND_TIMEOUT',
        at: 1640995200000,
        correlationId: 'test',
        payload: null
      })).toBe(false);
    });
  });

  describe('isTimerRequest type guard', () => {
    it('should return true for valid timer request', () => {
      const request = {
        intents: [
          {
            kind: 'ROUND_TIMEOUT',
            at: 1640995200000,
            correlationId: 'test-1'
          },
          {
            kind: 'ROUND_GRACE_TIMEOUT',
            at: 1640995300000,
            correlationId: 'test-2'
          }
        ]
      };

      expect(isTimerRequest(request)).toBe(true);
    });

    it('should return true for empty intents array', () => {
      const request = { intents: [] };
      expect(isTimerRequest(request)).toBe(true);
    });

    it('should return false for null or undefined', () => {
      expect(isTimerRequest(null)).toBe(false);
      expect(isTimerRequest(undefined)).toBe(false);
    });

    it('should return false for non-object values', () => {
      expect(isTimerRequest('string')).toBe(false);
      expect(isTimerRequest(123)).toBe(false);
    });

    it('should return false for missing intents property', () => {
      expect(isTimerRequest({})).toBe(false);
    });

    it('should return false for non-array intents', () => {
      expect(isTimerRequest({ intents: 'invalid' })).toBe(false);
      expect(isTimerRequest({ intents: 123 })).toBe(false);
    });

    it('should return false for invalid intents in array', () => {
      const request = {
        intents: [
          {
            kind: 'ROUND_TIMEOUT',
            at: 1640995200000,
            correlationId: 'test-1'
          },
          {
            kind: 'INVALID_KIND',
            at: 1640995300000,
            correlationId: 'test-2'
          }
        ]
      };

      expect(isTimerRequest(request)).toBe(false);
    });
  });

  describe('isTimerAck type guard', () => {
    it('should return true for valid timer ack with accepted true', () => {
      const ack = { accepted: true };
      expect(isTimerAck(ack)).toBe(true);
    });

    it('should return true for valid timer ack with accepted false and reason', () => {
      const ack = { accepted: false, reason: 'Error message' };
      expect(isTimerAck(ack)).toBe(true);
    });

    it('should return false for null or undefined', () => {
      expect(isTimerAck(null)).toBe(false);
      expect(isTimerAck(undefined)).toBe(false);
    });

    it('should return false for non-object values', () => {
      expect(isTimerAck('string')).toBe(false);
      expect(isTimerAck(123)).toBe(false);
    });

    it('should return false for missing accepted property', () => {
      expect(isTimerAck({})).toBe(false);
      expect(isTimerAck({ reason: 'test' })).toBe(false);
    });

    it('should return false for invalid accepted type', () => {
      expect(isTimerAck({ accepted: 'true' })).toBe(false);
      expect(isTimerAck({ accepted: 1 })).toBe(false);
    });

    it('should return false for invalid reason type', () => {
      expect(isTimerAck({ accepted: false, reason: 123 })).toBe(false);
      expect(isTimerAck({ accepted: false, reason: true })).toBe(false);
    });
  });

  describe('createTimerIntent helper', () => {
    it('should create timer intent without payload', () => {
      const intent = createTimerIntent('ROUND_TIMEOUT', 1640995200000, 'test-correlation');

      expect(intent).toEqual({
        kind: 'ROUND_TIMEOUT',
        at: 1640995200000,
        correlationId: 'test-correlation'
      });
      expect(intent.payload).toBeUndefined();
    });

    it('should create timer intent with payload', () => {
      const payload = { roomId: 'room-123', roundId: 'round-456' };
      const intent = createTimerIntent('ROUND_GRACE_TIMEOUT', 1640995200000, 'test-correlation', payload);

      expect(intent).toEqual({
        kind: 'ROUND_GRACE_TIMEOUT',
        at: 1640995200000,
        correlationId: 'test-correlation',
        payload: { roomId: 'room-123', roundId: 'round-456' }
      });
    });

    it('should create timer intent for all timer kinds', () => {
      const roundTimeout = createTimerIntent('ROUND_TIMEOUT', 1640995200000, 'test-1');
      const graceTimeout = createTimerIntent('ROUND_GRACE_TIMEOUT', 1640995300000, 'test-2');
      const reconnectExpires = createTimerIntent('RECONNECT_HOLD_EXPIRES', 1640995400000, 'test-3');

      expect(roundTimeout.kind).toBe('ROUND_TIMEOUT');
      expect(graceTimeout.kind).toBe('ROUND_GRACE_TIMEOUT');
      expect(reconnectExpires.kind).toBe('RECONNECT_HOLD_EXPIRES');
    });
  });

  describe('createTimerRequest helper', () => {
    it('should create timer request with single intent', () => {
      const intent = createTimerIntent('ROUND_TIMEOUT', 1640995200000, 'test-correlation');
      const request = createTimerRequest([intent]);

      expect(request).toEqual({
        intents: [intent]
      });
    });

    it('should create timer request with multiple intents', () => {
      const intent1 = createTimerIntent('ROUND_TIMEOUT', 1640995200000, 'test-1');
      const intent2 = createTimerIntent('ROUND_GRACE_TIMEOUT', 1640995300000, 'test-2');
      const request = createTimerRequest([intent1, intent2]);

      expect(request).toEqual({
        intents: [intent1, intent2]
      });
    });

    it('should create timer request with empty intents', () => {
      const request = createTimerRequest([]);

      expect(request).toEqual({
        intents: []
      });
    });
  });

  describe('createTimerAck helper', () => {
    it('should create timer ack with accepted true', () => {
      const ack = createTimerAck(true);

      expect(ack).toEqual({
        accepted: true
      });
      expect(ack.reason).toBeUndefined();
    });

    it('should create timer ack with accepted false and reason', () => {
      const ack = createTimerAck(false, 'Invalid timer configuration');

      expect(ack).toEqual({
        accepted: false,
        reason: 'Invalid timer configuration'
      });
    });

    it('should create timer ack with accepted false without reason', () => {
      const ack = createTimerAck(false);

      expect(ack).toEqual({
        accepted: false
      });
      expect(ack.reason).toBeUndefined();
    });
  });

  describe('Usage patterns', () => {
    it('should support typical timer scheduling workflow', () => {
      // Create timer intents for a round
      const roundTimeout = createTimerIntent(
        'ROUND_TIMEOUT',
        Date.now() + 60000, // 1 minute from now
        'round-123-timeout',
        { roomId: 'room-456', roundId: 'round-123' }
      );

      const graceTimeout = createTimerIntent(
        'ROUND_GRACE_TIMEOUT',
        Date.now() + 90000, // 1.5 minutes from now
        'round-123-grace',
        { roomId: 'room-456', roundId: 'round-123' }
      );

      // Create a batch request
      const request = createTimerRequest([roundTimeout, graceTimeout]);

      // Validate the request
      expect(isTimerRequest(request)).toBe(true);
      expect(request.intents).toHaveLength(2);

      // Create acknowledgment responses
      const successAck = createTimerAck(true);
      const failureAck = createTimerAck(false, 'Scheduler unavailable');

      expect(isTimerAck(successAck)).toBe(true);
      expect(isTimerAck(failureAck)).toBe(true);
      expect(successAck.accepted).toBe(true);
      expect(failureAck.accepted).toBe(false);
      expect(failureAck.reason).toBe('Scheduler unavailable');
    });

    it('should support reconnection timer workflow', () => {
      const reconnectTimer = createTimerIntent(
        'RECONNECT_HOLD_EXPIRES',
        Date.now() + 30000, // 30 seconds from now
        'participant-789-reconnect',
        { 
          participantId: 'participant-789',
          roomId: 'room-456',
          holdStartedAt: Date.now()
        }
      );

      const request = createTimerRequest([reconnectTimer]);
      
      expect(isTimerRequest(request)).toBe(true);
      expect(request.intents[0].kind).toBe('RECONNECT_HOLD_EXPIRES');
      expect(request.intents[0].payload?.participantId).toBe('participant-789');
    });

    it('should demonstrate type safety', () => {
      // TypeScript should catch invalid timer kinds at compile time
      // This test ensures our types are properly defined
      
      const validKinds: TimerKind[] = [
        'ROUND_TIMEOUT',
        'ROUND_GRACE_TIMEOUT', 
        'RECONNECT_HOLD_EXPIRES'
      ];

      validKinds.forEach(kind => {
        const intent = createTimerIntent(kind, Date.now(), 'test');
        expect(isTimerIntent(intent)).toBe(true);
      });
    });
  });
});
