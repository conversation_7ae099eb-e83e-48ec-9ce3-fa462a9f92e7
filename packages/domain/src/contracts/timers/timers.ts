/**
 * Timer Scheduling Contracts
 * 
 * This module defines the types and contracts for timer scheduling in the domain.
 * These are intent-only contracts that define what timers can be scheduled and
 * how they are structured, without implementing the actual scheduling mechanism.
 * 
 * The timer system supports three types of timers:
 * - ROUND_TIMEOUT: When a round should end
 * - ROUND_GRACE_TIMEOUT: When grace period for late participants expires
 * - RECONNECT_HOLD_EXPIRES: When reconnection hold period expires
 * 
 * All timers are scheduled with server-authoritative time (milliseconds since epoch)
 * and include correlation IDs for tracking and optional payload data.
 */

/**
 * Timer intent kinds supported by the system
 * 
 * These represent the three timer types that can be scheduled:
 * - ROUND_TIMEOUT: Fired when a round should end
 * - ROUND_GRACE_TIMEOUT: Fired when grace period for late participants expires  
 * - RECONNECT_HOLD_EXPIRES: Fired when reconnection hold period expires
 */
export type TimerKind = 
  | 'ROUND_TIMEOUT'
  | 'ROUND_GRACE_TIMEOUT'
  | 'RECONNECT_HOLD_EXPIRES';

/**
 * Timer intent representing a request to schedule a timer
 * 
 * This defines what timer should be scheduled, when it should fire,
 * and what context should be included when it fires.
 * 
 * @property kind - The type of timer to schedule
 * @property at - When the timer should fire (milliseconds since Unix epoch)
 * @property correlationId - Correlation ID for tracking this timer
 * @property payload - Optional additional data to include with the timer
 */
export interface TimerIntent {
  /** The type of timer to schedule */
  kind: TimerKind;
  
  /** When the timer should fire (milliseconds since Unix epoch) */
  at: number;
  
  /** Correlation ID for tracking this timer across the system */
  correlationId: string;
  
  /** Optional additional data to include when the timer fires */
  payload?: Record<string, unknown>;
}

/**
 * Timer request containing one or more timer intents
 * 
 * This represents a batch request to schedule multiple timers.
 * The scheduler can process multiple timer intents in a single request
 * for efficiency and atomicity.
 * 
 * @property intents - Array of timer intents to schedule
 */
export interface TimerRequest {
  /** Array of timer intents to schedule */
  intents: TimerIntent[];
}

/**
 * Timer acknowledgment response from the scheduler
 * 
 * This represents the response from the timer scheduler indicating
 * whether the timer request was accepted and processed successfully.
 * 
 * @property accepted - Whether the timer request was accepted
 * @property reason - Optional reason if the request was rejected
 */
export interface TimerAck {
  /** Whether the timer request was accepted and scheduled */
  accepted: boolean;
  
  /** Optional reason if the request was rejected */
  reason?: string;
}

/**
 * Type guard to check if an object is a valid TimerIntent
 * 
 * @param obj - Object to check
 * @returns True if the object is a valid TimerIntent
 */
export function isTimerIntent(obj: unknown): obj is TimerIntent {
  if (typeof obj !== 'object' || obj === null) {
    return false;
  }
  
  const intent = obj as Record<string, unknown>;
  
  return (
    typeof intent.kind === 'string' &&
    (intent.kind === 'ROUND_TIMEOUT' || 
     intent.kind === 'ROUND_GRACE_TIMEOUT' || 
     intent.kind === 'RECONNECT_HOLD_EXPIRES') &&
    typeof intent.at === 'number' &&
    typeof intent.correlationId === 'string' &&
    (intent.payload === undefined || 
     (typeof intent.payload === 'object' && intent.payload !== null))
  );
}

/**
 * Type guard to check if an object is a valid TimerRequest
 * 
 * @param obj - Object to check
 * @returns True if the object is a valid TimerRequest
 */
export function isTimerRequest(obj: unknown): obj is TimerRequest {
  if (typeof obj !== 'object' || obj === null) {
    return false;
  }
  
  const request = obj as Record<string, unknown>;
  
  return (
    Array.isArray(request.intents) &&
    request.intents.every(intent => isTimerIntent(intent))
  );
}

/**
 * Type guard to check if an object is a valid TimerAck
 * 
 * @param obj - Object to check
 * @returns True if the object is a valid TimerAck
 */
export function isTimerAck(obj: unknown): obj is TimerAck {
  if (typeof obj !== 'object' || obj === null) {
    return false;
  }
  
  const ack = obj as Record<string, unknown>;
  
  return (
    typeof ack.accepted === 'boolean' &&
    (ack.reason === undefined || typeof ack.reason === 'string')
  );
}

/**
 * Helper function to create a timer intent
 * 
 * @param kind - The type of timer to schedule
 * @param at - When the timer should fire (milliseconds since Unix epoch)
 * @param correlationId - Correlation ID for tracking
 * @param payload - Optional additional data
 * @returns A new TimerIntent
 */
export function createTimerIntent(
  kind: TimerKind,
  at: number,
  correlationId: string,
  payload?: Record<string, unknown>
): TimerIntent {
  const intent: TimerIntent = {
    kind,
    at,
    correlationId
  };
  
  if (payload !== undefined) {
    intent.payload = payload;
  }
  
  return intent;
}

/**
 * Helper function to create a timer request
 * 
 * @param intents - Array of timer intents to include in the request
 * @returns A new TimerRequest
 */
export function createTimerRequest(intents: TimerIntent[]): TimerRequest {
  return { intents };
}

/**
 * Helper function to create a timer acknowledgment
 * 
 * @param accepted - Whether the request was accepted
 * @param reason - Optional reason if rejected
 * @returns A new TimerAck
 */
export function createTimerAck(accepted: boolean, reason?: string): TimerAck {
  const ack: TimerAck = { accepted };
  
  if (reason !== undefined) {
    ack.reason = reason;
  }
  
  return ack;
}
