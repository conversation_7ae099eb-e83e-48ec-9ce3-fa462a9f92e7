import { Uuid } from '../../primitives/uuid/uuid.primitive';
import { SeatNo } from '../../primitives/seat-no/seat-no.primitive';
import { RoomId } from '../../rooms/value-objects/room-id/room-id.vo';
import { RoomConfig } from '../../rooms/value-objects/room-config/room-config.vo';
import { <PERSON>oster } from '../../entities/roster/roster.entity';
import { Room } from '../../aggregates/room/room.aggregate';
import { LateJoinPolicy } from '../../policies/late-join/late-join-policy.vo';
import { LateJoinAllocationMode } from '../../policies/late-join/types/late-join-allocation-mode.enum';
import { AllocationStrategy } from '../../policies/autopilot/types/allocation-strategy.enum';
import { ensure } from '../../support/ensure';
import {
  AllocatorNoParticipantsError,
  AllocatorCapacityExceededError,
  AllocatorParticipantNotFoundError,
  AllocatorNoAvailableRoomsError
} from './allocator.errors';

/**
 * Simple Linear Congruential Generator for deterministic pseudo-random numbers
 * Uses the same constants as Numerical Recipes: a=1664525, c=1013904223, m=2^32
 */
class SeededPRNG {
  private state: number;

  constructor(seed: number) {
    // Ensure seed is a positive 32-bit integer
    this.state = Math.abs(seed) % 0x100000000;
    if (this.state === 0) this.state = 1; // Avoid zero state
  }

  /**
   * Generate next pseudo-random number between 0 and 1 (exclusive)
   */
  next(): number {
    this.state = (this.state * 1664525 + 1013904223) % 0x100000000;
    return this.state / 0x100000000;
  }

  /**
   * Generate random integer between min (inclusive) and max (exclusive)
   */
  nextInt(min: number, max: number): number {
    return Math.floor(this.next() * (max - min)) + min;
  }
}

export type AllocationDecision = {
  roomId: RoomId;
  seatNo: SeatNo;
};

export type CreateRoomDecision = {
  createNew: true;
};

export type RoomDecision = AllocationDecision | CreateRoomDecision;

export class ProgressiveAllocator {
  private prng: SeededPRNG;

  constructor(
    private readonly roster: Roster,
    private readonly roomConfig: RoomConfig,
    private readonly lateJoinPolicy: LateJoinPolicy,
    private readonly allocationStrategy: AllocationStrategy,
    seed: number
  ) {
    this.prng = new SeededPRNG(seed);
  }

  /**
   * Select the next participant for seating based on deterministic rules
   * Returns undefined if no participants are available
   */
  selectNextParticipantForSeating(): Uuid | undefined {
    const connectedAttendees = this.roster.connectedAttendees();
    
    if (connectedAttendees.length === 0) {
      return undefined;
    }

    // Sort by participant ID for deterministic tiebreaking
    const sortedParticipants = connectedAttendees
      .sort((a, b) => a.toPrimitives().localeCompare(b.toPrimitives()));

    // For deterministic selection, use the first participant
    // In a real implementation, this could consider other factors like join order
    return sortedParticipants[0];
  }

  /**
   * Find the best room for a participant or decide to create a new one
   */
  findOrCreateRoomFor(participantId: Uuid, rooms: Map<string, Room>): RoomDecision {
    ensure(
      this.roster.has(participantId),
      new AllocatorParticipantNotFoundError({
        participantId: participantId.toPrimitives()
      })
    );

    const availableRooms = Array.from(rooms.values())
      .filter(room => room.hasSpace && !room.isClosed);

    // If no available rooms, create new one
    if (availableRooms.length === 0) {
      return { createNew: true };
    }

    // Apply late join policy
    const selectedRoom = this.selectRoomByPolicy(availableRooms);
    
    if (!selectedRoom) {
      return { createNew: true };
    }

    // Find available seat in selected room
    const availableSeat = selectedRoom.availableSeats[0];
    if (!availableSeat) {
      return { createNew: true };
    }

    return {
      roomId: selectedRoom.roomId,
      seatNo: SeatNo.fromPrimitives(availableSeat.seatNo.toPrimitives())
    };
  }

  /**
   * Assign a specific seat to a participant
   */
  assignSeat(participantId: Uuid, roomId: RoomId, seatNo: SeatNo): AllocationDecision {
    ensure(
      this.roster.has(participantId),
      new AllocatorParticipantNotFoundError({
        participantId: participantId.toPrimitives()
      })
    );

    return {
      roomId,
      seatNo
    };
  }

  private selectRoomByPolicy(availableRooms: Room[]): Room | undefined {
    if (availableRooms.length === 0) {
      return undefined;
    }

    switch (this.lateJoinPolicy.allocationMode) {
      case LateJoinAllocationMode.BEST_FIT:
        return this.selectBestFitRoom(availableRooms);
      
      case LateJoinAllocationMode.NEW_ROOM:
        // Always prefer creating new room, but if forced to choose existing
        return undefined;
      
      case LateJoinAllocationMode.LEAST_RECENT:
        return this.selectLeastRecentRoom(availableRooms);
      
      default:
        // Fallback to best fit
        return this.selectBestFitRoom(availableRooms);
    }
  }

  private selectBestFitRoom(rooms: Room[]): Room | undefined {
    if (rooms.length === 0) return undefined;

    // Find room with most space (to keep groups balanced)
    // If avoiding singletons, prefer rooms with size > 1
    let bestRoom = rooms[0];
    let bestScore = this.calculateBestFitScore(bestRoom);

    for (let i = 1; i < rooms.length; i++) {
      const room = rooms[i];
      const score = this.calculateBestFitScore(room);
      
      if (score > bestScore || (score === bestScore && this.shouldPreferRoom(room, bestRoom))) {
        bestRoom = room;
        bestScore = score;
      }
    }

    return bestRoom;
  }

  private calculateBestFitScore(room: Room): number {
    const availableSpace = room.config.maxSeats - room.size;
    let score = availableSpace;

    // Bonus for avoiding singletons if policy is set
    if (this.roomConfig.avoidSingleton && room.size > 1) {
      score += 10; // Prefer rooms that already have multiple people
    }

    // Prefer READY rooms over FILLING rooms
    if (room.isReady) {
      score += 5;
    }

    return score;
  }

  private selectLeastRecentRoom(rooms: Room[]): Room | undefined {
    if (rooms.length === 0) return undefined;

    // Sort by creation time (oldest first) for least recent allocation
    // Use room ID as tiebreaker for deterministic behavior
    const sortedRooms = [...rooms].sort((a, b) => {
      const timeDiff = a.createdAt.toPrimitives() - b.createdAt.toPrimitives();
      if (timeDiff !== 0) return timeDiff;
      
      // Tiebreaker: lexicographic room ID comparison
      return a.roomId.toPrimitives().localeCompare(b.roomId.toPrimitives());
    });

    return sortedRooms[0];
  }

  private shouldPreferRoom(candidate: Room, current: Room): boolean {
    // Deterministic tiebreaker: prefer room with lexicographically smaller ID
    return candidate.roomId.toPrimitives().localeCompare(current.roomId.toPrimitives()) < 0;
  }
}
