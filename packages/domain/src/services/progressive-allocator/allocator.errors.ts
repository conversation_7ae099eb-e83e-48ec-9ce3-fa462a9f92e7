import { DomainError, ErrorContext } from '../../errors/domain-error';

export const ERR_SERVICE_ALLOCATOR_NO_PARTICIPANTS = 'SERVICE.ALLOCATOR.NO_PARTICIPANTS' as const;
export const ERR_SERVICE_ALLOCATOR_CAPACITY_EXCEEDED = 'SERVICE.ALLOCATOR.CAPACITY_EXCEEDED' as const;
export const ERR_SERVICE_ALLOCATOR_PARTICIPANT_NOT_FOUND = 'SERVICE.ALLOCATOR.PARTICIPANT_NOT_FOUND' as const;
export const ERR_SERVICE_ALLOCATOR_NO_AVAILABLE_ROOMS = 'SERVICE.ALLOCATOR.NO_AVAILABLE_ROOMS' as const;

export class AllocatorNoParticipantsError extends DomainError {
  constructor(ctx?: ErrorContext) {
    super('No participants available for seating', ERR_SERVICE_ALLOCATOR_NO_PARTICIPANTS, ctx);
  }
}

export class AllocatorCapacityExceededError extends DomainError {
  constructor(ctx?: ErrorContext) {
    super('Session capacity exceeded', ERR_SERVICE_ALLOCATOR_CAPACITY_EXCEEDED, ctx);
  }
}

export class AllocatorParticipantNotFoundError extends DomainError {
  constructor(ctx?: ErrorContext) {
    super('Participant not found in roster', ERR_SERVICE_ALLOCATOR_PARTICIPANT_NOT_FOUND, ctx);
  }
}

export class AllocatorNoAvailableRoomsError extends DomainError {
  constructor(ctx?: ErrorContext) {
    super('No available rooms for allocation', ERR_SERVICE_ALLOCATOR_NO_AVAILABLE_ROOMS, ctx);
  }
}
