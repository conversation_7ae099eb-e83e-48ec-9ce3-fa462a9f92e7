import { ensure } from '../../support/ensure';
import { InvalidInstantError } from './instant.errors';

export class Instant {
  private constructor(private readonly value: number) {}

  static fromPrimitives(raw: number): Instant {
    ensure(
      Number.isInteger(raw) && raw >= 0 && Number.isFinite(raw),
      new InvalidInstantError({ value: raw })
    );
    return new Instant(raw);
  }

  toPrimitives(): number {
    return this.value;
  }

  static now(): Instant {
    return Instant.fromPrimitives(Date.now());
  }
}
