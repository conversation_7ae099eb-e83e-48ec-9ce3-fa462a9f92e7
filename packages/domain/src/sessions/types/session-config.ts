import { AutopilotPolicyPrimitives } from "../../policies/autopilot/types/auto-pilote.policy";
import { DisconnectionPolicyPrimitives } from "../../policies/disconnection/types/disconnection.policy";
import { LateJoinPolicyPrimitives } from "../../policies/late-join/types/late-join.policy";
import { LobbyAdmissionPolicyPrimitives } from "../../policies/lobby-admission/types/lobby-admission";
import { RoomConfigPrimitives } from "../../rooms/types/room-config";
import { SessionMode } from "./session-mode.enum";

export type SessionConfigPrimitives = {
  // Scheduling
  scheduledStartAt: number;
  estimatedDurationMs: number;
  
  // Rooms configuration
  defaultRoomConfig: RoomConfigPrimitives;
  maxParticipants: number;

  // Rounds configuration
  defaultRoundDurationMs: number;
  
  // Policies
  autopilotPolicy: AutopilotPolicyPrimitives;
  lobbyAdmissionPolicy: LobbyAdmissionPolicyPrimitives;
  disconnectionPolicy: DisconnectionPolicyPrimitives;
  lateJoinPolicy: LateJoinPolicyPrimitives;
  mode: SessionMode;
}