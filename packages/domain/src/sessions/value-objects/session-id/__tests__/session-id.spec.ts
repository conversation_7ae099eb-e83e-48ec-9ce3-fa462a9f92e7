import { SessionId } from '../session-id.vo';
import { InvalidUuidError } from '../../../../primitives/uuid/uuid.errors';

describe('SessionId', () => {
  describe('fromPrimitives_accepts_valid_uuid', () => {
    const validUuids = [
      '550e8400-e29b-41d4-a716-************',
      'f47ac10b-58cc-4372-a567-0e02b2c3d479',
      '6ba7b810-9dad-41d1-80b4-00c04fd430c8',
    ];

    validUuids.forEach(uuid => {
      it(`accepts valid UUID: ${uuid}`, () => {
        const result = SessionId.fromPrimitives(uuid);
        expect(result).toBeInstanceOf(SessionId);
        expect(result.toPrimitives()).toBe(uuid);
        expect(result.toString()).toBe(uuid);
      });
    });
  });

  describe('fromPrimitives_rejects_invalid_uuid', () => {
    const invalidValues = [
      '',
      'not-a-uuid',
      '550e8400-e29b-41d4-a716-44665544000', // too short
      '550e8400-e29b-21d4-a716-************', // wrong version
      null as any,
      undefined as any,
      123 as any,
    ];

    invalidValues.forEach(value => {
      it(`rejects invalid value: ${JSON.stringify(value)}`, () => {
        expect(() => SessionId.fromPrimitives(value)).toThrow(InvalidUuidError);
        
        try {
          SessionId.fromPrimitives(value);
        } catch (error) {
          expect(error).toBeInstanceOf(InvalidUuidError);
          expect((error as InvalidUuidError).code).toBe('PRIMITIVE.UUID.INVALID');
        }
      });
    });
  });

  describe('toPrimitives_roundtrip', () => {
    it('roundtrip returns original value', () => {
      const original = '550e8400-e29b-41d4-a716-************';
      const sessionId = SessionId.fromPrimitives(original);
      expect(sessionId.toPrimitives()).toBe(original);
    });

    it('toString works correctly', () => {
      const original = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
      const sessionId = SessionId.fromPrimitives(original);
      expect(sessionId.toString()).toBe(original);
    });
  });

  describe('composition_over_uuid', () => {
    it('uses Uuid internally without duplicating validation logic', () => {
      // This test ensures we're composing over Uuid rather than duplicating regex
      const validUuid = '550e8400-e29b-41d4-a716-************';
      const sessionId = SessionId.fromPrimitives(validUuid);
      
      // If composition is working correctly, this should work
      expect(sessionId.toPrimitives()).toBe(validUuid);
      
      // And invalid UUIDs should throw the same error as Uuid
      expect(() => SessionId.fromPrimitives('invalid')).toThrow(InvalidUuidError);
    });
  });
});
