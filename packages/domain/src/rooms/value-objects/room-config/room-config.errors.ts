import { DomainError } from '../../../errors/domain-error';

export const ERR_VO_ROOM_CONFIG_MIN = 'VO.ROOM_CONFIG.MIN_INVALID' as const;
export const ERR_VO_ROOM_CONFIG_MAX = 'VO.ROOM_CONFIG.MAX_INVALID' as const;

export class RoomConfigMinInvalidError extends DomainError {
  constructor(ctx?: Record<string, unknown>) {
    super(ERR_VO_ROOM_CONFIG_MIN, 'minSeats must be >= 2.', ctx);
  }
}

export class RoomConfigMaxInvalidError extends DomainError {
  constructor(ctx?: Record<string, unknown>) {
    super(ERR_VO_ROOM_CONFIG_MAX, 'maxSeats must be >= minSeats.', ctx);
  }
}
