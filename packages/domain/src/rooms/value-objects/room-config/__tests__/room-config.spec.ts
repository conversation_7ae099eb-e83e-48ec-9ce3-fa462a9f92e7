import { RoomConfig } from '../room-config.vo';
import { 
  RoomConfigMinInvalidError, 
  RoomConfigMaxInvalidError,
  ERR_VO_ROOM_CONFIG_MIN,
  ERR_VO_ROOM_CONFIG_MAX
} from '../room-config.errors';
import { RoomConfigPrimitives } from '../../../types/room-config';

describe('RoomConfig', () => {
  describe('fromPrimitives - valid cases', () => {
    it('should accept valid configuration: minSeats=2, maxSeats=4', () => {
      const primitives: RoomConfigPrimitives = { minSeats: 2, maxSeats: 4, avoidSingleton: true };
      const config = RoomConfig.fromPrimitives(primitives);
      
      expect(config.minSeats).toBe(2);
      expect(config.maxSeats).toBe(4);
    });

    it('should accept valid configuration: minSeats=3, maxSeats=10', () => {
      const primitives: RoomConfigPrimitives = { minSeats: 3, maxSeats: 10, avoidSingleton: true };
      const config = RoomConfig.fromPrimitives(primitives);
      
      expect(config.minSeats).toBe(3);
      expect(config.maxSeats).toBe(10);
    });

    it('should accept equal minSeats and maxSeats', () => {
      const primitives: RoomConfigPrimitives = { minSeats: 5, maxSeats: 5,  avoidSingleton: true };
      const config = RoomConfig.fromPrimitives(primitives);
      
      expect(config.minSeats).toBe(5);
      expect(config.maxSeats).toBe(5);
    });
  });

  describe('fromPrimitives - invalid cases', () => {
    it('should reject minSeats < 2', () => {
      const primitives: RoomConfigPrimitives = { minSeats: 1, maxSeats: 4, avoidSingleton: true };
      
      expect(() => RoomConfig.fromPrimitives(primitives)).toThrow(RoomConfigMinInvalidError);
      
      try {
        RoomConfig.fromPrimitives(primitives);
      } catch (error) {
        expect(error).toBeInstanceOf(RoomConfigMinInvalidError);
        expect((error as RoomConfigMinInvalidError).code).toBe(ERR_VO_ROOM_CONFIG_MIN);
        expect((error as RoomConfigMinInvalidError).ctx?.min).toBe(1);
      }
    });

    it('should reject maxSeats < minSeats', () => {
      const primitives: RoomConfigPrimitives = { minSeats: 4, maxSeats: 3, avoidSingleton: true };
      
      expect(() => RoomConfig.fromPrimitives(primitives)).toThrow(RoomConfigMaxInvalidError);
      
      try {
        RoomConfig.fromPrimitives(primitives);
      } catch (error) {
        expect(error).toBeInstanceOf(RoomConfigMaxInvalidError);
        expect((error as RoomConfigMaxInvalidError).code).toBe(ERR_VO_ROOM_CONFIG_MAX);
        expect((error as RoomConfigMaxInvalidError).ctx?.min).toBe(4);
        expect((error as RoomConfigMaxInvalidError).ctx?.max).toBe(3);
      }
    });

    it('should reject non-positive minSeats', () => {
      const primitives: RoomConfigPrimitives = { minSeats: 0, maxSeats: 4, avoidSingleton: true };
      
      expect(() => RoomConfig.fromPrimitives(primitives)).toThrow();
    });

    it('should reject non-positive maxSeats', () => {
      const primitives: RoomConfigPrimitives = { minSeats: 2, maxSeats: 0, avoidSingleton: true };
      
      expect(() => RoomConfig.fromPrimitives(primitives)).toThrow();
    });
  });

  describe('toPrimitives', () => {
    it('should serialize correctly', () => {
      const primitives: RoomConfigPrimitives = { minSeats: 2, maxSeats: 4, avoidSingleton: true };
      const config = RoomConfig.fromPrimitives(primitives);
      
      expect(config.toPrimitives()).toEqual(primitives);
    });
  });

  describe('roundtrip', () => {
    it('should maintain equality after roundtrip', () => {
      const original: RoomConfigPrimitives = { minSeats: 3, maxSeats: 8, avoidSingleton: true };
      const config = RoomConfig.fromPrimitives(original);
      const roundtrip = config.toPrimitives();
      
      expect(roundtrip).toEqual(original);
    });
  });
});
