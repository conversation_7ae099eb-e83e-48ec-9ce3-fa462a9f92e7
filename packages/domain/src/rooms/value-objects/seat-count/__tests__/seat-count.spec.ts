import { SeatCount } from '../seat-count.vo';
import { InvalidNonNegativeIntError } from '../../../../primitives/non-negative-int/non-negative-int.errors';

describe('SeatCount', () => {
  describe('fromPrimitives_accepts_non_negative_integers', () => {
    const validValues = [
      0,  // Empty room
      3,  // Small room
      10, // Medium room
      50, // Large room
    ];

    validValues.forEach(value => {
      it(`accepts valid seat count: ${value}`, () => {
        const result = SeatCount.fromPrimitives(value);
        expect(result).toBeInstanceOf(SeatCount);
        expect(result.toPrimitives()).toBe(value);
      });
    });
  });

  describe('fromPrimitives_rejects_invalid_values', () => {
    const invalidValues = [
      // Negative numbers
      -1,
      -5,
      
      // Non-integers
      1.2,
      2.5,
      
      // Special numbers
      NaN,
      Infinity,
      -Infinity,
      
      // Non-numbers
      '3' as any,
      null as any,
      undefined as any,
      {} as any,
      [] as any,
    ];

    invalidValues.forEach(value => {
      it(`rejects invalid value: ${JSON.stringify(value)}`, () => {
        expect(() => SeatCount.fromPrimitives(value)).toThrow(InvalidNonNegativeIntError);
        
        try {
          SeatCount.fromPrimitives(value);
        } catch (error) {
          expect(error).toBeInstanceOf(InvalidNonNegativeIntError);
          expect((error as InvalidNonNegativeIntError).code).toBe('PRIMITIVE.NON_NEGATIVE_INT.INVALID');
        }
      });
    });
  });

  describe('toPrimitives_roundtrip', () => {
    it('roundtrip returns original value', () => {
      const validValues = [0, 3, 10, 50];
      
      validValues.forEach(original => {
        const seatCount = SeatCount.fromPrimitives(original);
        expect(seatCount.toPrimitives()).toBe(original);
      });
    });
  });

  describe('composition_with_NonNegativeInt', () => {
    it('uses NonNegativeInt internally without duplicating validation logic', () => {
      // This test ensures we're composing over NonNegativeInt
      const validCount = 5;
      const seatCount = SeatCount.fromPrimitives(validCount);
      
      expect(seatCount.toPrimitives()).toBe(validCount);
      
      // Invalid values should throw the same error as NonNegativeInt
      expect(() => SeatCount.fromPrimitives(-1)).toThrow(InvalidNonNegativeIntError);
      expect(() => SeatCount.fromPrimitives(1.5)).toThrow(InvalidNonNegativeIntError);
    });
  });
});
