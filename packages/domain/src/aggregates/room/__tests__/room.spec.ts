/* eslint-disable @typescript-eslint/no-explicit-any */
import { Room } from '../room.aggregate';
import { RoomCapacityError, RoomStateError, RoomParticipantNotFoundError, ERR_AGGREGATE_ROOM_CAPACITY, ERR_AGGREGATE_ROOM_STATE, ERR_AGGREGATE_ROOM_PARTICIPANT_NOT_FOUND } from '../room.errors';
import { PersistenceMappingError } from '../../../errors/persistence-mapping-error';
import { Uuid } from '../../../primitives/uuid/uuid.primitive';
import { Instant } from '../../../primitives/instant/instant.primitive';
import { RoomId } from '../../../rooms/value-objects/room-id/room-id.vo';
import { RoomConfig } from '../../../rooms/value-objects/room-config/room-config.vo';
import { RoomConfigPrimitives } from '../../../rooms/types/room-config';
import { RoomPrimitives } from '../contracts/room.type';
import { SeatState } from '../../../entities/seat/contracts/seat-state.enum';
import { RoomState } from '../contracts/room-state.enum';

describe('Room', () => {
  const validRoomId = '550e8400-e29b-41d4-a716-************';
  const validParticipantId1 = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';
  const validParticipantId2 = '6ba7b810-9dad-41d1-80b4-00c04fd430c8';
  const baseTimestamp = 1640995200000; // 2022-01-01 00:00:00 UTC
  const laterTimestamp = 1640995260000; // 1 minute later
  const evenLaterTimestamp = 1640995320000; // 2 minutes later

  const validRoomConfig: RoomConfigPrimitives = {
    minSeats: 2,
    maxSeats: 4,
    avoidSingleton: true,
  };

  const validRoomDto: RoomPrimitives = {
    roomId: validRoomId,
    config: validRoomConfig,
    seats: [
      { seatNo: 0, state: SeatState.EMPTY },
      { seatNo: 1, state: SeatState.EMPTY },
      { seatNo: 2, state: SeatState.EMPTY },
      { seatNo: 3, state: SeatState.EMPTY },
    ],
    state: RoomState.FILLING,
    createdAt: baseTimestamp,
  };

  describe('fromPrimitives', () => {
    it('creates room from valid DTO', () => {
      const room = Room.fromPrimitives(validRoomDto);
      
      expect(room).toBeInstanceOf(Room);
      expect(room.roomId.toPrimitives()).toBe(validRoomId);
      expect(room.currentState).toBe(RoomState.FILLING);
      expect(room.isFilling).toBe(true);
      expect(room.isReady).toBe(false);
      expect(room.isClosed).toBe(false);
      expect(room.size).toBe(0);
      expect(room.hasSpace).toBe(true);
      expect(room.allSeats).toHaveLength(4);
      expect(room.availableSeats).toHaveLength(4);
      expect(room.occupiedSeats).toHaveLength(0);
      expect(room.reservedSeats).toHaveLength(0);
    });

    it('creates room with occupied seats', () => {
      const roomWithOccupiedSeats: RoomPrimitives = {
        ...validRoomDto,
        seats: [
          { seatNo: 0, state: SeatState.OCCUPIED, participantId: validParticipantId1, assignedAt: baseTimestamp },
          { seatNo: 1, state: SeatState.OCCUPIED, participantId: validParticipantId2, assignedAt: baseTimestamp },
          { seatNo: 2, state: SeatState.EMPTY },
          { seatNo: 3, state: SeatState.EMPTY },
        ],
        state: RoomState.READY,
      };
      
      const room = Room.fromPrimitives(roomWithOccupiedSeats);
      
      expect(room.currentState).toBe(RoomState.READY);
      expect(room.isReady).toBe(true);
      expect(room.size).toBe(2);
      expect(room.hasSpace).toBe(true);
      expect(room.occupiedSeats).toHaveLength(2);
      expect(room.availableSeats).toHaveLength(2);
    });

    it('throws PersistenceMappingError for null DTO', () => {
      expect(() => Room.fromPrimitives(null as any)).toThrow(PersistenceMappingError);
    });

    it('throws PersistenceMappingError for invalid roomId', () => {
      const invalidDto = { ...validRoomDto, roomId: 'invalid-uuid' };
      expect(() => Room.fromPrimitives(invalidDto)).toThrow(PersistenceMappingError);
    });

    it('throws PersistenceMappingError for invalid state', () => {
      const invalidDto = { ...validRoomDto, state: 'INVALID_STATE' as any };
      expect(() => Room.fromPrimitives(invalidDto)).toThrow(PersistenceMappingError);
    });

    it('throws PersistenceMappingError for invalid timestamp', () => {
      const invalidDto = { ...validRoomDto, createdAt: -1 };
      expect(() => Room.fromPrimitives(invalidDto)).toThrow(PersistenceMappingError);
    });
  });

  describe('create', () => {
    it('creates new room in FILLING state with empty seats', () => {
      const roomId = RoomId.fromPrimitives(validRoomId);
      const config = RoomConfig.fromPrimitives(validRoomConfig);
      const createdAt = Instant.fromPrimitives(baseTimestamp);
      
      const room = Room.create(roomId, config, createdAt);
      
      expect(room.roomId).toBe(roomId);
      expect(room.config).toBe(config);
      expect(room.createdAt).toBe(createdAt);
      expect(room.currentState).toBe(RoomState.FILLING);
      expect(room.isFilling).toBe(true);
      expect(room.size).toBe(0);
      expect(room.hasSpace).toBe(true);
      expect(room.allSeats).toHaveLength(4); // maxSeats from config
      expect(room.availableSeats).toHaveLength(4);
      expect(room.occupiedSeats).toHaveLength(0);
    });
  });

  describe('toPrimitives', () => {
    it('roundtrip preserves data', () => {
      const original = validRoomDto;
      const room = Room.fromPrimitives(original);
      const result = room.toPrimitives();
      
      expect(result.roomId).toBe(original.roomId);
      expect(result.config).toEqual(original.config);
      expect(result.state).toBe(original.state);
      expect(result.createdAt).toBe(original.createdAt);
      expect(result.seats).toHaveLength(original.seats.length);
    });
  });

  describe('assignParticipant', () => {
    it('assigns participant to first available seat', () => {
      const room = Room.fromPrimitives(validRoomDto);
      const participantId = Uuid.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(baseTimestamp);
      
      const updatedRoom = room.assignParticipantToSeat(participantId, at);
      
      expect(updatedRoom.size).toBe(1);
      expect(updatedRoom.occupiedSeats).toHaveLength(1);
      expect(updatedRoom.availableSeats).toHaveLength(3);
      expect(updatedRoom.hasSpace).toBe(true);
      expect(updatedRoom).not.toBe(room); // Immutable
      
      const occupiedSeat = updatedRoom.occupiedSeats[0];
      expect(occupiedSeat.currentParticipantId).toBe(validParticipantId1);
      expect(occupiedSeat.seatNo.toPrimitives()).toBe(0); // First seat
    });

    it('assigns multiple participants to different seats', () => {
      const room = Room.fromPrimitives(validRoomDto);
      const participant1 = Uuid.fromPrimitives(validParticipantId1);
      const participant2 = Uuid.fromPrimitives(validParticipantId2);
      const at = Instant.fromPrimitives(baseTimestamp);
      
      const roomWith1 = room.assignParticipantToSeat(participant1, at);
      const roomWith2 = roomWith1.assignParticipantToSeat(participant2, at);
      
      expect(roomWith2.size).toBe(2);
      expect(roomWith2.occupiedSeats).toHaveLength(2);
      expect(roomWith2.availableSeats).toHaveLength(2);
      
      const occupiedSeats = roomWith2.occupiedSeats;
      expect(occupiedSeats[0].seatNo.toPrimitives()).toBe(0);
      expect(occupiedSeats[1].seatNo.toPrimitives()).toBe(1);
    });

    it('throws RoomCapacityError when room is closed', () => {
      const closedRoomDto = { ...validRoomDto, state: RoomState.CLOSED };
      const room = Room.fromPrimitives(closedRoomDto);
      const participantId = Uuid.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(baseTimestamp);
      
      expect(() => room.assignParticipantToSeat(participantId, at)).toThrow(RoomCapacityError);
      expect(() => room.assignParticipantToSeat(participantId, at)).toThrow('Room capacity exceeded');
    });

    it('throws RoomCapacityError when room is at max capacity', () => {
      const fullRoomDto: RoomPrimitives = {
        ...validRoomDto,
        seats: [
          { seatNo: 0, state: SeatState.OCCUPIED, participantId: '550e8400-e29b-41d4-a716-************', assignedAt: baseTimestamp },
          { seatNo: 1, state: SeatState.OCCUPIED, participantId: '550e8400-e29b-41d4-a716-************', assignedAt: baseTimestamp },
          { seatNo: 2, state: SeatState.OCCUPIED, participantId: '550e8400-e29b-41d4-a716-************', assignedAt: baseTimestamp },
          { seatNo: 3, state: SeatState.OCCUPIED, participantId: '550e8400-e29b-41d4-a716-************', assignedAt: baseTimestamp },
        ],
      };
      const room = Room.fromPrimitives(fullRoomDto);
      const participantId = Uuid.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(baseTimestamp);
      
      expect(() => room.assignParticipantToSeat(participantId, at)).toThrow(RoomCapacityError);
    });
  });

  describe('releaseSeatByParticipant', () => {
    it('releases participant seat', () => {
      const roomWithParticipant: RoomPrimitives = {
        ...validRoomDto,
        seats: [
          { seatNo: 0, state: SeatState.OCCUPIED, participantId: validParticipantId1, assignedAt: baseTimestamp },
          { seatNo: 1, state: SeatState.EMPTY },
          { seatNo: 2, state: SeatState.EMPTY },
          { seatNo: 3, state: SeatState.EMPTY },
        ],
      };
      const room = Room.fromPrimitives(roomWithParticipant);
      const participantId = Uuid.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(laterTimestamp);

      const updatedRoom = room.releaseSeatOfParticipant(participantId, at);

      expect(updatedRoom.size).toBe(0);
      expect(updatedRoom.occupiedSeats).toHaveLength(0);
      expect(updatedRoom.availableSeats).toHaveLength(4);
      expect(updatedRoom).not.toBe(room); // Immutable
    });

    it('returns same instance when participant not found (no-op)', () => {
      const room = Room.fromPrimitives(validRoomDto);
      const participantId = Uuid.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(baseTimestamp);

      const updatedRoom = room.releaseSeatOfParticipant(participantId, at);

      expect(updatedRoom).toBe(room); // Same instance for no-op
    });
  });

  describe('reserveSeatForReconnect', () => {
    it('reserves seat for reconnect', () => {
      const roomWithParticipant: RoomPrimitives = {
        ...validRoomDto,
        seats: [
          { seatNo: 0, state: SeatState.OCCUPIED, participantId: validParticipantId1, assignedAt: baseTimestamp },
          { seatNo: 1, state: SeatState.EMPTY },
          { seatNo: 2, state: SeatState.EMPTY },
          { seatNo: 3, state: SeatState.EMPTY },
        ],
      };
      const room = Room.fromPrimitives(roomWithParticipant);
      const participantId = Uuid.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(laterTimestamp);

      const updatedRoom = room.reserveSeatForReconnect(participantId, at);

      expect(updatedRoom.size).toBe(1); // Still counts as occupied
      expect(updatedRoom.occupiedSeats).toHaveLength(0);
      expect(updatedRoom.reservedSeats).toHaveLength(1);
      expect(updatedRoom.availableSeats).toHaveLength(3);
      expect(updatedRoom).not.toBe(room); // Immutable
    });

    it('throws RoomParticipantNotFoundError when participant not found', () => {
      const room = Room.fromPrimitives(validRoomDto);
      const participantId = Uuid.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(baseTimestamp);

      expect(() => room.reserveSeatForReconnect(participantId, at)).toThrow(RoomParticipantNotFoundError);
      expect(() => room.reserveSeatForReconnect(participantId, at)).toThrow('Participant not found in room');
    });
  });

  describe('restoreSeatAfterReconnect', () => {
    it('restores seat after reconnect', () => {
      const roomWithReservedSeat: RoomPrimitives = {
        ...validRoomDto,
        seats: [
          {
            seatNo: 0,
            state: SeatState.RESERVED_FOR_RECONNECT,
            participantId: validParticipantId1,
            assignedAt: baseTimestamp,
            reservedSince: laterTimestamp
          },
          { seatNo: 1, state: SeatState.EMPTY },
          { seatNo: 2, state: SeatState.EMPTY },
          { seatNo: 3, state: SeatState.EMPTY },
        ],
      };
      const room = Room.fromPrimitives(roomWithReservedSeat);
      const participantId = Uuid.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(laterTimestamp + 30000); // Within deadline

      const updatedRoom = room.restoreSeatAfterReconnect(participantId, at);

      expect(updatedRoom.size).toBe(1);
      expect(updatedRoom.occupiedSeats).toHaveLength(1);
      expect(updatedRoom.reservedSeats).toHaveLength(0);
      expect(updatedRoom.availableSeats).toHaveLength(3);
      expect(updatedRoom).not.toBe(room); // Immutable
    });

    it('throws RoomParticipantNotFoundError when participant not in reconnect state', () => {
      const room = Room.fromPrimitives(validRoomDto);
      const participantId = Uuid.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(baseTimestamp);

      expect(() => room.restoreSeatAfterReconnect(participantId, at)).toThrow(RoomParticipantNotFoundError);
      expect(() => room.restoreSeatAfterReconnect(participantId, at)).toThrow('Participant not found in reconnect state');
    });
  });

  describe('makeReady', () => {
    it('transitions from FILLING to READY when minimum seats reached', () => {
      const roomWithMinSeats: RoomPrimitives = {
        ...validRoomDto,
        seats: [
          { seatNo: 0, state: SeatState.OCCUPIED, participantId: validParticipantId1, assignedAt: baseTimestamp },
          { seatNo: 1, state: SeatState.OCCUPIED, participantId: validParticipantId2, assignedAt: baseTimestamp },
          { seatNo: 2, state: SeatState.EMPTY },
          { seatNo: 3, state: SeatState.EMPTY },
        ],
      };
      const room = Room.fromPrimitives(roomWithMinSeats);

      const readyRoom = room.makeReady();

      expect(readyRoom.currentState).toBe(RoomState.READY);
      expect(readyRoom.isReady).toBe(true);
      expect(readyRoom.isFilling).toBe(false);
      expect(readyRoom).not.toBe(room); // Immutable
    });

    it('throws RoomStateError when not in FILLING state', () => {
      const readyRoomDto = { ...validRoomDto, state: RoomState.READY };
      const room = Room.fromPrimitives(readyRoomDto);

      expect(() => room.makeReady()).toThrow(RoomStateError);
      expect(() => room.makeReady()).toThrow('Cannot make room ready unless in FILLING state');
    });

    it('throws RoomStateError when insufficient participants', () => {
      const roomWithOneParticipant: RoomPrimitives = {
        ...validRoomDto,
        seats: [
          { seatNo: 0, state: SeatState.OCCUPIED, participantId: validParticipantId1, assignedAt: baseTimestamp },
          { seatNo: 1, state: SeatState.EMPTY },
          { seatNo: 2, state: SeatState.EMPTY },
          { seatNo: 3, state: SeatState.EMPTY },
        ],
      };
      const room = Room.fromPrimitives(roomWithOneParticipant);

      expect(() => room.makeReady()).toThrow(RoomStateError);
      expect(() => room.makeReady()).toThrow('Cannot make room ready with insufficient participants');
    });
  });

  describe('makeClosed', () => {
    it('transitions from FILLING to CLOSED', () => {
      const room = Room.fromPrimitives(validRoomDto);

      const closedRoom = room.makeClosed();

      expect(closedRoom.currentState).toBe(RoomState.CLOSED);
      expect(closedRoom.isClosed).toBe(true);
      expect(closedRoom.hasSpace).toBe(false);
      expect(closedRoom).not.toBe(room); // Immutable
    });

    it('transitions from READY to CLOSED', () => {
      const readyRoomDto = { ...validRoomDto, state: RoomState.READY };
      const room = Room.fromPrimitives(readyRoomDto);

      const closedRoom = room.makeClosed();

      expect(closedRoom.currentState).toBe(RoomState.CLOSED);
      expect(closedRoom.isClosed).toBe(true);
    });

    it('throws RoomStateError when already closed', () => {
      const closedRoomDto = { ...validRoomDto, state: RoomState.CLOSED };
      const room = Room.fromPrimitives(closedRoomDto);

      expect(() => room.makeClosed()).toThrow(RoomStateError);
      expect(() => room.makeClosed()).toThrow('Room is already closed');
    });
  });

  describe('state getters', () => {
    it('isFilling returns correct state', () => {
      const filling = Room.fromPrimitives(validRoomDto);
      expect(filling.isFilling).toBe(true);
      expect(filling.isReady).toBe(false);
      expect(filling.isClosed).toBe(false);
    });

    it('isReady returns correct state', () => {
      const readyDto = { ...validRoomDto, state: RoomState.READY };
      const ready = Room.fromPrimitives(readyDto);
      expect(ready.isFilling).toBe(false);
      expect(ready.isReady).toBe(true);
      expect(ready.isClosed).toBe(false);
    });

    it('isClosed returns correct state', () => {
      const closedDto = { ...validRoomDto, state: RoomState.CLOSED };
      const closed = Room.fromPrimitives(closedDto);
      expect(closed.isFilling).toBe(false);
      expect(closed.isReady).toBe(false);
      expect(closed.isClosed).toBe(true);
    });

    it('hasSpace returns correct value', () => {
      const filling = Room.fromPrimitives(validRoomDto);
      expect(filling.hasSpace).toBe(true);

      const closed = Room.fromPrimitives({ ...validRoomDto, state: RoomState.CLOSED });
      expect(closed.hasSpace).toBe(false);
    });
  });

  describe('error context preservation', () => {
    it('preserves context in RoomCapacityError', () => {
      const closedRoomDto = { ...validRoomDto, state: RoomState.CLOSED };
      const room = Room.fromPrimitives(closedRoomDto);
      const participantId = Uuid.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(baseTimestamp);

      try {
        room.assignParticipantToSeat(participantId, at);
      } catch (error) {
        expect(error).toBeInstanceOf(RoomCapacityError);
        expect((error as RoomCapacityError).code).toBe(ERR_AGGREGATE_ROOM_CAPACITY);
        expect((error as RoomCapacityError).ctx?.roomId).toBe(validRoomId);
        expect((error as RoomCapacityError).ctx?.participantId).toBe(validParticipantId1);
      }
    });

    it('preserves context in RoomStateError', () => {
      const readyRoomDto = { ...validRoomDto, state: RoomState.READY };
      const room = Room.fromPrimitives(readyRoomDto);

      try {
        room.makeReady();
      } catch (error) {
        expect(error).toBeInstanceOf(RoomStateError);
        expect((error as RoomStateError).code).toBe(ERR_AGGREGATE_ROOM_STATE);
        expect((error as RoomStateError).ctx?.currentState).toBe(RoomState.READY);
        expect((error as RoomStateError).ctx?.roomId).toBe(validRoomId);
      }
    });

    it('preserves context in RoomParticipantNotFoundError', () => {
      const room = Room.fromPrimitives(validRoomDto);
      const participantId = Uuid.fromPrimitives(validParticipantId1);
      const at = Instant.fromPrimitives(baseTimestamp);

      try {
        room.reserveSeatForReconnect(participantId, at);
      } catch (error) {
        expect(error).toBeInstanceOf(RoomParticipantNotFoundError);
        expect((error as RoomParticipantNotFoundError).code).toBe(ERR_AGGREGATE_ROOM_PARTICIPANT_NOT_FOUND);
        expect((error as RoomParticipantNotFoundError).ctx?.roomId).toBe(validRoomId);
        expect((error as RoomParticipantNotFoundError).ctx?.participantId).toBe(validParticipantId1);
      }
    });
  });

  describe('complete room lifecycle', () => {
    it('follows complete FILLING → READY → CLOSED lifecycle', () => {
      const roomId = RoomId.fromPrimitives(validRoomId);
      const config = RoomConfig.fromPrimitives(validRoomConfig);
      const createdAt = Instant.fromPrimitives(baseTimestamp);

      // Create in FILLING state
      let room = Room.create(roomId, config, createdAt);
      expect(room.isFilling).toBe(true);
      expect(room.size).toBe(0);

      // Assign participants
      const participant1 = Uuid.fromPrimitives(validParticipantId1);
      const participant2 = Uuid.fromPrimitives(validParticipantId2);
      const at = Instant.fromPrimitives(baseTimestamp);

      room = room.assignParticipantToSeat(participant1, at);
      expect(room.size).toBe(1);
      expect(room.isFilling).toBe(true); // Still filling

      room = room.assignParticipantToSeat(participant2, at);
      expect(room.size).toBe(2);
      expect(room.isFilling).toBe(true); // Still filling until makeReady called

      // Make ready: FILLING → READY
      room = room.makeReady();
      expect(room.isReady).toBe(true);
      expect(room.size).toBe(2);

      // Close room: READY → CLOSED
      room = room.makeClosed();
      expect(room.isClosed).toBe(true);
      expect(room.hasSpace).toBe(false);
      expect(room.size).toBe(2); // Participants still there
    });

    it('handles seat reservation and reconnection lifecycle', () => {
      const roomId = RoomId.fromPrimitives(validRoomId);
      const config = RoomConfig.fromPrimitives(validRoomConfig);
      const createdAt = Instant.fromPrimitives(baseTimestamp);

      let room = Room.create(roomId, config, createdAt);
      const participantId = Uuid.fromPrimitives(validParticipantId1);

      // Assign participant
      room = room.assignParticipantToSeat(participantId, Instant.fromPrimitives(baseTimestamp));
      expect(room.occupiedSeats).toHaveLength(1);
      expect(room.reservedSeats).toHaveLength(0);

      // Reserve for reconnect
      room = room.reserveSeatForReconnect(participantId, Instant.fromPrimitives(laterTimestamp));
      expect(room.occupiedSeats).toHaveLength(0);
      expect(room.reservedSeats).toHaveLength(1);
      expect(room.size).toBe(1); // Still counts toward size

      // Restore after reconnect
      room = room.restoreSeatAfterReconnect(participantId, Instant.fromPrimitives(laterTimestamp + 30000));
      expect(room.occupiedSeats).toHaveLength(1);
      expect(room.reservedSeats).toHaveLength(0);
      expect(room.size).toBe(1);

      // Release seat
      room = room.releaseSeatOfParticipant(participantId, Instant.fromPrimitives(evenLaterTimestamp));
      expect(room.occupiedSeats).toHaveLength(0);
      expect(room.reservedSeats).toHaveLength(0);
      expect(room.size).toBe(0);
    });
  });
});
