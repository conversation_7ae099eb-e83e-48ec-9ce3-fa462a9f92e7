import { ensure } from '../../support/ensure';
import { Instant } from '../../primitives/instant/instant.primitive';
import { RoomId } from '../../rooms/value-objects/room-id/room-id.vo';
import { RoomConfig } from '../../rooms/value-objects/room-config/room-config.vo';
import { Seat } from '../../entities/seat/seat.entity';
import { PersistenceMappingError } from '../../errors/persistence-mapping-error';
import {
  RoomCapacityError,
  RoomStateError,
  RoomParticipantNotFoundError,
} from './room.errors';
import { SeatPrimitives } from '../../entities/seat/contracts/seat.type';
import { SeatState } from '../../entities/seat/contracts/seat-state.enum';
import { RoomState } from './contracts/room-state.enum';
import { RoomPrimitives } from './contracts/room.type';
import { ParticipantId } from 'participants/value-objects/participant-id/participant-id.vo';

export class Room {
  private constructor(
    public readonly roomId: RoomId,
    public readonly config: RoomConfig,
    private seats: Seat[],
    private state: RoomState,
    public readonly createdAt: Instant,
  ) {}

  static fromPrimitives(dto: RoomPrimitives): Room {
    try {
      ensure(
        dto != null,
        new PersistenceMappingError('Room DTO is null or undefined'),
      );

      ensure(
        dto.seats.length === dto.config.maxSeats,
        new PersistenceMappingError(
          'Number of seats does not match max seats',
        ),
      );

      ensure(
        Object.values(RoomState).includes(dto.state),
        new PersistenceMappingError('Invalid room state', { state: dto.state }),
      )

      ensure(
  dto.seats.every(s => Number.isInteger(s.seatNo) && s.seatNo >= 0 && s.seatNo < dto.config.maxSeats),
  new PersistenceMappingError('Seat number out of range', { seatNos: dto.seats.map(s => s.seatNo) })
);
ensure(
  new Set(dto.seats.map(s => s.seatNo)).size === dto.seats.length,
  new PersistenceMappingError('Duplicate seat numbers', { seatNos: dto.seats.map(s => s.seatNo) })
);


      const roomId = RoomId.fromPrimitives(dto.roomId);
      const config = RoomConfig.fromPrimitives(dto.config);
      const createdAt = Instant.fromPrimitives(dto.createdAt);


      const seats = dto.seats.map((seatDto) => Seat.fromPrimitives(seatDto));

      return new Room(
        roomId,
        config,
        seats,
        dto.state,
        createdAt,
      );
    } catch (error) {
      if (error instanceof PersistenceMappingError) {
        throw error;
      }
      throw new PersistenceMappingError('Failed to map room from primitives', {
        originalError: error,
      });
    }
  }

  static create(roomId: RoomId, config: RoomConfig, createdAt: Instant): Room {
    // Create seats with indices 0..maxSeats-1
    const seats: Seat[] = [];
    for (let i = 0; i < config.maxSeats; i++) {
      const seatPrimitives: SeatPrimitives = {
        seatNo: i,
        state: SeatState.EMPTY,
      };
      seats.push(Seat.fromPrimitives(seatPrimitives));
    }

    return new Room(
      roomId,
      config,
      seats,
      RoomState.FILLING,
      createdAt,
    );
  }

  toPrimitives(): RoomPrimitives {
    return {
      roomId: this.roomId.toPrimitives(),
      config: this.config.toPrimitives(),
      seats: this.seats.map((seat) => seat.toPrimitives()),
      state: this.state,
      createdAt: this.createdAt.toPrimitives(),
    };
  }

  assignParticipantToSeat(participantId: ParticipantId, at: Instant): Room {
    ensure(
      this.hasSpace,
      new RoomCapacityError('Room capacity exceeded', {
        roomId: this.roomId.toPrimitives(),
        currentSize: this.size,
        maxSeats: this.config.maxSeats,
        participantId: participantId.toPrimitives(),
      }),
    );

    ensure(
      !this.seats.some(
        (s) =>
          (s.isOccupied || s.isReservedForReconnect) &&
          s.currentParticipantId === participantId.toPrimitives(),
      ),
      new RoomStateError('Participant already in room', {
        roomId: this.roomId.toPrimitives(),
        participantId: participantId.toPrimitives(),
      }),
    );

    // Find first available seat
    const availableSeatIndex = this.seats.findIndex((seat) => seat.isEmpty);

    ensure(
      availableSeatIndex !== -1,
      new RoomCapacityError('No available seats found', {
        roomId: this.roomId.toPrimitives(),
        participantId: participantId.toPrimitives(),
      }),
    );

    // Assign the seat
    const newSeats = [...this.seats];
    newSeats[availableSeatIndex] = this.seats[availableSeatIndex].assign(
      participantId,
      at,
    );

    return new Room(
      this.roomId,
      this.config,
      newSeats,
      this.state,
      this.createdAt,
    );
  }

  releaseSeatOfParticipant(participantId: ParticipantId, at: Instant): Room {
    const seatIndex = this.seats.findIndex(
      (seat) =>
        seat.isOccupied &&
        seat.currentParticipantId === participantId.toPrimitives(),
    );

    // No-op if participant not found (already released)
    if (seatIndex === -1) {
      return this;
    }

    const newSeats = [...this.seats];
    newSeats[seatIndex] = this.seats[seatIndex].release(at);

    return new Room(
      this.roomId,
      this.config,
      newSeats,
      this.state,
      this.createdAt,
    );
  }

  releaseAllReservedSeats(at: Instant): Room {
    const newSeats = this.seats.map((seat) =>
      seat.isReservedForReconnect ? seat.release(at) : seat,
    );

    return new Room(
      this.roomId,
      this.config,
      newSeats,
      this.state,
      this.createdAt,
    );
  }

  reserveSeatForReconnect(participantId: ParticipantId, at: Instant): Room {
    const seatIndex = this.seats.findIndex(
      (seat) =>
        seat.isOccupied &&
        seat.currentParticipantId === participantId.toPrimitives(),
    );

    // No-op if participant not found
    if (seatIndex === -1) {
      return this;
    }

    const newSeats = [...this.seats];
    newSeats[seatIndex] = this.seats[seatIndex].reserveForReconnect(at);

    return new Room(
      this.roomId,
      this.config,
      newSeats,
      this.state,
      this.createdAt,
    );
  }

  restoreSeatAfterReconnect(participantId: ParticipantId, at: Instant): Room {
    const seatIndex = this.seats.findIndex(
      (seat) =>
        seat.isReservedForReconnect &&
        seat.currentParticipantId === participantId.toPrimitives(),
    );

    ensure(
      seatIndex !== -1,
      new RoomParticipantNotFoundError(
        'Participant not found in reconnect state',
        {
          roomId: this.roomId.toPrimitives(),
          participantId: participantId.toPrimitives(),
        },
      ),
    );

    const newSeats = [...this.seats];

    newSeats[seatIndex] = this.seats[seatIndex].restoreAfterReconnect(
      participantId,
      at,
      this.config.disconnectionPolicy,
    );

    return new Room(
      this.roomId,
      this.config,
      newSeats,
      this.state,
      this.createdAt,
    );
  }

  makeReady(): Room {
    ensure(
      this.state === RoomState.FILLING,
      new RoomStateError('Cannot make room ready unless in FILLING state', {
        currentState: this.state,
        roomId: this.roomId.toPrimitives(),
      }),
    );

    ensure(
      this.size >= this.config.minSeats,
      new RoomStateError(
        'Cannot make room ready with insufficient participants',
        {
          currentSize: this.size,
          minSeats: this.config.minSeats,
          roomId: this.roomId.toPrimitives(),
        },
      ),
    );

    return new Room(
      this.roomId,
      this.config,
      this.seats,
      RoomState.READY,
      this.createdAt,
    );
  }

  makeClosed(): Room {
    ensure(
      this.state !== RoomState.CLOSED,
      new RoomStateError('Room is already closed', {
        currentState: this.state,
        roomId: this.roomId.toPrimitives(),
      }),
    );

     ensure(
    this.availableSeats.length === 0,
    new RoomStateError('Room cannot be closed because it is not full', {
      roomId: this.roomId.toPrimitives(),
      availableSeats: this.availableSeats.length,
      occupiedSeats: this.occupiedSeats.length,
      reservedSeats: this.reservedSeats.length,
    }),
  );

    return new Room(
      this.roomId,
      this.config,
      this.seats,
      RoomState.CLOSED,
      this.createdAt,
    );
  }

  // Derived properties
  get size(): number {
    return this.seats.filter(
      (seat) => seat.isOccupied || seat.isReservedForReconnect,
    ).length;
  }

  get hasSpace(): boolean {
    return this.state !== RoomState.CLOSED && this.size < this.config.maxSeats;
  }

  get currentState(): RoomState {
    return this.state;
  }

  get isFilling(): boolean {
    return this.state === RoomState.FILLING;
  }

  get isReady(): boolean {
    return this.state === RoomState.READY;
  }

  get isClosed(): boolean {
    return this.state === RoomState.CLOSED;
  }

  get occupiedSeats(): Seat[] {
    return this.seats.filter((seat) => seat.isOccupied);
  }

  get availableSeats(): Seat[] {
    return this.seats.filter((seat) => seat.isEmpty);
  }

  get reservedSeats(): Seat[] {
    return this.seats.filter((seat) => seat.isReservedForReconnect);
  }

  get allSeats(): Seat[] {
    return [...this.seats];
  }
}
