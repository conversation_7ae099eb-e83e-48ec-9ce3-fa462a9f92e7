import { ensure } from '../../support/ensure';
import { Uuid } from '../../primitives/uuid/uuid.primitive';
import { Instant } from '../../primitives/instant/instant.primitive';
import { SessionId } from '../../sessions/value-objects/session-id/session-id.vo';
import { SessionConfig } from '../../sessions/value-objects/session-config/session-config.vo';
import { SessionConfigPrimitives } from '../../sessions/types/session-config';
import { ParticipantId } from '../../participants/value-objects/participant-id/participant-id.vo';
import { ParticipantRole } from '../../participants/types/participant-role.enum';
import { RoomId } from '../../rooms/value-objects/room-id/room-id.vo';
import { Room } from '../room/room.aggregate';
import { RoundInstance } from '../../entities/round-instance/round-instance.entity';
import { ParticipantPresence } from '../../entities/participant-presence/presence.entity';
import { PersistenceMappingError } from '../../errors/persistence-mapping-error';
import {
  SessionStateError,
  SessionInvariantError,
  SessionCapacityError,
  SessionParticipantNotFoundError,
} from './session.errors';
import { RoomPrimitives } from '../../aggregates/room/contracts/room.type';
import { ParticipantPresencePrimitives } from '../../entities/participant-presence/contracts/participant-presence.type';
import { RoundInstancePrimitives } from '../../entities/round-instance/contracts/round-instance.type';
import { RoomConfig } from '../../rooms/value-objects/room-config/room-config.vo';

export type SessionState =
  | 'SCHEDULED'
  | 'RUNNING'
  | 'PAUSED'
  | 'COMPLETED'
  | 'CANCELED';

export type SessionPrimitives = {
  sessionId: string;
  config: SessionConfigPrimitives;
  state: SessionState;
  createdByUserId: string;
  hostId?: string;
  mainRoomId: string;
  currentRoundIndex: number;
  rounds: RoundInstancePrimitives[];
  participants: ParticipantPresencePrimitives[];
  rooms: RoomPrimitives[];
  createdAt: number;
};

/**
 * IMPROVEMENT TODO
 *
 * Avoid multiple lookups by passing instances as arguments instead of IDs.
 */

export class Session {
  private constructor(
    public readonly sessionId: SessionId,
    public readonly config: SessionConfig,
    private state: SessionState,
    public readonly createdByUserId: Uuid,
    private hostId: Uuid | undefined,
    public readonly mainRoomId: RoomId,
    private currentRoundIndex: number,
    private rounds: RoundInstance[],
    private participants: ParticipantPresence[],
    private rooms: Room[],
    public readonly createdAt: Instant,
  ) {}

  static fromPrimitives(dto: SessionPrimitives): Session {
    try {
      ensure(
        dto != null,
        new PersistenceMappingError('Session DTO is null or undefined'),
      );

      const sessionId = SessionId.fromPrimitives(dto.sessionId);
      const config = SessionConfig.fromPrimitives(dto.config);
      const createdByUserId = Uuid.fromPrimitives(dto.createdByUserId);
      const hostId = dto.hostId ? Uuid.fromPrimitives(dto.hostId) : undefined;
      const mainRoomId = RoomId.fromPrimitives(dto.mainRoomId);
      // currentRoundIndex can be -1 (no round started), so we don't use NonNegativeInt
      const currentRoundIndex = dto.currentRoundIndex;
      const createdAt = Instant.fromPrimitives(dto.createdAt);

      ensure(
        ['SCHEDULED', 'RUNNING', 'PAUSED', 'COMPLETED', 'CANCELED'].includes(
          dto.state,
        ),
        new PersistenceMappingError('Invalid session state', {
          state: dto.state,
        }),
      );

      const rounds = dto.rounds.map((roundDto) =>
        RoundInstance.fromPrimitives(roundDto),
      );
      const participants = dto.participants.map((participantDto) =>
        ParticipantPresence.fromPrimitives(participantDto),
      );
      const rooms = dto.rooms.map((roomDto) => Room.fromPrimitives(roomDto));

      return new Session(
        sessionId,
        config,
        dto.state,
        createdByUserId,
        hostId,
        mainRoomId,
        currentRoundIndex,
        rounds,
        participants,
        rooms,
        createdAt,
      );
    } catch (error) {
      if (error instanceof PersistenceMappingError) {
        throw error;
      }
      throw new PersistenceMappingError(
        'Failed to map session from primitives',
        { originalError: error },
      );
    }
  }

  static create(
    sessionId: SessionId,
    config: SessionConfig,
    adminId: Uuid,
    mainRoomId: RoomId,
    createdAt: Instant,
  ): Session {
    // Create main room

    // Create a special room config for the main room
    const mainRoomConfig = RoomConfig.fromPrimitives({
      minSeats: config.defaultRoomConfig.minSeats,
      maxSeats: config.maxParticipants, // maximum participants in a session (can be changed as needed)
      avoidSingleton: config.defaultRoomConfig.avoidSingleton,
      disconnectionPolicy: config.disconnectionPolicy,
    });

    const mainRoom = Room.create(mainRoomId, mainRoomConfig, createdAt);

    return new Session(
      sessionId,
      config,
      'SCHEDULED',
      adminId,
      undefined, // No host initially
      mainRoomId,
      -1, // Start at -1, first round will be 0
      [], // No rounds initially
      [], // No participants initially
      [mainRoom], // Start with main room
      createdAt,
    );
  }

  toPrimitives(): SessionPrimitives {
    return {
      sessionId: this.sessionId.toPrimitives(),
      config: this.config.toPrimitives(),
      state: this.state,
      createdByUserId: this.createdByUserId.toPrimitives(),
      hostId: this.hostId?.toPrimitives(),
      mainRoomId: this.mainRoomId.toPrimitives(),
      currentRoundIndex: this.currentRoundIndex,
      rounds: this.rounds.map((round) => round.toPrimitives()),
      participants: this.participants.map((participant) =>
        participant.toPrimitives(),
      ),
      rooms: this.rooms.map((room) => room.toPrimitives()),
      createdAt: this.createdAt.toPrimitives(),
    };
  }

  startSession(at: Instant): Session {
    if (this.state === 'RUNNING' || this.state === 'PAUSED') return this;

    ensure(
      this.state === 'SCHEDULED',
      new SessionStateError('Cannot start session unless in SCHEDULED state', {
        currentState: this.state,
        sessionId: this.sessionId.toPrimitives(),
      }),
    );

    return new Session(
      this.sessionId,
      this.config,
      'RUNNING',
      this.createdByUserId,
      this.hostId,
      this.mainRoomId,
      this.currentRoundIndex,
      this.rounds,
      this.participants,
      this.rooms,
      this.createdAt,
    );
  }

  pauseSession(): Session {
    if (this.state === 'PAUSED') return this;

    ensure(
      this.state === 'RUNNING',
      new SessionStateError('Cannot pause session unless in RUNNING state', {
        currentState: this.state,
        sessionId: this.sessionId.toPrimitives(),
      }),
    );

    return new Session(
      this.sessionId,
      this.config,
      'PAUSED',
      this.createdByUserId,
      this.hostId,
      this.mainRoomId,
      this.currentRoundIndex,
      this.rounds,
      this.participants,
      this.rooms,
      this.createdAt,
    );
  }

  resumeSession(): Session {
    if (this.state === 'RUNNING') return this;

    ensure(
      this.state === 'PAUSED',
      new SessionStateError('Cannot resume session unless in PAUSED state', {
        currentState: this.state,
        sessionId: this.sessionId.toPrimitives(),
      }),
    );

    return new Session(
      this.sessionId,
      this.config,
      'RUNNING',
      this.createdByUserId,
      this.hostId,
      this.mainRoomId,
      this.currentRoundIndex,
      this.rounds,
      this.participants,
      this.rooms,
      this.createdAt,
    );
  }

  completeSession(): Session {
    if (this.state === 'COMPLETED' || this.state === 'CANCELED') return this;
    ensure(
      this.state === 'RUNNING' || this.state === 'PAUSED',
      new SessionStateError(
        'Cannot complete session unless in RUNNING or PAUSED state',
        {
          currentState: this.state,
          sessionId: this.sessionId.toPrimitives(),
        },
      ),
    );

    return new Session(
      this.sessionId,
      this.config,
      'COMPLETED',
      this.createdByUserId,
      this.hostId,
      this.mainRoomId,
      this.currentRoundIndex,
      this.rounds,
      this.participants,
      this.rooms,
      this.createdAt,
    );
  }

  cancelSession(): Session {
    ensure(
      this.state !== 'COMPLETED' && this.state !== 'CANCELED',
      new SessionStateError('Cannot cancel a completed session', {
        currentState: this.state,
        sessionId: this.sessionId.toPrimitives(),
      }),
    );

    return new Session(
      this.sessionId,
      this.config,
      'CANCELED',
      this.createdByUserId,
      this.hostId,
      this.mainRoomId,
      this.currentRoundIndex,
      this.rounds,
      this.participants,
      this.rooms,
      this.createdAt,
    );
  }

  // Getters for state inspection
  get currentState(): SessionState {
    return this.state;
  }

  get isScheduled(): boolean {
    return this.state === 'SCHEDULED';
  }

  get isRunning(): boolean {
    return this.state === 'RUNNING';
  }

  get isPaused(): boolean {
    return this.state === 'PAUSED';
  }

  get isCompleted(): boolean {
    return this.state === 'COMPLETED';
  }

  get isCanceled(): boolean {
    return this.state === 'CANCELED';
  }

  get currentRound(): RoundInstance | undefined {
    if (
      this.currentRoundIndex < 0 ||
      this.currentRoundIndex >= this.rounds.length
    ) {
      return undefined;
    }
    return this.rounds[this.currentRoundIndex];
  }

  get allRounds(): RoundInstance[] {
    return [...this.rounds];
  }

  get allParticipants(): ParticipantPresence[] {
    return [...this.participants];
  }

  get allRooms(): Room[] {
    return [...this.rooms];
  }

  get participantCount(): number {
    return this.participants.length;
  }

  get hasHost(): boolean {
    return this.hostId != null;
  }

  addParticipant(
    participantId: ParticipantId,
    at: Instant,
    participantRole: ParticipantRole = ParticipantRole.MEMBER,
  ): Session {
    // Check if participant already exists (rejoin after disconnect)
    const existingParticipant = this.participants.find(
      (p) => p.participantId.toPrimitives() === participantId.toPrimitives(),
    );

    if (existingParticipant) {
      // No-op
      return this;
    }

    const updatedParticipants = [
      ...this.participants,
      ParticipantPresence.create(participantId, participantRole),
    ];

    return new Session(
      this.sessionId,
      this.config,
      this.state,
      this.createdByUserId,
      this.hostId,
      this.mainRoomId,
      this.currentRoundIndex,
      this.rounds,
      updatedParticipants,
      this.rooms,
      this.createdAt,
    );
  }

  // Participant management methods
  onJoin(
    participantId: ParticipantId,
    at: Instant,
    participantRole: ParticipantRole = ParticipantRole.MEMBER,
  ): Session {
    // 1. Verify session is running

    ensure(
      this.state === 'RUNNING',
      new SessionStateError(
        'Cannot join participant unless session is RUNNING',
        {
          currentState: this.state,
          sessionId: this.sessionId.toPrimitives(),
          participantId: participantId.toPrimitives(),
        },
      ),
    );

    // 2. Verify session capacity hasn't been exceeded

    ensure(
      this.participantCount < this.config.maxParticipants,
      new SessionCapacityError('Session capacity exceeded', {
        currentCount: this.participantCount,
        maxParticipants: this.config.maxParticipants,
        sessionId: this.sessionId.toPrimitives(),
        participantId: participantId.toPrimitives(),
      }),
    );

    // Check if participant already exists (rejoin after disconnect)
    const existingParticipant = this.participants.find(
      (p) => p.participantId.toPrimitives() === participantId.toPrimitives(),
    );

    if (existingParticipant) {
      // Probably a reconnect, try to restore the participant to their seat

      try {
        if (!this.rounds.some((r) => r.isActive)) {
          return this.assignParticipantToRoom(
            participantId,
            this.mainRoomId,
            at,
          );
        }
        return this.restoreReservedSeat(participantId, at);

        // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (_) {
        // If restore fails, treat it as a new join

        // Add the participant to the participant presence first, then seat them
        return this.addParticipant(
          participantId,
          at,
          participantRole,
        ).seatParticipant(participantId, at);
      }
    }

    // Add the participant to the participant presence first, then seat them
    return this.addParticipant(
      participantId,
      at,
      participantRole,
    ).seatParticipant(participantId, at);
  }

  seatParticipant(participantId: ParticipantId, at: Instant): Session {
    /**
     * Seat the new participant in a room
     *
     * Cases:
     *
     * 1. No round is active
     *    Seat the participant in the main room
     *
     * 2. A round is active
     *    1. Find an existing room with space
     *    2. Create a new room if none exists
     *    3. Assign the participant to the room
     */

    ensure(
      this.state === 'RUNNING',
      new SessionStateError(
        'Cannot seat participant unless session is RUNNING',
        {
          currentState: this.state,
          sessionId: this.sessionId.toPrimitives(),
          participantId: participantId.toPrimitives(),
        },
      ),
    );

    ensure(
      this.participants.some(
        (p) => p.participantId.toPrimitives() === participantId.toPrimitives(),
      ),
      new SessionParticipantNotFoundError('Participant not found', {
        participantId: participantId.toPrimitives(),
        sessionId: this.sessionId.toPrimitives(),
      }),
    );

    if (this.currentRoundIndex < 0) {
      // No round is active, seat in main room
      return this.assignParticipantToRoom(participantId, this.mainRoomId, at);
    }

    // eslint-disable-next-line no-useless-catch
    try {
      // A round is active, find or create a room with space
      let targetRoom: Room | undefined = undefined;

      // Fill room progressively
      const candidateRooms = this.rooms
        .filter(
          (room) =>
            room.roomId.toPrimitives() !== this.mainRoomId.toPrimitives(),
        )
        .filter((room) => room.hasSpace)
        .sort(
          (a, b) =>
            a.occupiedSeats.length +
            a.reservedSeats.length -
            (b.occupiedSeats.length + b.reservedSeats.length),
        );

      if (candidateRooms.length > 0) {
        targetRoom = candidateRooms[0];
      } else {
        const newRoomId = RoomId.fromPrimitives(Uuid.generate());
        const sessionWithNewRoom = this.createRoom(newRoomId, at);
        targetRoom = sessionWithNewRoom.findRoomById(newRoomId);

        ensure(
          targetRoom,
          new SessionInvariantError('New room creation failed', {
            sessionId: this.sessionId.toPrimitives(),
            newRoomId: newRoomId.toPrimitives(),
          }),
        );

        return sessionWithNewRoom.assignParticipantToRoom(
          participantId,
          targetRoom.roomId,
          at,
        );
      }

      ensure(
        targetRoom,
        new SessionInvariantError('No target room found', {
          sessionId: this.sessionId.toPrimitives(),
          participantId: participantId.toPrimitives(),
        }),
      );

      // Assign participant to the target room
      return this.assignParticipantToRoom(participantId, targetRoom.roomId, at);
    } catch (error) {
      // Handle some edge cases : room full, etc.
      // Maybe consider the next room (in the nextroomtofills array)

      // for now, let us throw the error
      throw error;
    }
  }

  onLeave(participantId: ParticipantId, at: Instant): Session {
    const existingParticipant = this.participants.find(
      (p) => p.participantId.toPrimitives() === participantId.toPrimitives(),
    );

    if (!existingParticipant) {
      // No-op if participant doesn't exist
      return this;
    }

    // Update participant's leave time
    const updatedParticipants = this.participants.map((p) =>
      p.participantId.toPrimitives() === participantId.toPrimitives()
        ? p.exitRoom(at)
        : p,
    );

    /**
     * We are not going to directly release the seat of the participant when they leave.
     *
     * Instead we will rely on the reconnection grace period to release the seat.
     *
     *
     * When the participant tries to reconnect within the grace period, we will restore their seat.
     *
     * If the participant does not reconnect within the grace period, the seat will be released automatically.
     */

    // Reserve seat for reconnection if participant has a seat in a room

    const currentParticipantRoom = this.rooms.find(
      (room) =>
        room.roomId.toPrimitives() === existingParticipant.currentRoomId,
    );

    // Update rooms
    const roomsAfterReservation = currentParticipantRoom
      ? this.rooms.map((room) =>
          room.roomId.toPrimitives() ===
          currentParticipantRoom.roomId.toPrimitives()
            ? currentParticipantRoom.reserveSeatForReconnect(participantId, at)
            : // TODO: Start a timer to release the seat if the participant does not reconnect within the grace period
              room,
        )
      : this.rooms;

    return new Session(
      this.sessionId,
      this.config,
      this.state,
      this.createdByUserId,
      this.hostId,
      this.mainRoomId,
      this.currentRoundIndex,
      this.rounds,
      updatedParticipants,
      roomsAfterReservation,
      this.createdAt,
    );
  }

  assignParticipantToRoom(
    participantId: ParticipantId,
    targetRoomId: RoomId,
    at: Instant,
  ): Session {
    ensure(
      this.state === 'RUNNING',
      new SessionStateError(
        'Cannot assign participant to room unless session is RUNNING',
        {
          currentState: this.state,
          sessionId: this.sessionId.toPrimitives(),
          participantId: participantId.toPrimitives(),
          targetRoomId: targetRoomId.toPrimitives(),
        },
      ),
    );

    //Check if a seat is reserved for the participant in their current room
    const hasReservation = this.rooms.some((r) =>
      r.allSeats.some(
        (seat) =>
          seat.currentParticipantId === participantId.toPrimitives() &&
          seat.isReservedForReconnect,
      ),
    );

    ensure(
      !hasReservation,
      new SessionInvariantError(
        'Cannot move participant to another room while their seat is reserved for reconnect',
        {
          participantId: participantId.toPrimitives(),
          sessionId: this.sessionId.toPrimitives(),
        },
      ),
    );

    // Check if participant exists in a room and get their current room, if they exist.
    const participantCurrentRoom = this.rooms.find((r) =>
      r.allSeats.some(
        (seat) => seat.currentParticipantId === participantId.toPrimitives(),
      ),
    );

    if (
      participantCurrentRoom?.roomId.toPrimitives() ===
      targetRoomId.toPrimitives()
    ) {
      return this; // or no-op/ensure
    }

    // Update participant presence
    const updatedParticipants = this.participants.map((p) => {
      if (p.participantId.toPrimitives() === participantId.toPrimitives()) {
        if (participantCurrentRoom) {
          const _p = p.exitRoom(at);

          return _p.enterRoom(targetRoomId, at);
        } else {
          return p.enterRoom(targetRoomId, at);
        }
      } else {
        return p;
      }
    });

    // Update rooms
    const updatedRooms = this.rooms.map((room) => {
      if (room.roomId.toPrimitives() === targetRoomId.toPrimitives()) {
        return room.assignParticipantToSeat(participantId, at);
      } else if (
        participantCurrentRoom &&
        room.roomId.toPrimitives() ===
          participantCurrentRoom.roomId.toPrimitives()
      ) {
        return participantCurrentRoom?.releaseSeatOfParticipant(
          participantId,
          at,
        );
      } else {
        return room;
      }
    });

    return new Session(
      this.sessionId,
      this.config,
      this.state,
      this.createdByUserId,
      this.hostId,
      this.mainRoomId,
      this.currentRoundIndex,
      this.rounds,
      updatedParticipants,
      updatedRooms,
      this.createdAt,
    );
  }

  restoreReservedSeat(participantId: ParticipantId, at: Instant): Session {
    ensure(
      this.state === 'RUNNING',
      new SessionStateError('Cannot restore seat unless session is RUNNING', {
        currentState: this.state,
        sessionId: this.sessionId.toPrimitives(),
        participantId: participantId.toPrimitives(),
      }),
    );

    const reservedParticipantRoom = this.rooms.find((r) =>
      r.allSeats.some(
        (seat) =>
          seat.isReservedForReconnect &&
          seat.currentParticipantId === participantId.toPrimitives(),
      ),
    );

    ensure(
      reservedParticipantRoom,
      new SessionInvariantError('Participant current room not found', {
        participantId: participantId.toPrimitives(),
        sessionId: this.sessionId.toPrimitives(),
      }),
    );

    // Update rooms
    const updatedRooms = this.rooms.map((room) => {
      if (
        room.roomId.toPrimitives() ===
        reservedParticipantRoom.roomId.toPrimitives()
      ) {
        return reservedParticipantRoom.restoreSeatAfterReconnect(
          participantId,
          at,
        );
      } else {
        return room;
      }
    });

    // Update participants
    const updatedParticipants = this.participants.map((p) =>
      p.participantId.toPrimitives() === participantId.toPrimitives()
        ? p.enterRoom(reservedParticipantRoom.roomId, at)
        : p,
    );

    return new Session(
      this.sessionId,
      this.config,
      this.state,
      this.createdByUserId,
      this.hostId,
      this.mainRoomId,
      this.currentRoundIndex,
      this.rounds,
      updatedParticipants,
      updatedRooms,
      this.createdAt,
    );
  }

  setHost(hostId: Uuid): Session {
    if (this.hostId?.toPrimitives() === hostId.toPrimitives()) return this;

    const participant = this.participants.find(
      (p) => p.participantId.toPrimitives() === hostId.toPrimitives(),
    );

    ensure(
      participant,
      new SessionInvariantError('Host must be a participant', {
        hostId: hostId.toPrimitives(),
        sessionId: this.sessionId.toPrimitives(),
      }),
    );

    ensure(
      participant.currentTags.includes(`role-${ParticipantRole.HOST}`),
      new SessionInvariantError('Host must have HOST role', {
        hostId: hostId.toPrimitives(),
        sessionId: this.sessionId.toPrimitives(),
      }),
    );

    return new Session(
      this.sessionId,
      this.config,
      this.state,
      this.createdByUserId,
      hostId,
      this.mainRoomId,
      this.currentRoundIndex,
      this.rounds,
      this.participants,
      this.rooms,
      this.createdAt,
    );
  }

  // Room management methods
  createRoom(roomId: RoomId, at: Instant): Session {
    ensure(
      this.state === 'RUNNING',
      new SessionStateError('Cannot create room unless session is RUNNING', {
        currentState: this.state,
        sessionId: this.sessionId.toPrimitives(),
        roomId: roomId.toPrimitives(),
      }),
    );

    // Check if room already exists
    const existingRoom = this.rooms.find(
      (r) => r.roomId.toPrimitives() === roomId.toPrimitives(),
    );

    if (existingRoom) {
      // No-op if room already exists
      return this;
    }

    const newRoom = Room.create(roomId, this.config.defaultRoomConfig, at);
    const updatedRooms = [...this.rooms, newRoom];

    return new Session(
      this.sessionId,
      this.config,
      this.state,
      this.createdByUserId,
      this.hostId,
      this.mainRoomId,
      this.currentRoundIndex,
      this.rounds,
      this.participants,
      updatedRooms,
      this.createdAt,
    );
  }

  // Round management methods
  addRound(round: RoundInstance): Session {
    if (
      this.rounds.some(
        (r) => r.roundId.toPrimitives() === round.roundId.toPrimitives(),
      )
    )
      return this;

    ensure(
      this.state === 'SCHEDULED',
      new SessionStateError('Cannot add round unless session is SCHEDULED', {
        currentState: this.state,
        sessionId: this.sessionId.toPrimitives(),
        roundId: round.roundId.toPrimitives(),
      }),
    );

    const updatedRounds = [...this.rounds, round];

    return new Session(
      this.sessionId,
      this.config,
      this.state,
      this.createdByUserId,
      this.hostId,
      this.mainRoomId,
      this.currentRoundIndex,
      updatedRounds,
      this.participants,
      this.rooms,
      this.createdAt,
    );
  }

  startRound(at: Instant): Session {
    ensure(
      this.state === 'RUNNING',
      new SessionStateError('Cannot start round unless session is RUNNING', {
        currentState: this.state,
        sessionId: this.sessionId.toPrimitives(),
      }),
    );

    const nextRoundIndex = this.currentRoundIndex + 1;

    ensure(
      nextRoundIndex < this.rounds.length,
      new SessionInvariantError('No more rounds available to start', {
        currentRoundIndex: this.currentRoundIndex,
        totalRounds: this.rounds.length,
        sessionId: this.sessionId.toPrimitives(),
      }),
    );

    // Start the next round
    const updatedRounds = this.rounds.map((round, index) =>
      index === nextRoundIndex ? round.start(at) : round,
    );

    return new Session(
      this.sessionId,
      this.config,
      this.state,
      this.createdByUserId,
      this.hostId,
      this.mainRoomId,
      nextRoundIndex,
      updatedRounds,
      this.participants,
      this.rooms,
      this.createdAt,
    );
  }

  endCurrentRound(at: Instant): Session {
    if (
      this.currentRoundIndex < 0 ||
      this.currentRoundIndex >= this.rounds.length
    )
      return this;
    ensure(
      this.state === 'RUNNING',
      new SessionStateError('Cannot end round unless session is RUNNING', {
        currentState: this.state,
        sessionId: this.sessionId.toPrimitives(),
      }),
    );

    ensure(
      this.currentRoundIndex >= 0 &&
        this.currentRoundIndex < this.rounds.length,
      new SessionInvariantError('No active round to end', {
        currentRoundIndex: this.currentRoundIndex,
        totalRounds: this.rounds.length,
        sessionId: this.sessionId.toPrimitives(),
      }),
    );

    // End the current round
    const updatedRounds = this.rounds.map((round, index) =>
      index === this.currentRoundIndex ? round.beginClosing(at) : round,
    );

    return (
      new Session(
        this.sessionId,
        this.config,
        this.state,
        this.createdByUserId,
        this.hostId,
        this.mainRoomId,
        this.currentRoundIndex,
        updatedRounds,
        this.participants,
        this.rooms,
        this.createdAt,
      )
        // Release all reserved seats
        .releaseAllReservedSeats(at)
    );
  }

  closeCurrentRound(at: Instant): Session {
    if (
      this.currentRoundIndex < 0 ||
      this.currentRoundIndex >= this.rounds.length
    )
      return this;
    ensure(
      this.state === 'RUNNING',
      new SessionStateError('Cannot close round unless session is RUNNING', {
        currentState: this.state,
        sessionId: this.sessionId.toPrimitives(),
      }),
    );

    ensure(
      this.currentRoundIndex >= 0 &&
        this.currentRoundIndex < this.rounds.length,
      new SessionInvariantError('No active round to close', {
        currentRoundIndex: this.currentRoundIndex,
        totalRounds: this.rounds.length,
        sessionId: this.sessionId.toPrimitives(),
      }),
    );

    // Close the current round
    const updatedRounds = this.rounds.map((round, index) =>
      index === this.currentRoundIndex ? round.close(at) : round,
    );

    return new Session(
      this.sessionId,
      this.config,
      this.state,
      this.createdByUserId,
      this.hostId,
      this.mainRoomId,
      this.currentRoundIndex,
      updatedRounds,
      this.participants,
      this.rooms,
      this.createdAt,
    ) // Release all reserved seats
      .releaseAllReservedSeats(at);
  }

  // Utility methods for room and participant queries
  findRoomById(roomId: RoomId): Room | undefined {
    return this.rooms.find(
      (r) => r.roomId.toPrimitives() === roomId.toPrimitives(),
    );
  }

  findParticipantById(
    participantId: ParticipantId,
  ): ParticipantPresence | undefined {
    return this.participants.find(
      (p) => p.participantId.toPrimitives() === participantId.toPrimitives(),
    );
  }

  getParticipantRoom(participantId: ParticipantId): Room | undefined {
    return this.rooms.find((room) =>
      room.allSeats.some(
        (seat) => seat.currentParticipantId === participantId.toPrimitives(),
      ),
    );
  }

  getConnectedParticipants(): ParticipantPresence[] {
    return this.participants.filter((p) => p.isConnected);
  }

  getRoomsWithSpace(): Room[] {
    return this.rooms.filter((room) => room.hasSpace);
  }

  getFillingRooms(): Room[] {
    return this.rooms.filter((room) => room.isFilling);
  }

  getReadyRooms(): Room[] {
    return this.rooms.filter((room) => room.isReady);
  }

  moveParticipantBetweenRooms(
    participantId: ParticipantId,
    fromRoomId: RoomId | undefined,
    toRoomId: RoomId,
    at: Instant,
  ): Session {
    ensure(
      this.state === 'RUNNING',
      new SessionStateError(
        'Cannot move participant unless session is RUNNING',
        {
          currentState: this.state,
          sessionId: this.sessionId.toPrimitives(),
          participantId: participantId.toPrimitives(),
        },
      ),
    );

    // Verify if target room exists
    const toRoom = this.findRoomById(toRoomId);

    ensure(
      toRoom,
      new SessionInvariantError('Target room not found', {
        fromRoomId: fromRoomId?.toPrimitives(),
        toRoomId: toRoomId.toPrimitives(),
        sessionId: this.sessionId.toPrimitives(),
      }),
    );

    if (fromRoomId?.toPrimitives() === toRoomId.toPrimitives()) {
      return this;
    }

    // Move participant by assigning to target room (this will release from source room)
    return this.assignParticipantToRoom(participantId, toRoomId, at);
  }

  returnAllParticipantsToMain(at: Instant): Session {
    ensure(
      this.state === 'RUNNING',
      new SessionStateError(
        'Cannot return participants to main unless session is RUNNING',
        {
          currentState: this.state,
          sessionId: this.sessionId.toPrimitives(),
        },
      ),
    );

    const mainRoom = this.findRoomById(this.mainRoomId);
    ensure(
      mainRoom != null,
      new SessionInvariantError('Main room not found', {
        mainRoomId: this.mainRoomId.toPrimitives(),
        sessionId: this.sessionId.toPrimitives(),
      }),
    );

    // Collect all participants from breakout rooms
    const participantsToMove = this.rooms
      .filter(
        (room) =>
          room.roomId.toPrimitives() !== this.mainRoomId.toPrimitives() &&
          room.occupiedSeats.length > 0,
      )
      .map((room) => ({
        fromRoomId: room.roomId,
        participants: room.occupiedSeats.map((seat) =>
          ParticipantId.fromPrimitives(seat.currentParticipantId),
        ),
      }));

    // Move all participants to main room
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    let updatedSession: Session = this;

    for (const { fromRoomId, participants } of participantsToMove) {
      for (const participantId of participants) {
        updatedSession = updatedSession.moveParticipantBetweenRooms(
          participantId,
          fromRoomId,
          this.mainRoomId,
          at,
        );
      }
    }

    return updatedSession;
  }

  /**
 * Rotate participants across existing breakout rooms using the diagonal-spread rule.
 *
 * Mapping rule:
 *   For each source room at index `rIdx` (r),
 *   enumerate its occupants as [p0, p1, p2, …].
 *   Each occupant at position `pIdx` moves to:
 *
 *       targetrIdx = (rIdx + pIdx) % R
 *
 * Where:
 *   - R            = number of breakout rooms (roomOrder.length)
 *   - roomOrder    = ordered list of breakout roomIds (stable each round)
 *   - rIdx         = index of the current room in roomOrder
 *   - fromRoomId   = actual roomId string from which the participant comes
 *   - occupants    = array of ParticipantPresencePrimitives currently in fromRoomId
 *   - pIdx         = occupant’s index in that occupants array (0..occupants.length-1)
 *   - participantId= unique identifier of the occupant (Uuid primitive string)
 *   - toRoomId     = roomOrder[(rIdx + pIdx) % R], the target breakout roomId
 *   - plan         = array of { participantId, fromRoomId, toRoomId } move instructions
 *   - now          = server time in ms epoch when moves are applied
 *   - maxGroupSize = largest occupants.length across all rooms (diagnostic only;
 *                    tells if the no-repeat invariant is guaranteed: maxGroupSize ≤ R)
 *
 * Invariant:
 *   - If maxGroupSize ≤ R, then no pair of participants who shared a room in round t
 *     can be together again in round t+1.
 *   - If maxGroupSize > R, repeats are mathematically unavoidable (pigeonhole principle).
 *
 
 * Examples (numbers):
 *   1) R = 2 rooms; sizes 2 & 2  (M=2, R=2 → GUARANTEED)
 *      Room0: [A0, A1]
 *      Room1: [B0, B1]
 *      Mapping: (r+j) mod 2
 *        A0→0, A1→1;  B0→1, B1→0
 *      New: Room0=[A0,B1], Room1=[A1,B0]  → no pairs repeat.
 *
 *   2) R = 2 rooms; sizes 3 & 2  (M=3, R=2 → IMPOSSIBLE to guarantee)
 *      Room0: [A0, A1, A2] → A0→0, A1→1, A2→0 (collision A0 with A2)
 *      Room1: [B0, B1]     → B0→1, B1→0
 *      New: Room0=[A0,A2,B1], Room1=[A1,B0] → A0/A2 repeat (pigeonhole).
 *
 *   3) R = 3 rooms; each size 3  (M=3, R=3 → GUARANTEED)
 *      Room0: [A0,A1,A2] → 0,1,2
 *      Room1: [B0,B1,B2] → 1,2,0
 *      Room2: [C0,C1,C2] → 2,0,1
 *      Each former group spreads to all 3 rooms → no pairs repeat.
 *
 *   4) R = 4 rooms; each size 4  (M=4, R=4 → GUARANTEED)
 *      Room r occupants [X0..X3] map to rooms r, r+1, r+2, r+3 (all distinct).
 *
 * Optional UX tweak:
 *   If you want *everyone* to move (not even j=0 stays), add a rotating base offset:
 *     base = (this.currentRoundIndex ?? 0) % R
 *     toIndex = (r + base + j) % R
 *   This keeps the guarantee the same; it only affects motion feel.
 */
  public rotateBreakouts(at: Instant): Session {
    if (this.currentRoundIndex < 0) return this;
    // --- 0) Guards -------------------------------------------------------------
    ensure(
      this.state === 'RUNNING',
      new SessionStateError('Cannot rotate rooms unless session is RUNNING', {
        currentState: this.state,
        sessionId: this.sessionId.toPrimitives(),
      }),
    );

    // --- 1) Build a stable room order (exclude Main) --------------------------
    const breakoutRooms = this.rooms.filter(
      (r) => r.roomId.toPrimitives() !== this.mainRoomId.toPrimitives(),
    );
    const roomOrder: string[] = breakoutRooms.map((r) =>
      r.roomId.toPrimitives(),
    );
    const R = roomOrder.length;
    if (R <= 1) return this; // per your policy: with 0/1 breakout rooms, do nothing.

    // --- 2) Snapshot: group current seated participants by roomId -------------
    // Deterministic per-room ordering → sort by (joinedAt, participantId)
    const occupantsByRoom = new Map<string, ParticipantPresencePrimitives[]>();
    for (const p of this.participants) {
      const rid = p.toPrimitives().currentRoomId;
      if (!rid) continue; // not seated
      if (!roomOrder.includes(rid)) continue; // not a breakout room
      if (!occupantsByRoom.has(rid)) occupantsByRoom.set(rid, []);
      occupantsByRoom.get(rid)!.push(p.toPrimitives());
    }

    for (const list of occupantsByRoom.values()) {
      list.sort(
        (a, b) =>
          (a.joinedAt ?? 0) - (b.joinedAt ?? 0) ||
          a.participantId.localeCompare(b.participantId),
      );
    }

    // --- 3) Plan: diagonal spread over OCCUPANTS ONLY -------------------------
    type Move = { participantId: string; fromRoomId: string; toRoomId: string };
    const plan: Move[] = [];

    for (let rIdx = 0; rIdx < R; rIdx++) {
      const fromRoomId = roomOrder[rIdx];
      const occupants = occupantsByRoom.get(fromRoomId) ?? [];

      for (let pIdx = 0; pIdx < occupants.length; pIdx++) {
        const pid = occupants[pIdx].participantId;
        const toRoomId = roomOrder[(rIdx + pIdx) % R];

        if (toRoomId !== fromRoomId) {
          plan.push({ participantId: pid, fromRoomId, toRoomId });
        }
      }
    }

    // --- 4) Apply the move plan (room-level; seat choice is Room policy) ------
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    let updatedSession: Session = this;

    for (const { participantId, fromRoomId, toRoomId } of plan) {
      updatedSession = updatedSession.moveParticipantBetweenRooms(
        ParticipantId.fromPrimitives(participantId),
        RoomId.fromPrimitives(fromRoomId),
        RoomId.fromPrimitives(toRoomId),
        at,
      );
    }

    return updatedSession;
  }

  makeRoomReady(roomId: RoomId, at: Instant): Session {
    if (
      this.rooms.some(
        (r) => r.roomId.toPrimitives() === roomId.toPrimitives() && r.isReady,
      )
    ) {
      return this;
    }
    ensure(
      this.state === 'RUNNING',
      new SessionStateError(
        'Cannot make room ready unless session is RUNNING',
        {
          currentState: this.state,
          sessionId: this.sessionId.toPrimitives(),
          roomId: roomId.toPrimitives(),
        },
      ),
    );

    const room = this.rooms.find(
      (r) => r.roomId.toPrimitives() === roomId.toPrimitives(),
    );

    ensure(
      room,
      new SessionInvariantError('Room not found', {
        roomId: roomId.toPrimitives(),
        sessionId: this.sessionId.toPrimitives(),
      }),
    );

    ensure(
      room.occupiedSeats.length + room.reservedSeats.length >=
        this.config.defaultRoomConfig.minSeats, // min seats policy,
      new SessionInvariantError('Room is not in FILLING state', {
        roomId: roomId.toPrimitives(),
        occupiedSeats: room.occupiedSeats.length,
        reservedSeats: room.reservedSeats.length,
        sessionId: this.sessionId.toPrimitives(),
      }),
    );

    const updatedRoom = room.makeReady();

    const updatedRooms = this.rooms.map((room) =>
      room.roomId.toPrimitives() === roomId.toPrimitives() ? updatedRoom : room,
    );

    return new Session(
      this.sessionId,
      this.config,
      this.state,
      this.createdByUserId,
      this.hostId,
      this.mainRoomId,
      this.currentRoundIndex,
      this.rounds,
      this.participants,
      updatedRooms,
      this.createdAt,
    );
  }

  makeRoomClosed(roomId: RoomId, at: Instant): Session {
    if (
      this.rooms.some(
        (r) => r.roomId.toPrimitives() === roomId.toPrimitives() && r.isClosed,
      )
    ) {
      return this;
    }
    ensure(
      this.state === 'RUNNING',
      new SessionStateError(
        'Cannot make room closed unless session is RUNNING',
        {
          currentState: this.state,
          sessionId: this.sessionId.toPrimitives(),
          roomId: roomId.toPrimitives(),
        },
      ),
    );

    const room = this.rooms.find(
      (r) => r.roomId.toPrimitives() === roomId.toPrimitives(),
    );

    ensure(
      room,
      new SessionInvariantError('Room not found', {
        roomId: roomId.toPrimitives(),
        sessionId: this.sessionId.toPrimitives(),
      }),
    );

    ensure(
      room.availableSeats.length === 0,
      new SessionInvariantError(
        'Room cannot be closed because it is not full',
        {
          roomId: roomId.toPrimitives(),
          availableSeats: room.availableSeats.length,
          occupiedSeats: room.occupiedSeats.length,
          reservedSeats: room.reservedSeats.length,
          sessionId: this.sessionId.toPrimitives(),
        },
      ),
    );

    const updatedRoom = room.makeClosed();

    const updatedRooms = this.rooms.map((room) =>
      roomId.toPrimitives() === room.roomId.toPrimitives() ? updatedRoom : room,
    );

    return new Session(
      this.sessionId,
      this.config,
      this.state,
      this.createdByUserId,
      this.hostId,
      this.mainRoomId,
      this.currentRoundIndex,
      this.rounds,
      this.participants,
      updatedRooms,
      this.createdAt,
    );
  }

  releaseAllReservedSeats(at: Instant): Session {
    const updatedRooms = this.rooms.map((room) =>
      room.releaseAllReservedSeats(at),
    );

    return new Session(
      this.sessionId,
      this.config,
      this.state,
      this.createdByUserId,
      this.hostId,
      this.mainRoomId,
      this.currentRoundIndex,
      this.rounds,
      this.participants,
      updatedRooms,
      this.createdAt,
    );
  }
}
