import { Session } from '../session.aggregate';
import { SessionStateError } from '../session.errors';
import { SessionTestFactories } from './helpers/factories';

describe('Path 12: returnAllParticipantsToMain', () => {
  describe('throws SessionStateError if not RUNNING', () => {
    const invalidStates = ['SCHEDULED', 'PAUSED', 'COMPLETED', 'CANCELED'];

    it.each(invalidStates)('should throw when session is %s', (state) => {
      // Arrange
      const session = SessionTestFactories.createSession({ state: state as any });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert
      expect(() => session.returnAllParticipantsToMain(at)).toThrow(SessionStateError);
      expect(() => session.returnAllParticipantsToMain(at)).toThrow('Cannot return participants unless session is RUNNING');
    });
  });

  describe('moves all participants from breakout rooms to main room', () => {
    it('should move participants from single breakout room to main', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantId1 = SessionTestFactories.createParticipantId();
      const participantId2 = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add participants and seat them in breakout rooms
      let sessionWithParticipants = session
        .addParticipant(participantId1, 'MEMBER', at)
        .addParticipant(participantId2, 'MEMBER', at);

      // Join participants and create breakout rooms
      sessionWithParticipants = sessionWithParticipants
        .onJoin(participantId1, at)
        .onJoin(participantId2, at);

      // Create breakout room by seating participants
      const breakoutRoomId = SessionTestFactories.createRoomId();
      sessionWithParticipants = sessionWithParticipants
        .assignParticipantToRoom(participantId1, breakoutRoomId, at)
        .assignParticipantToRoom(participantId2, breakoutRoomId, at);

      // Act
      const result = sessionWithParticipants.returnAllParticipantsToMain(at);

      // Assert
      expect(result).not.toBe(sessionWithParticipants);
      
      // All participants should be in main room
      const participant1 = result.getParticipant(participantId1);
      const participant2 = result.getParticipant(participantId2);
      
      expect(participant1?.currentRoomId?.toPrimitives()).toBe(result.mainRoomId.toPrimitives());
      expect(participant2?.currentRoomId?.toPrimitives()).toBe(result.mainRoomId.toPrimitives());
    });

    it('should move participants from multiple breakout rooms to main', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantId1 = SessionTestFactories.createParticipantId();
      const participantId2 = SessionTestFactories.createParticipantId();
      const participantId3 = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add participants
      let sessionWithParticipants = session
        .addParticipant(participantId1, 'MEMBER', at)
        .addParticipant(participantId2, 'MEMBER', at)
        .addParticipant(participantId3, 'MEMBER', at);

      // Join participants
      sessionWithParticipants = sessionWithParticipants
        .onJoin(participantId1, at)
        .onJoin(participantId2, at)
        .onJoin(participantId3, at);

      // Create multiple breakout rooms
      const breakoutRoomId1 = SessionTestFactories.createRoomId();
      const breakoutRoomId2 = SessionTestFactories.createRoomId();
      
      sessionWithParticipants = sessionWithParticipants
        .assignParticipantToRoom(participantId1, breakoutRoomId1, at)
        .assignParticipantToRoom(participantId2, breakoutRoomId1, at)
        .assignParticipantToRoom(participantId3, breakoutRoomId2, at);

      // Act
      const result = sessionWithParticipants.returnAllParticipantsToMain(at);

      // Assert
      expect(result).not.toBe(sessionWithParticipants);
      
      // All participants should be in main room
      const participant1 = result.getParticipant(participantId1);
      const participant2 = result.getParticipant(participantId2);
      const participant3 = result.getParticipant(participantId3);
      
      expect(participant1?.currentRoomId?.toPrimitives()).toBe(result.mainRoomId.toPrimitives());
      expect(participant2?.currentRoomId?.toPrimitives()).toBe(result.mainRoomId.toPrimitives());
      expect(participant3?.currentRoomId?.toPrimitives()).toBe(result.mainRoomId.toPrimitives());
    });

    it('should handle participants already in main room', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantId1 = SessionTestFactories.createParticipantId();
      const participantId2 = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add participants and keep one in main, move one to breakout
      let sessionWithParticipants = session
        .addParticipant(participantId1, 'MEMBER', at)
        .addParticipant(participantId2, 'MEMBER', at);

      sessionWithParticipants = sessionWithParticipants
        .onJoin(participantId1, at)
        .onJoin(participantId2, at);

      // Move only one participant to breakout room
      const breakoutRoomId = SessionTestFactories.createRoomId();
      sessionWithParticipants = sessionWithParticipants
        .assignParticipantToRoom(participantId2, breakoutRoomId, at);

      // Act
      const result = sessionWithParticipants.returnAllParticipantsToMain(at);

      // Assert
      expect(result).not.toBe(sessionWithParticipants);
      
      // Both participants should be in main room
      const participant1 = result.getParticipant(participantId1);
      const participant2 = result.getParticipant(participantId2);
      
      expect(participant1?.currentRoomId?.toPrimitives()).toBe(result.mainRoomId.toPrimitives());
      expect(participant2?.currentRoomId?.toPrimitives()).toBe(result.mainRoomId.toPrimitives());
    });
  });

  describe('no-op when all participants already in main room', () => {
    it('should return same instance when no participants in breakout rooms', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add participant and keep in main room
      const sessionWithParticipant = session
        .addParticipant(participantId, 'MEMBER', at)
        .onJoin(participantId, at);

      // Act
      const result = sessionWithParticipant.returnAllParticipantsToMain(at);

      // Assert
      expect(result).toBe(sessionWithParticipant); // Same instance for no-op
    });

    it('should return same instance when no participants exist', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.returnAllParticipantsToMain(at);

      // Assert
      expect(result).toBe(session); // Same instance for no-op
    });
  });

  describe('updates presence timestamps', () => {
    it('should update presence timestamps for moved participants', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantId = SessionTestFactories.createParticipantId();
      const joinTime = SessionTestFactories.createInstant(SessionTestFactories.BASE_TIMESTAMP);
      const moveTime = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add participant and move to breakout room
      let sessionWithParticipant = session
        .addParticipant(participantId, 'MEMBER', joinTime)
        .onJoin(participantId, joinTime);

      const breakoutRoomId = SessionTestFactories.createRoomId();
      sessionWithParticipant = sessionWithParticipant
        .assignParticipantToRoom(participantId, breakoutRoomId, joinTime);

      // Act
      const result = sessionWithParticipant.returnAllParticipantsToMain(moveTime);

      // Assert
      const participant = result.getParticipant(participantId);
      expect(participant?.lastSeenAt).toBe(moveTime);
    });
  });

  describe('preserves seat reservations', () => {
    it('should maintain reserved seats when moving participants', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add participant, join, move to breakout, then leave (creating reservation)
      let sessionWithParticipant = session
        .addParticipant(participantId, 'MEMBER', at)
        .onJoin(participantId, at);

      const breakoutRoomId = SessionTestFactories.createRoomId();
      sessionWithParticipant = sessionWithParticipant
        .assignParticipantToRoom(participantId, breakoutRoomId, at)
        .onLeave(participantId, at);

      // Act
      const result = sessionWithParticipant.returnAllParticipantsToMain(at);

      // Assert
      expect(result).not.toBe(sessionWithParticipant);
      
      // Participant should still have reservation in main room
      const participant = result.getParticipant(participantId);
      expect(participant?.currentRoomId?.toPrimitives()).toBe(result.mainRoomId.toPrimitives());
    });
  });

  describe('immutability verification', () => {
    it('should return new instance when moving participants', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add participant and move to breakout room
      let sessionWithParticipant = session
        .addParticipant(participantId, 'MEMBER', at)
        .onJoin(participantId, at);

      const breakoutRoomId = SessionTestFactories.createRoomId();
      sessionWithParticipant = sessionWithParticipant
        .assignParticipantToRoom(participantId, breakoutRoomId, at);

      // Act
      const result = sessionWithParticipant.returnAllParticipantsToMain(at);

      // Assert
      expect(result).not.toBe(sessionWithParticipant);
      
      // Original should be unchanged
      const originalParticipant = sessionWithParticipant.getParticipant(participantId);
      expect(originalParticipant?.currentRoomId?.toPrimitives()).toBe(breakoutRoomId.toPrimitives());
    });

    it('should preserve all other properties', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add participant and move to breakout room
      let sessionWithParticipant = session
        .addParticipant(participantId, 'MEMBER', at)
        .onJoin(participantId, at);

      const breakoutRoomId = SessionTestFactories.createRoomId();
      sessionWithParticipant = sessionWithParticipant
        .assignParticipantToRoom(participantId, breakoutRoomId, at);

      // Act
      const result = sessionWithParticipant.returnAllParticipantsToMain(at);

      // Assert
      expect(result.sessionId).toBe(sessionWithParticipant.sessionId);
      expect(result.config).toBe(sessionWithParticipant.config);
      expect(result.currentState).toBe(sessionWithParticipant.currentState);
      expect(result.createdByUserId).toBe(sessionWithParticipant.createdByUserId);
      expect(result.mainRoomId).toBe(sessionWithParticipant.mainRoomId);
      expect(result.createdAt).toBe(sessionWithParticipant.createdAt);
      expect(result.participantCount).toBe(sessionWithParticipant.participantCount);
      expect(result.toPrimitives().hostId).toBe(sessionWithParticipant.toPrimitives().hostId);
    });
  });
});
