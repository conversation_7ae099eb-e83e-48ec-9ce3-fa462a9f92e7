import { Session } from '../session.aggregate';
import { SessionStateError } from '../session.errors';
import { SessionTestFactories } from './helpers/factories';

describe('Path 3: Lifecycle transitions (idempotency + guards)', () => {
  describe('startSession', () => {
    it('should transition from SCHEDULED to RUNNING', () => {
      // Arrange
      const session = SessionTestFactories.createScheduledSession();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.startSession(at);

      // Assert
      expect(result).not.toBe(session); // New instance
      expect(result.currentState).toBe('RUNNING');
      expect(result.isRunning).toBe(true);
      expect(session.currentState).toBe('SCHEDULED'); // Original unchanged
    });

    it('should be idempotent when already RUNNING', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.startSession(at);

      // Assert
      expect(result).toBe(session); // Same instance (idempotent)
      expect(result.currentState).toBe('RUNNING');
    });

    it('should be idempotent when PAUSED', () => {
      // Arrange
      const session = SessionTestFactories.createPausedSession();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.startSession(at);

      // Assert
      expect(result).toBe(session); // Same instance (idempotent)
      expect(result.currentState).toBe('PAUSED');
    });

    it('should throw SessionStateError when not SCHEDULED', () => {
      // Arrange
      const invalidStates = ['COMPLETED', 'CANCELED'];
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      invalidStates.forEach((state) => {
        const session = SessionTestFactories.createSession({ state: state as any });

        // Act & Assert
        expect(() => session.startSession(at)).toThrow(SessionStateError);
        expect(() => session.startSession(at)).toThrow('Cannot start session unless in SCHEDULED state');
      });
    });
  });

  describe('pauseSession', () => {
    it('should transition from RUNNING to PAUSED', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();

      // Act
      const result = session.pauseSession();

      // Assert
      expect(result).not.toBe(session); // New instance
      expect(result.currentState).toBe('PAUSED');
      expect(result.isPaused).toBe(true);
      expect(session.currentState).toBe('RUNNING'); // Original unchanged
    });

    it('should be idempotent when already PAUSED', () => {
      // Arrange
      const session = SessionTestFactories.createPausedSession();

      // Act
      const result = session.pauseSession();

      // Assert
      expect(result).toBe(session); // Same instance (idempotent)
      expect(result.currentState).toBe('PAUSED');
    });

    it('should throw SessionStateError when not RUNNING', () => {
      // Arrange
      const invalidStates = ['SCHEDULED', 'COMPLETED', 'CANCELED'];

      invalidStates.forEach((state) => {
        const session = SessionTestFactories.createSession({ state: state as any });

        // Act & Assert
        expect(() => session.pauseSession()).toThrow(SessionStateError);
        expect(() => session.pauseSession()).toThrow('Cannot pause session unless in RUNNING state');
      });
    });
  });

  describe('resumeSession', () => {
    it('should transition from PAUSED to RUNNING', () => {
      // Arrange
      const session = SessionTestFactories.createPausedSession();

      // Act
      const result = session.resumeSession();

      // Assert
      expect(result).not.toBe(session); // New instance
      expect(result.currentState).toBe('RUNNING');
      expect(result.isRunning).toBe(true);
      expect(session.currentState).toBe('PAUSED'); // Original unchanged
    });

    it('should be idempotent when already RUNNING', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();

      // Act
      const result = session.resumeSession();

      // Assert
      expect(result).toBe(session); // Same instance (idempotent)
      expect(result.currentState).toBe('RUNNING');
    });

    it('should throw SessionStateError when not PAUSED', () => {
      // Arrange
      const invalidStates = ['SCHEDULED', 'COMPLETED', 'CANCELED'];

      invalidStates.forEach((state) => {
        const session = SessionTestFactories.createSession({ state: state as any });

        // Act & Assert
        expect(() => session.resumeSession()).toThrow(SessionStateError);
        expect(() => session.resumeSession()).toThrow('Cannot resume session unless in PAUSED state');
      });
    });
  });

  describe('completeSession', () => {
    it('should transition from RUNNING to COMPLETED', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();

      // Act
      const result = session.completeSession();

      // Assert
      expect(result).not.toBe(session); // New instance
      expect(result.currentState).toBe('COMPLETED');
      expect(result.isCompleted).toBe(true);
      expect(session.currentState).toBe('RUNNING'); // Original unchanged
    });

    it('should transition from PAUSED to COMPLETED', () => {
      // Arrange
      const session = SessionTestFactories.createPausedSession();

      // Act
      const result = session.completeSession();

      // Assert
      expect(result).not.toBe(session); // New instance
      expect(result.currentState).toBe('COMPLETED');
      expect(result.isCompleted).toBe(true);
      expect(session.currentState).toBe('PAUSED'); // Original unchanged
    });

    it('should be idempotent when already COMPLETED', () => {
      // Arrange
      const session = SessionTestFactories.createCompletedSession();

      // Act
      const result = session.completeSession();

      // Assert
      expect(result).toBe(session); // Same instance (idempotent)
      expect(result.currentState).toBe('COMPLETED');
    });

    it('should be idempotent when CANCELED', () => {
      // Arrange
      const session = SessionTestFactories.createCanceledSession();

      // Act
      const result = session.completeSession();

      // Assert
      expect(result).toBe(session); // Same instance (idempotent)
      expect(result.currentState).toBe('CANCELED');
    });

    it('should throw SessionStateError when SCHEDULED', () => {
      // Arrange
      const session = SessionTestFactories.createScheduledSession();

      // Act & Assert
      expect(() => session.completeSession()).toThrow(SessionStateError);
      expect(() => session.completeSession()).toThrow('Cannot complete session unless in RUNNING or PAUSED state');
    });
  });

  describe('cancelSession', () => {
    it('should transition from SCHEDULED to CANCELED', () => {
      // Arrange
      const session = SessionTestFactories.createScheduledSession();

      // Act
      const result = session.cancelSession();

      // Assert
      expect(result).not.toBe(session); // New instance
      expect(result.currentState).toBe('CANCELED');
      expect(result.isCanceled).toBe(true);
      expect(session.currentState).toBe('SCHEDULED'); // Original unchanged
    });

    it('should transition from RUNNING to CANCELED', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();

      // Act
      const result = session.cancelSession();

      // Assert
      expect(result).not.toBe(session); // New instance
      expect(result.currentState).toBe('CANCELED');
      expect(result.isCanceled).toBe(true);
      expect(session.currentState).toBe('RUNNING'); // Original unchanged
    });

    it('should transition from PAUSED to CANCELED', () => {
      // Arrange
      const session = SessionTestFactories.createPausedSession();

      // Act
      const result = session.cancelSession();

      // Assert
      expect(result).not.toBe(session); // New instance
      expect(result.currentState).toBe('CANCELED');
      expect(result.isCanceled).toBe(true);
      expect(session.currentState).toBe('PAUSED'); // Original unchanged
    });

    it('should throw SessionStateError when already COMPLETED', () => {
      // Arrange
      const session = SessionTestFactories.createCompletedSession();

      // Act & Assert
      expect(() => session.cancelSession()).toThrow(SessionStateError);
      expect(() => session.cancelSession()).toThrow('Cannot cancel a completed session');
    });

    it('should throw SessionStateError when already CANCELED', () => {
      // Arrange
      const session = SessionTestFactories.createCanceledSession();

      // Act & Assert
      expect(() => session.cancelSession()).toThrow(SessionStateError);
      expect(() => session.cancelSession()).toThrow('Cannot cancel a completed session');
    });
  });

  describe('immutability verification', () => {
    it('should return new instances for all state changes', () => {
      // Arrange
      const originalSession = SessionTestFactories.createScheduledSession();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const runningSession = originalSession.startSession(at);
      const pausedSession = runningSession.pauseSession();
      const resumedSession = pausedSession.resumeSession();
      const completedSession = resumedSession.completeSession();

      // Assert - All should be different instances
      expect(runningSession).not.toBe(originalSession);
      expect(pausedSession).not.toBe(runningSession);
      expect(resumedSession).not.toBe(pausedSession);
      expect(completedSession).not.toBe(resumedSession);

      // Original should remain unchanged
      expect(originalSession.currentState).toBe('SCHEDULED');
      expect(runningSession.currentState).toBe('RUNNING');
      expect(pausedSession.currentState).toBe('PAUSED');
      expect(resumedSession.currentState).toBe('RUNNING');
      expect(completedSession.currentState).toBe('COMPLETED');
    });

    it('should preserve all other properties during state transitions', () => {
      // Arrange
      const hostId = SessionTestFactories.createUuid().toPrimitives();
      const participants = [SessionTestFactories.createParticipantPresence().toPrimitives()];
      const rounds = [SessionTestFactories.createRoundInstance().toPrimitives()];
      
      const originalSession = SessionTestFactories.createScheduledSession({
        hostId,
        participants,
        rounds,
        currentRoundIndex: 0,
      });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const runningSession = originalSession.startSession(at);

      // Assert - All properties except state should be preserved
      expect(runningSession.sessionId).toBe(originalSession.sessionId);
      expect(runningSession.config).toBe(originalSession.config);
      expect(runningSession.createdByUserId).toBe(originalSession.createdByUserId);
      expect(runningSession.mainRoomId).toBe(originalSession.mainRoomId);
      expect(runningSession.createdAt).toBe(originalSession.createdAt);
      expect(runningSession.allParticipants).toHaveLength(originalSession.allParticipants.length);
      expect(runningSession.allRounds).toHaveLength(originalSession.allRounds.length);
      expect(runningSession.allRooms).toHaveLength(originalSession.allRooms.length);
      expect(runningSession.toPrimitives().currentRoundIndex).toBe(originalSession.toPrimitives().currentRoundIndex);
      expect(runningSession.toPrimitives().hostId).toBe(originalSession.toPrimitives().hostId);
    });
  });
});
