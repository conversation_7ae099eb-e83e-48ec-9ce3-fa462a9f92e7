import { Session } from '../session.aggregate';
import { SessionStateError } from '../session.errors';
import { SessionTestFactories } from './helpers/factories';

describe('Path 14: releaseAllReservedSeats', () => {
  describe('throws SessionStateError if not RUNNING', () => {
    const invalidStates = ['SCHEDULED', 'PAUSED', 'COMPLETED', 'CANCELED'];

    it.each(invalidStates)('should throw when session is %s', (state) => {
      // Arrange
      const session = SessionTestFactories.createSession({ state: state as any });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert
      expect(() => session.releaseAllReservedSeats(at)).toThrow(SessionStateError);
      expect(() => session.releaseAllReservedSeats(at)).toThrow('Cannot release reserved seats unless session is RUNNING');
    });
  });

  describe('releases all reserved seats across all rooms', () => {
    it('should release reserved seats in main room', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add participant, join, then leave (creating reservation)
      let sessionWithReservation = session
        .addParticipant(participantId, 'MEMBER', at)
        .onJoin(participantId, at)
        .onLeave(participantId, at);

      // Verify reservation exists
      const mainRoom = sessionWithReservation.getRoom(sessionWithReservation.mainRoomId);
      expect(mainRoom?.reservedSeats.length).toBe(1);

      // Act
      const result = sessionWithReservation.releaseAllReservedSeats(at);

      // Assert
      expect(result).not.toBe(sessionWithReservation);
      
      const resultMainRoom = result.getRoom(result.mainRoomId);
      expect(resultMainRoom?.reservedSeats.length).toBe(0);
    });

    it('should release reserved seats in breakout rooms', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add participant, join, move to breakout, then leave (creating reservation)
      let sessionWithReservation = session
        .addParticipant(participantId, 'MEMBER', at)
        .onJoin(participantId, at);

      const breakoutRoomId = SessionTestFactories.createRoomId();
      sessionWithReservation = sessionWithReservation
        .assignParticipantToRoom(participantId, breakoutRoomId, at)
        .onLeave(participantId, at);

      // Verify reservation exists in breakout room
      const breakoutRoom = sessionWithReservation.getRoom(breakoutRoomId);
      expect(breakoutRoom?.reservedSeats.length).toBe(1);

      // Act
      const result = sessionWithReservation.releaseAllReservedSeats(at);

      // Assert
      expect(result).not.toBe(sessionWithReservation);
      
      const resultBreakoutRoom = result.getRoom(breakoutRoomId);
      expect(resultBreakoutRoom?.reservedSeats.length).toBe(0);
    });

    it('should release reserved seats in multiple rooms simultaneously', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantId1 = SessionTestFactories.createParticipantId();
      const participantId2 = SessionTestFactories.createParticipantId();
      const participantId3 = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add participants
      let sessionWithParticipants = session
        .addParticipant(participantId1, 'MEMBER', at)
        .addParticipant(participantId2, 'MEMBER', at)
        .addParticipant(participantId3, 'MEMBER', at);

      // Join all participants
      sessionWithParticipants = sessionWithParticipants
        .onJoin(participantId1, at)
        .onJoin(participantId2, at)
        .onJoin(participantId3, at);

      // Move some to breakout rooms
      const breakoutRoomId1 = SessionTestFactories.createRoomId();
      const breakoutRoomId2 = SessionTestFactories.createRoomId();
      
      sessionWithParticipants = sessionWithParticipants
        .assignParticipantToRoom(participantId2, breakoutRoomId1, at)
        .assignParticipantToRoom(participantId3, breakoutRoomId2, at);

      // Create reservations by having participants leave
      sessionWithParticipants = sessionWithParticipants
        .onLeave(participantId1, at) // Main room reservation
        .onLeave(participantId2, at) // Breakout room 1 reservation
        .onLeave(participantId3, at); // Breakout room 2 reservation

      // Verify reservations exist
      const mainRoom = sessionWithParticipants.getRoom(sessionWithParticipants.mainRoomId);
      const breakoutRoom1 = sessionWithParticipants.getRoom(breakoutRoomId1);
      const breakoutRoom2 = sessionWithParticipants.getRoom(breakoutRoomId2);
      
      expect(mainRoom?.reservedSeats.length).toBe(1);
      expect(breakoutRoom1?.reservedSeats.length).toBe(1);
      expect(breakoutRoom2?.reservedSeats.length).toBe(1);

      // Act
      const result = sessionWithParticipants.releaseAllReservedSeats(at);

      // Assert
      expect(result).not.toBe(sessionWithParticipants);
      
      const resultMainRoom = result.getRoom(result.mainRoomId);
      const resultBreakoutRoom1 = result.getRoom(breakoutRoomId1);
      const resultBreakoutRoom2 = result.getRoom(breakoutRoomId2);
      
      expect(resultMainRoom?.reservedSeats.length).toBe(0);
      expect(resultBreakoutRoom1?.reservedSeats.length).toBe(0);
      expect(resultBreakoutRoom2?.reservedSeats.length).toBe(0);
    });
  });

  describe('no-op when no reserved seats exist', () => {
    it('should return same instance when no reservations exist', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.releaseAllReservedSeats(at);

      // Assert
      expect(result).toBe(session); // Same instance for no-op
    });

    it('should return same instance when participants are seated but no reservations', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add participant and join (but don't leave, so no reservation)
      const sessionWithParticipant = session
        .addParticipant(participantId, 'MEMBER', at)
        .onJoin(participantId, at);

      // Act
      const result = sessionWithParticipant.releaseAllReservedSeats(at);

      // Assert
      expect(result).toBe(sessionWithParticipant); // Same instance for no-op
    });
  });

  describe('removes participants from roster when releasing reservations', () => {
    it('should remove participant from roster when releasing their reservation', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add participant, join, then leave (creating reservation)
      const sessionWithReservation = session
        .addParticipant(participantId, 'MEMBER', at)
        .onJoin(participantId, at)
        .onLeave(participantId, at);

      // Verify participant exists in roster
      expect(sessionWithReservation.getParticipant(participantId)).toBeDefined();

      // Act
      const result = sessionWithReservation.releaseAllReservedSeats(at);

      // Assert
      expect(result.getParticipant(participantId)).toBeUndefined();
      expect(result.participantCount).toBe(0);
    });

    it('should remove multiple participants when releasing multiple reservations', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantId1 = SessionTestFactories.createParticipantId();
      const participantId2 = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add participants, join, then leave (creating reservations)
      const sessionWithReservations = session
        .addParticipant(participantId1, 'MEMBER', at)
        .addParticipant(participantId2, 'MEMBER', at)
        .onJoin(participantId1, at)
        .onJoin(participantId2, at)
        .onLeave(participantId1, at)
        .onLeave(participantId2, at);

      // Verify participants exist in roster
      expect(sessionWithReservations.participantCount).toBe(2);

      // Act
      const result = sessionWithReservations.releaseAllReservedSeats(at);

      // Assert
      expect(result.getParticipant(participantId1)).toBeUndefined();
      expect(result.getParticipant(participantId2)).toBeUndefined();
      expect(result.participantCount).toBe(0);
    });

    it('should preserve participants who are currently seated (not reserved)', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantId1 = SessionTestFactories.createParticipantId(); // Will have reservation
      const participantId2 = SessionTestFactories.createParticipantId(); // Will stay seated
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add participants and join both
      let sessionWithParticipants = session
        .addParticipant(participantId1, 'MEMBER', at)
        .addParticipant(participantId2, 'MEMBER', at)
        .onJoin(participantId1, at)
        .onJoin(participantId2, at);

      // Only participant1 leaves (creating reservation), participant2 stays
      sessionWithParticipants = sessionWithParticipants.onLeave(participantId1, at);

      // Act
      const result = sessionWithParticipants.releaseAllReservedSeats(at);

      // Assert
      expect(result.getParticipant(participantId1)).toBeUndefined(); // Removed
      expect(result.getParticipant(participantId2)).toBeDefined(); // Preserved
      expect(result.participantCount).toBe(1);
    });
  });

  describe('immutability verification', () => {
    it('should return new instance when releasing reservations', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add participant, join, then leave (creating reservation)
      const sessionWithReservation = session
        .addParticipant(participantId, 'MEMBER', at)
        .onJoin(participantId, at)
        .onLeave(participantId, at);

      // Act
      const result = sessionWithReservation.releaseAllReservedSeats(at);

      // Assert
      expect(result).not.toBe(sessionWithReservation);
      
      // Original should be unchanged
      const originalMainRoom = sessionWithReservation.getRoom(sessionWithReservation.mainRoomId);
      expect(originalMainRoom?.reservedSeats.length).toBe(1);
      expect(sessionWithReservation.participantCount).toBe(1);
    });

    it('should preserve all other properties', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add participant, join, then leave (creating reservation)
      const sessionWithReservation = session
        .addParticipant(participantId, 'MEMBER', at)
        .onJoin(participantId, at)
        .onLeave(participantId, at);

      // Act
      const result = sessionWithReservation.releaseAllReservedSeats(at);

      // Assert
      expect(result.sessionId).toBe(sessionWithReservation.sessionId);
      expect(result.config).toBe(sessionWithReservation.config);
      expect(result.currentState).toBe(sessionWithReservation.currentState);
      expect(result.createdByUserId).toBe(sessionWithReservation.createdByUserId);
      expect(result.mainRoomId).toBe(sessionWithReservation.mainRoomId);
      expect(result.createdAt).toBe(sessionWithReservation.createdAt);
      expect(result.toPrimitives().hostId).toBe(sessionWithReservation.toPrimitives().hostId);
    });
  });
});
