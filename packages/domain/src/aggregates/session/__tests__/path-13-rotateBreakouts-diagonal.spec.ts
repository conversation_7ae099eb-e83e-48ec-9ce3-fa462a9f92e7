import { Session } from '../session.aggregate';
import { SessionStateError, SessionInvariantError } from '../session.errors';
import { SessionTestFactories } from './helpers/factories';

describe('Path 13: rotateBreakouts – diagonal spread', () => {
  describe('throws SessionStateError if not RUNNING', () => {
    const invalidStates = ['SCHEDULED', 'PAUSED', 'COMPLETED', 'CANCELED'];

    it.each(invalidStates)('should throw when session is %s', (state) => {
      // Arrange
      const session = SessionTestFactories.createSession({ state: state as any });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert
      expect(() => session.rotateBreakouts(at)).toThrow(SessionStateError);
      expect(() => session.rotateBreakouts(at)).toThrow('Cannot rotate breakouts unless session is RUNNING');
    });
  });

  describe('throws SessionInvariantError when insufficient participants', () => {
    it('should throw when no participants exist', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert
      expect(() => session.rotateBreakouts(at)).toThrow(SessionInvariantError);
      expect(() => session.rotateBreakouts(at)).toThrow('Insufficient participants for breakout rotation');
    });

    it('should throw when only one participant exists', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      const sessionWithParticipant = session
        .addParticipant(participantId, 'MEMBER', at)
        .onJoin(participantId, at);

      // Act & Assert
      expect(() => sessionWithParticipant.rotateBreakouts(at)).toThrow(SessionInvariantError);
      expect(() => sessionWithParticipant.rotateBreakouts(at)).toThrow('Insufficient participants for breakout rotation');
    });
  });

  describe('diagonal spread algorithm', () => {
    it('should distribute 4 participants into 2 rooms diagonally', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantIds = [
        SessionTestFactories.createParticipantId(),
        SessionTestFactories.createParticipantId(),
        SessionTestFactories.createParticipantId(),
        SessionTestFactories.createParticipantId(),
      ];
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add and join all participants
      let sessionWithParticipants = session;
      for (const participantId of participantIds) {
        sessionWithParticipants = sessionWithParticipants
          .addParticipant(participantId, 'MEMBER', at)
          .onJoin(participantId, at);
      }

      // Act
      const result = sessionWithParticipants.rotateBreakouts(at);

      // Assert
      expect(result).not.toBe(sessionWithParticipants);
      
      // Should create breakout rooms (not main room)
      const allRooms = result.allRooms;
      const breakoutRooms = allRooms.filter(room => room.roomId.toPrimitives() !== result.mainRoomId.toPrimitives());
      expect(breakoutRooms.length).toBeGreaterThan(0);
      
      // All participants should be in breakout rooms
      for (const participantId of participantIds) {
        const participant = result.getParticipant(participantId);
        expect(participant?.currentRoomId?.toPrimitives()).not.toBe(result.mainRoomId.toPrimitives());
      }
    });

    it('should distribute 6 participants into 3 rooms diagonally', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantIds = Array.from({ length: 6 }, () => SessionTestFactories.createParticipantId());
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add and join all participants
      let sessionWithParticipants = session;
      for (const participantId of participantIds) {
        sessionWithParticipants = sessionWithParticipants
          .addParticipant(participantId, 'MEMBER', at)
          .onJoin(participantId, at);
      }

      // Act
      const result = sessionWithParticipants.rotateBreakouts(at);

      // Assert
      expect(result).not.toBe(sessionWithParticipants);
      
      // Should create multiple breakout rooms
      const allRooms = result.allRooms;
      const breakoutRooms = allRooms.filter(room => room.roomId.toPrimitives() !== result.mainRoomId.toPrimitives());
      expect(breakoutRooms.length).toBeGreaterThanOrEqual(2);
      
      // All participants should be distributed across breakout rooms
      for (const participantId of participantIds) {
        const participant = result.getParticipant(participantId);
        expect(participant?.currentRoomId?.toPrimitives()).not.toBe(result.mainRoomId.toPrimitives());
      }
    });

    it('should ensure no room exceeds maxSeats from defaultRoomConfig', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantIds = Array.from({ length: 8 }, () => SessionTestFactories.createParticipantId());
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add and join all participants
      let sessionWithParticipants = session;
      for (const participantId of participantIds) {
        sessionWithParticipants = sessionWithParticipants
          .addParticipant(participantId, 'MEMBER', at)
          .onJoin(participantId, at);
      }

      // Act
      const result = sessionWithParticipants.rotateBreakouts(at);

      // Assert
      const allRooms = result.allRooms;
      const breakoutRooms = allRooms.filter(room => room.roomId.toPrimitives() !== result.mainRoomId.toPrimitives());
      
      for (const room of breakoutRooms) {
        expect(room.occupiedSeats.length).toBeLessThanOrEqual(room.config.maxSeats);
      }
    });

    it('should respect minSeats policy when creating rooms', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantIds = Array.from({ length: 5 }, () => SessionTestFactories.createParticipantId());
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add and join all participants
      let sessionWithParticipants = session;
      for (const participantId of participantIds) {
        sessionWithParticipants = sessionWithParticipants
          .addParticipant(participantId, 'MEMBER', at)
          .onJoin(participantId, at);
      }

      // Act
      const result = sessionWithParticipants.rotateBreakouts(at);

      // Assert
      const allRooms = result.allRooms;
      const breakoutRooms = allRooms.filter(room => room.roomId.toPrimitives() !== result.mainRoomId.toPrimitives());
      
      for (const room of breakoutRooms) {
        expect(room.occupiedSeats.length).toBeGreaterThanOrEqual(room.config.minSeats);
      }
    });
  });

  describe('handles participants already in breakout rooms', () => {
    it('should redistribute participants from existing breakout rooms', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantIds = [
        SessionTestFactories.createParticipantId(),
        SessionTestFactories.createParticipantId(),
        SessionTestFactories.createParticipantId(),
        SessionTestFactories.createParticipantId(),
      ];
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add and join all participants
      let sessionWithParticipants = session;
      for (const participantId of participantIds) {
        sessionWithParticipants = sessionWithParticipants
          .addParticipant(participantId, 'MEMBER', at)
          .onJoin(participantId, at);
      }

      // First rotation
      const firstRotation = sessionWithParticipants.rotateBreakouts(at);
      
      // Get initial room assignments
      const initialAssignments = participantIds.map(id => ({
        participantId: id,
        roomId: firstRotation.getParticipant(id)?.currentRoomId?.toPrimitives()
      }));

      // Act - Second rotation
      const secondRotation = firstRotation.rotateBreakouts(at);

      // Assert
      expect(secondRotation).not.toBe(firstRotation);
      
      // At least some participants should have different room assignments
      const newAssignments = participantIds.map(id => ({
        participantId: id,
        roomId: secondRotation.getParticipant(id)?.currentRoomId?.toPrimitives()
      }));

      const changedAssignments = initialAssignments.filter((initial, index) => 
        initial.roomId !== newAssignments[index].roomId
      );
      
      expect(changedAssignments.length).toBeGreaterThan(0);
    });
  });

  describe('updates presence timestamps', () => {
    it('should update presence timestamps for all moved participants', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantIds = [
        SessionTestFactories.createParticipantId(),
        SessionTestFactories.createParticipantId(),
        SessionTestFactories.createParticipantId(),
        SessionTestFactories.createParticipantId(),
      ];
      const joinTime = SessionTestFactories.createInstant(SessionTestFactories.BASE_TIMESTAMP);
      const rotateTime = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add and join all participants
      let sessionWithParticipants = session;
      for (const participantId of participantIds) {
        sessionWithParticipants = sessionWithParticipants
          .addParticipant(participantId, 'MEMBER', joinTime)
          .onJoin(participantId, joinTime);
      }

      // Act
      const result = sessionWithParticipants.rotateBreakouts(rotateTime);

      // Assert
      for (const participantId of participantIds) {
        const participant = result.getParticipant(participantId);
        expect(participant?.lastSeenAt).toBe(rotateTime);
      }
    });
  });

  describe('immutability verification', () => {
    it('should return new instance when rotating breakouts', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantIds = [
        SessionTestFactories.createParticipantId(),
        SessionTestFactories.createParticipantId(),
        SessionTestFactories.createParticipantId(),
        SessionTestFactories.createParticipantId(),
      ];
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add and join all participants
      let sessionWithParticipants = session;
      for (const participantId of participantIds) {
        sessionWithParticipants = sessionWithParticipants
          .addParticipant(participantId, 'MEMBER', at)
          .onJoin(participantId, at);
      }

      // Act
      const result = sessionWithParticipants.rotateBreakouts(at);

      // Assert
      expect(result).not.toBe(sessionWithParticipants);
      
      // Original should be unchanged
      for (const participantId of participantIds) {
        const originalParticipant = sessionWithParticipants.getParticipant(participantId);
        expect(originalParticipant?.currentRoomId?.toPrimitives()).toBe(sessionWithParticipants.mainRoomId.toPrimitives());
      }
    });

    it('should preserve all other properties', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantIds = [
        SessionTestFactories.createParticipantId(),
        SessionTestFactories.createParticipantId(),
        SessionTestFactories.createParticipantId(),
        SessionTestFactories.createParticipantId(),
      ];
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add and join all participants
      let sessionWithParticipants = session;
      for (const participantId of participantIds) {
        sessionWithParticipants = sessionWithParticipants
          .addParticipant(participantId, 'MEMBER', at)
          .onJoin(participantId, at);
      }

      // Act
      const result = sessionWithParticipants.rotateBreakouts(at);

      // Assert
      expect(result.sessionId).toBe(sessionWithParticipants.sessionId);
      expect(result.config).toBe(sessionWithParticipants.config);
      expect(result.currentState).toBe(sessionWithParticipants.currentState);
      expect(result.createdByUserId).toBe(sessionWithParticipants.createdByUserId);
      expect(result.mainRoomId).toBe(sessionWithParticipants.mainRoomId);
      expect(result.createdAt).toBe(sessionWithParticipants.createdAt);
      expect(result.participantCount).toBe(sessionWithParticipants.participantCount);
      expect(result.toPrimitives().hostId).toBe(sessionWithParticipants.toPrimitives().hostId);
    });
  });
});
