import { Session } from '../session.aggregate';
import { SessionStateError, SessionParticipantNotFoundError } from '../session.errors';
import { SessionTestFactories } from './helpers/factories';

describe('Path 6: seatParticipant – selection & creation path', () => {
  describe('throws SessionStateError if not RUNNING', () => {
    const invalidStates = ['SCHEDULED', 'PAUSED', 'COMPLETED', 'CANCELED'];

    it.each(invalidStates)('should throw when session is %s', (state) => {
      // Arrange
      const participantId = SessionTestFactories.createParticipantId();
      const session = SessionTestFactories.createSession({
        state: state as any,
        participants: [SessionTestFactories.createParticipantPresence(participantId).toPrimitives()],
      });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert
      expect(() => session.seatParticipant(participantId, at)).toThrow(SessionStateError);
      expect(() => session.seatParticipant(participantId, at)).toThrow('Cannot seat participant unless session is RUNNING');
    });
  });

  describe('throws SessionParticipantNotFoundError if participant not in roster', () => {
    it('should throw when participant does not exist', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const nonExistentParticipantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert
      expect(() => session.seatParticipant(nonExistentParticipantId, at)).toThrow(SessionParticipantNotFoundError);
      expect(() => session.seatParticipant(nonExistentParticipantId, at)).toThrow('Participant not found');
    });

    it('should include context in participant not found error', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const nonExistentParticipantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert
      try {
        session.seatParticipant(nonExistentParticipantId, at);
        fail('Expected SessionParticipantNotFoundError to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(SessionParticipantNotFoundError);
        expect((error as any).context).toMatchObject({
          participantId: nonExistentParticipantId.toPrimitives(),
          sessionId: session.sessionId.toPrimitives(),
        });
      }
    });
  });

  describe('when no active round: assigns to main room', () => {
    it('should seat participant in main room when currentRoundIndex < 0', () => {
      // Arrange
      const participantId = SessionTestFactories.createParticipantId();
      const participant = SessionTestFactories.createParticipantPresence(participantId);
      const session = SessionTestFactories.createRunningSession({
        participants: [participant.toPrimitives()],
        currentRoundIndex: -1,
      });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.seatParticipant(participantId, at);

      // Assert
      expect(result).not.toBe(session);
      
      const seatedParticipant = result.findParticipantById(participantId);
      expect(seatedParticipant!.toPrimitives().currentRoomId).toBe(result.mainRoomId.toPrimitives());
      
      const mainRoom = result.findRoomById(result.mainRoomId);
      expect(mainRoom!.occupiedSeats).toHaveLength(1);
      expect(mainRoom!.occupiedSeats[0].currentParticipantId).toBe(participantId.toPrimitives());
    });

    it('should work with multiple participants', () => {
      // Arrange
      const participantId1 = SessionTestFactories.createParticipantId();
      const participantId2 = SessionTestFactories.createParticipantId();
      const participants = [
        SessionTestFactories.createParticipantPresence(participantId1).toPrimitives(),
        SessionTestFactories.createParticipantPresence(participantId2).toPrimitives(),
      ];
      const session = SessionTestFactories.createRunningSession({
        participants,
        currentRoundIndex: -1,
      });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result1 = session.seatParticipant(participantId1, at);
      const result2 = result1.seatParticipant(participantId2, at);

      // Assert
      const mainRoom = result2.findRoomById(result2.mainRoomId);
      expect(mainRoom!.occupiedSeats).toHaveLength(2);
      
      const occupiedParticipantIds = mainRoom!.occupiedSeats.map(seat => seat.currentParticipantId);
      expect(occupiedParticipantIds).toContain(participantId1.toPrimitives());
      expect(occupiedParticipantIds).toContain(participantId2.toPrimitives());
    });
  });

  describe('when active round: breakout room selection and creation', () => {
    describe('picks breakout room with smallest (occupied + reserved) among those with hasSpace=true', () => {
      it('should select empty breakout room over occupied one', () => {
        // Arrange
        const participantId = SessionTestFactories.createParticipantId();
        const participant = SessionTestFactories.createParticipantPresence(participantId);
        
        const rounds = [SessionTestFactories.createRoundInstance().toPrimitives()];
        
        // Create two breakout rooms: one empty, one with a participant
        const emptyRoom = SessionTestFactories.createRoom();
        const occupiedRoom = SessionTestFactories.createRoom().assignParticipantToSeat(
          SessionTestFactories.createParticipantId(),
          SessionTestFactories.createInstant()
        );
        
        const rooms = [
          SessionTestFactories.createRoom(
            SessionTestFactories.createRoomId('main-room-id')
          ).toPrimitives(),
          occupiedRoom.toPrimitives(), // Has 1 participant
          emptyRoom.toPrimitives(), // Empty - should be selected
        ];
        
        const session = SessionTestFactories.createRunningSession({
          participants: [participant.toPrimitives()],
          rounds,
          rooms,
          currentRoundIndex: 0,
          mainRoomId: 'main-room-id',
        });
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act
        const result = session.seatParticipant(participantId, at);

        // Assert
        const seatedParticipant = result.findParticipantById(participantId);
        expect(seatedParticipant!.toPrimitives().currentRoomId).toBe(emptyRoom.roomId.toPrimitives());
        
        const targetRoom = result.findRoomById(emptyRoom.roomId);
        expect(targetRoom!.occupiedSeats).toHaveLength(1);
        expect(targetRoom!.occupiedSeats[0].currentParticipantId).toBe(participantId.toPrimitives());
      });

      it('should consider reserved seats in selection', () => {
        // Arrange
        const participantId = SessionTestFactories.createParticipantId();
        const participant = SessionTestFactories.createParticipantPresence(participantId);
        
        const rounds = [SessionTestFactories.createRoundInstance().toPrimitives()];
        
        // Create rooms with different (occupied + reserved) counts
        const roomWithReserved = SessionTestFactories.createRoom().reserveSeatForReconnect(
          SessionTestFactories.createParticipantId(),
          SessionTestFactories.createInstant()
        ); // Has 1 reserved
        const emptyRoom = SessionTestFactories.createRoom(); // Empty
        
        const rooms = [
          SessionTestFactories.createRoom(
            SessionTestFactories.createRoomId('main-room-id')
          ).toPrimitives(),
          roomWithReserved.toPrimitives(), // Has 1 reserved
          emptyRoom.toPrimitives(), // Empty - should be selected
        ];
        
        const session = SessionTestFactories.createRunningSession({
          participants: [participant.toPrimitives()],
          rounds,
          rooms,
          currentRoundIndex: 0,
          mainRoomId: 'main-room-id',
        });
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act
        const result = session.seatParticipant(participantId, at);

        // Assert
        const seatedParticipant = result.findParticipantById(participantId);
        expect(seatedParticipant!.toPrimitives().currentRoomId).toBe(emptyRoom.roomId.toPrimitives());
      });

      it('should exclude main room from breakout selection', () => {
        // Arrange
        const participantId = SessionTestFactories.createParticipantId();
        const participant = SessionTestFactories.createParticipantPresence(participantId);
        
        const rounds = [SessionTestFactories.createRoundInstance().toPrimitives()];
        const breakoutRoom = SessionTestFactories.createRoom();
        
        const rooms = [
          SessionTestFactories.createRoom(
            SessionTestFactories.createRoomId('main-room-id')
          ).toPrimitives(), // Main room (should be excluded)
          breakoutRoom.toPrimitives(), // Breakout room (should be selected)
        ];
        
        const session = SessionTestFactories.createRunningSession({
          participants: [participant.toPrimitives()],
          rounds,
          rooms,
          currentRoundIndex: 0,
          mainRoomId: 'main-room-id',
        });
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act
        const result = session.seatParticipant(participantId, at);

        // Assert
        const seatedParticipant = result.findParticipantById(participantId);
        expect(seatedParticipant!.toPrimitives().currentRoomId).toBe(breakoutRoom.roomId.toPrimitives());
        expect(seatedParticipant!.toPrimitives().currentRoomId).not.toBe('main-room-id');
      });

      it('should exclude rooms without space', () => {
        // Arrange
        const participantId = SessionTestFactories.createParticipantId();
        const participant = SessionTestFactories.createParticipantPresence(participantId);
        
        const rounds = [SessionTestFactories.createRoundInstance().toPrimitives()];
        
        // Create a full room (no space)
        const fullRoom = SessionTestFactories.createRoom(
          undefined,
          SessionTestFactories.createDefaultRoomConfig({ maxSeats: 1 })
        ).assignParticipantToSeat(
          SessionTestFactories.createParticipantId(),
          SessionTestFactories.createInstant()
        );
        
        const emptyRoom = SessionTestFactories.createRoom();
        
        const rooms = [
          SessionTestFactories.createRoom(
            SessionTestFactories.createRoomId('main-room-id')
          ).toPrimitives(),
          fullRoom.toPrimitives(), // Full room (should be excluded)
          emptyRoom.toPrimitives(), // Empty room (should be selected)
        ];
        
        const session = SessionTestFactories.createRunningSession({
          participants: [participant.toPrimitives()],
          rounds,
          rooms,
          currentRoundIndex: 0,
          mainRoomId: 'main-room-id',
        });
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act
        const result = session.seatParticipant(participantId, at);

        // Assert
        const seatedParticipant = result.findParticipantById(participantId);
        expect(seatedParticipant!.toPrimitives().currentRoomId).toBe(emptyRoom.roomId.toPrimitives());
        expect(seatedParticipant!.toPrimitives().currentRoomId).not.toBe(fullRoom.roomId.toPrimitives());
      });
    });

    describe('if no room has space → creates new room and assigns', () => {
      it('should create new room when all breakout rooms are full', () => {
        // Arrange
        const participantId = SessionTestFactories.createParticipantId();
        const participant = SessionTestFactories.createParticipantPresence(participantId);
        
        const rounds = [SessionTestFactories.createRoundInstance().toPrimitives()];
        
        // Create full breakout rooms
        const fullRoom1 = SessionTestFactories.createRoom(
          undefined,
          SessionTestFactories.createDefaultRoomConfig({ maxSeats: 1 })
        ).assignParticipantToSeat(
          SessionTestFactories.createParticipantId(),
          SessionTestFactories.createInstant()
        );
        
        const fullRoom2 = SessionTestFactories.createRoom(
          undefined,
          SessionTestFactories.createDefaultRoomConfig({ maxSeats: 1 })
        ).assignParticipantToSeat(
          SessionTestFactories.createParticipantId(),
          SessionTestFactories.createInstant()
        );
        
        const rooms = [
          SessionTestFactories.createRoom(
            SessionTestFactories.createRoomId('main-room-id')
          ).toPrimitives(),
          fullRoom1.toPrimitives(),
          fullRoom2.toPrimitives(),
        ];
        
        const session = SessionTestFactories.createRunningSession({
          participants: [participant.toPrimitives()],
          rounds,
          rooms,
          currentRoundIndex: 0,
          mainRoomId: 'main-room-id',
        });
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act
        const result = session.seatParticipant(participantId, at);

        // Assert
        expect(result.allRooms).toHaveLength(4); // main + 2 full + 1 new
        
        const seatedParticipant = result.findParticipantById(participantId);
        expect(seatedParticipant!.toPrimitives().currentRoomId).not.toBe('main-room-id');
        expect(seatedParticipant!.toPrimitives().currentRoomId).not.toBe(fullRoom1.roomId.toPrimitives());
        expect(seatedParticipant!.toPrimitives().currentRoomId).not.toBe(fullRoom2.roomId.toPrimitives());
        
        // The new room should have the participant
        const newRoomId = seatedParticipant!.toPrimitives().currentRoomId!;
        const newRoom = result.findRoomById(SessionTestFactories.createRoomId(newRoomId));
        expect(newRoom!.occupiedSeats).toHaveLength(1);
        expect(newRoom!.occupiedSeats[0].currentParticipantId).toBe(participantId.toPrimitives());
      });

      it('should create new room when no breakout rooms exist', () => {
        // Arrange
        const participantId = SessionTestFactories.createParticipantId();
        const participant = SessionTestFactories.createParticipantPresence(participantId);
        
        const rounds = [SessionTestFactories.createRoundInstance().toPrimitives()];
        
        const rooms = [
          SessionTestFactories.createRoom(
            SessionTestFactories.createRoomId('main-room-id')
          ).toPrimitives(), // Only main room
        ];
        
        const session = SessionTestFactories.createRunningSession({
          participants: [participant.toPrimitives()],
          rounds,
          rooms,
          currentRoundIndex: 0,
          mainRoomId: 'main-room-id',
        });
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act
        const result = session.seatParticipant(participantId, at);

        // Assert
        expect(result.allRooms).toHaveLength(2); // main + 1 new
        
        const seatedParticipant = result.findParticipantById(participantId);
        expect(seatedParticipant!.toPrimitives().currentRoomId).not.toBe('main-room-id');
        
        // The new room should have the participant
        const newRoomId = seatedParticipant!.toPrimitives().currentRoomId!;
        const newRoom = result.findRoomById(SessionTestFactories.createRoomId(newRoomId));
        expect(newRoom!.occupiedSeats).toHaveLength(1);
        expect(newRoom!.occupiedSeats[0].currentParticipantId).toBe(participantId.toPrimitives());
      });
    });
  });

  describe('immutability verification', () => {
    it('should return new instance when seating participant', () => {
      // Arrange
      const participantId = SessionTestFactories.createParticipantId();
      const participant = SessionTestFactories.createParticipantPresence(participantId);
      const session = SessionTestFactories.createRunningSession({
        participants: [participant.toPrimitives()],
        currentRoundIndex: -1,
      });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.seatParticipant(participantId, at);

      // Assert
      expect(result).not.toBe(session);
      
      // Original should be unchanged
      const originalParticipant = session.findParticipantById(participantId);
      expect(originalParticipant!.toPrimitives().currentRoomId).toBeUndefined();
      
      // Result should have seated participant
      const resultParticipant = result.findParticipantById(participantId);
      expect(resultParticipant!.toPrimitives().currentRoomId).toBeDefined();
    });
  });
});
