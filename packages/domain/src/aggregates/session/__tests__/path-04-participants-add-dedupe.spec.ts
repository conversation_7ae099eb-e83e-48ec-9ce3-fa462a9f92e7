import { Session } from '../session.aggregate';
import { ParticipantRole } from '../../../participants/types/participant-role.enum';
import { SessionTestFactories } from './helpers/factories';

describe('Path 4: Participants – add & dedupe', () => {
  describe('addParticipant adds presence once', () => {
    it('should add new participant to empty session', () => {
      // Arrange
      const session = SessionTestFactories.createScheduledSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.addParticipant(participantId, at);

      // Assert
      expect(result).not.toBe(session); // New instance
      expect(result.allParticipants).toHaveLength(1);
      expect(result.participantCount).toBe(1);
      expect(result.allParticipants[0].participantId.toPrimitives()).toBe(participantId.toPrimitives());
      expect(session.allParticipants).toHaveLength(0); // Original unchanged
    });

    it('should add participant with default MEMBER role', () => {
      // Arrange
      const session = SessionTestFactories.createScheduledSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.addParticipant(participantId, at);

      // Assert
      const addedParticipant = result.allParticipants[0];
      expect(addedParticipant.currentTags).toContain('role-MEMBER');
    });

    it('should add participant with specified role', () => {
      // Arrange
      const session = SessionTestFactories.createScheduledSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.addParticipant(participantId, at, ParticipantRole.HOST);

      // Assert
      const addedParticipant = result.allParticipants[0];
      expect(addedParticipant.currentTags).toContain('role-HOST');
    });

    it('should add multiple different participants', () => {
      // Arrange
      const session = SessionTestFactories.createScheduledSession();
      const participantId1 = SessionTestFactories.createParticipantId();
      const participantId2 = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result1 = session.addParticipant(participantId1, at);
      const result2 = result1.addParticipant(participantId2, at);

      // Assert
      expect(result2.allParticipants).toHaveLength(2);
      expect(result2.participantCount).toBe(2);
      
      const participantIds = result2.allParticipants.map(p => p.participantId.toPrimitives());
      expect(participantIds).toContain(participantId1.toPrimitives());
      expect(participantIds).toContain(participantId2.toPrimitives());
    });

    it('should work in any session state', () => {
      // Arrange
      const states = ['SCHEDULED', 'RUNNING', 'PAUSED', 'COMPLETED', 'CANCELED'];
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      states.forEach((state) => {
        const session = SessionTestFactories.createSession({ state: state as any });

        // Act
        const result = session.addParticipant(participantId, at);

        // Assert
        expect(result.allParticipants).toHaveLength(1);
        expect(result.participantCount).toBe(1);
      });
    });
  });

  describe('repeated call for same id is a no-op', () => {
    it('should not add duplicate participant', () => {
      // Arrange
      const session = SessionTestFactories.createScheduledSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result1 = session.addParticipant(participantId, at);
      const result2 = result1.addParticipant(participantId, at);

      // Assert
      expect(result2).toBe(result1); // Same instance (no-op)
      expect(result2.allParticipants).toHaveLength(1);
      expect(result2.participantCount).toBe(1);
    });

    it('should be no-op even with different role', () => {
      // Arrange
      const session = SessionTestFactories.createScheduledSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result1 = session.addParticipant(participantId, at, ParticipantRole.MEMBER);
      const result2 = result1.addParticipant(participantId, at, ParticipantRole.HOST);

      // Assert
      expect(result2).toBe(result1); // Same instance (no-op)
      expect(result2.allParticipants).toHaveLength(1);
      
      // Original role should be preserved
      const participant = result2.allParticipants[0];
      expect(participant.currentTags).toContain('role-MEMBER');
      expect(participant.currentTags).not.toContain('role-HOST');
    });

    it('should be no-op even with different timestamp', () => {
      // Arrange
      const session = SessionTestFactories.createScheduledSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at1 = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);
      const at2 = SessionTestFactories.createInstant(SessionTestFactories.EVEN_LATER_TIMESTAMP);

      // Act
      const result1 = session.addParticipant(participantId, at1);
      const result2 = result1.addParticipant(participantId, at2);

      // Assert
      expect(result2).toBe(result1); // Same instance (no-op)
      expect(result2.allParticipants).toHaveLength(1);
    });

    it('should handle multiple no-op calls', () => {
      // Arrange
      const session = SessionTestFactories.createScheduledSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      let result = session.addParticipant(participantId, at);
      for (let i = 0; i < 5; i++) {
        const nextResult = result.addParticipant(participantId, at);
        expect(nextResult).toBe(result); // Should always be same instance
        result = nextResult;
      }

      // Assert
      expect(result.allParticipants).toHaveLength(1);
      expect(result.participantCount).toBe(1);
    });
  });

  describe('participant presence verification', () => {
    it('should create participant with correct properties', () => {
      // Arrange
      const session = SessionTestFactories.createScheduledSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.addParticipant(participantId, at, ParticipantRole.HOST);

      // Assert
      const participant = result.allParticipants[0];
      expect(participant.participantId.toPrimitives()).toBe(participantId.toPrimitives());
      expect(participant.currentTags).toContain('role-HOST');
      expect(participant.toPrimitives().joinedAt).toBeUndefined(); // Not joined yet, just added to roster
      expect(participant.toPrimitives().leftAt).toBeUndefined();
      expect(participant.toPrimitives().currentRoomId).toBeUndefined(); // Not seated yet
    });

    it('should preserve existing participants when adding new one', () => {
      // Arrange
      const existingParticipant = SessionTestFactories.createParticipantPresence();
      const session = SessionTestFactories.createSession({
        participants: [existingParticipant.toPrimitives()],
      });
      const newParticipantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.addParticipant(newParticipantId, at);

      // Assert
      expect(result.allParticipants).toHaveLength(2);
      
      const participantIds = result.allParticipants.map(p => p.participantId.toPrimitives());
      expect(participantIds).toContain(existingParticipant.participantId.toPrimitives());
      expect(participantIds).toContain(newParticipantId.toPrimitives());
    });
  });

  describe('immutability verification', () => {
    it('should return new instance when adding participant', () => {
      // Arrange
      const session = SessionTestFactories.createScheduledSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.addParticipant(participantId, at);

      // Assert
      expect(result).not.toBe(session);
      expect(session.allParticipants).toHaveLength(0); // Original unchanged
      expect(result.allParticipants).toHaveLength(1);
    });

    it('should preserve all other properties', () => {
      // Arrange
      const rounds = [SessionTestFactories.createRoundInstance().toPrimitives()];
      const session = SessionTestFactories.createScheduledSession({
        rounds,
        currentRoundIndex: 0,
      });
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.addParticipant(participantId, at);

      // Assert
      expect(result.sessionId).toBe(session.sessionId);
      expect(result.config).toBe(session.config);
      expect(result.currentState).toBe(session.currentState);
      expect(result.createdByUserId).toBe(session.createdByUserId);
      expect(result.mainRoomId).toBe(session.mainRoomId);
      expect(result.createdAt).toBe(session.createdAt);
      expect(result.allRounds).toHaveLength(session.allRounds.length);
      expect(result.allRooms).toHaveLength(session.allRooms.length);
      expect(result.toPrimitives().currentRoundIndex).toBe(session.toPrimitives().currentRoundIndex);
      expect(result.toPrimitives().hostId).toBe(session.toPrimitives().hostId);
    });

    it('should not mutate participant arrays', () => {
      // Arrange
      const session = SessionTestFactories.createScheduledSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.addParticipant(participantId, at);
      const participants = result.allParticipants;

      // Mutate returned array
      participants.push(SessionTestFactories.createParticipantPresence());

      // Assert - Internal state should be unchanged
      expect(result.allParticipants).toHaveLength(1);
      expect(result.participantCount).toBe(1);
    });
  });
});
