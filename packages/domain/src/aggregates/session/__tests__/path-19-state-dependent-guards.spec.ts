import { Session } from '../session.aggregate';
import { SessionStateError } from '../session.errors';
import { SessionTestFactories } from './helpers/factories';

describe('Path 19: State-dependent guards everywhere', () => {
  describe('SCHEDULED state guards', () => {
    it('should allow only startSession and cancelSession from SCHEDULED', () => {
      // Arrange
      const session = SessionTestFactories.createSession({ state: 'SCHEDULED' });
      const participantId = SessionTestFactories.createParticipantId();
      const roundSpec = SessionTestFactories.createRoundSpec();
      const roomId = SessionTestFactories.createRoomId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert - Allowed operations
      expect(() => session.startSession(at)).not.toThrow();
      expect(() => session.cancelSession(at)).not.toThrow();

      // Act & Assert - Forbidden operations
      expect(() => session.pauseSession(at)).toThrow(SessionStateError);
      expect(() => session.resumeSession(at)).toThrow(SessionStateError);
      expect(() => session.completeSession(at)).toThrow(SessionStateError);
      expect(() => session.onJoin(participantId, at)).toThrow(SessionStateError);
      expect(() => session.onLeave(participantId, at)).toThrow(SessionStateError);
      expect(() => session.addRound(roundSpec, at)).toThrow(SessionStateError);
      expect(() => session.startRound(0, at)).toThrow(SessionStateError);
      expect(() => session.endCurrentRound(at)).toThrow(SessionStateError);
      expect(() => session.closeCurrentRound(at)).toThrow(SessionStateError);
      expect(() => session.assignParticipantToRoom(participantId, roomId, at)).toThrow(SessionStateError);
      expect(() => session.makeRoomReady(roomId, at)).toThrow(SessionStateError);
      expect(() => session.makeRoomClosed(roomId, at)).toThrow(SessionStateError);
      expect(() => session.returnAllParticipantsToMain(at)).toThrow(SessionStateError);
      expect(() => session.rotateBreakouts(at)).toThrow(SessionStateError);
      expect(() => session.releaseAllReservedSeats(at)).toThrow(SessionStateError);
      expect(() => session.restoreReservedSeat(participantId, at)).toThrow(SessionStateError);
    });
  });

  describe('RUNNING state guards', () => {
    it('should allow most operations from RUNNING state', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantId = SessionTestFactories.createParticipantId();
      const roundSpec = SessionTestFactories.createRoundSpec();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add participant first for operations that require it
      const sessionWithParticipant = session.addParticipant(participantId, 'MEMBER', at);

      // Act & Assert - Allowed operations
      expect(() => session.pauseSession(at)).not.toThrow();
      expect(() => session.completeSession(at)).not.toThrow();
      expect(() => session.cancelSession(at)).not.toThrow();
      expect(() => sessionWithParticipant.onJoin(participantId, at)).not.toThrow();
      expect(() => session.addRound(roundSpec, at)).not.toThrow();
      expect(() => session.returnAllParticipantsToMain(at)).not.toThrow();
      expect(() => session.releaseAllReservedSeats(at)).not.toThrow();

      // Act & Assert - Forbidden operations
      expect(() => session.startSession(at)).not.toThrow(); // Idempotent
      expect(() => session.resumeSession(at)).not.toThrow(); // Idempotent
    });

    it('should enforce participant existence for participant-dependent operations', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const nonExistentParticipantId = SessionTestFactories.createParticipantId();
      const roomId = SessionTestFactories.createRoomId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert - Should throw for non-existent participants
      expect(() => session.onJoin(nonExistentParticipantId, at)).toThrow();
      expect(() => session.onLeave(nonExistentParticipantId, at)).not.toThrow(); // No-op
      expect(() => session.assignParticipantToRoom(nonExistentParticipantId, roomId, at)).toThrow();
      expect(() => session.restoreReservedSeat(nonExistentParticipantId, at)).toThrow();
    });
  });

  describe('PAUSED state guards', () => {
    it('should allow only resumeSession, completeSession, and cancelSession from PAUSED', () => {
      // Arrange
      const session = SessionTestFactories.createSession({ state: 'PAUSED' });
      const participantId = SessionTestFactories.createParticipantId();
      const roundSpec = SessionTestFactories.createRoundSpec();
      const roomId = SessionTestFactories.createRoomId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert - Allowed operations
      expect(() => session.resumeSession(at)).not.toThrow();
      expect(() => session.completeSession(at)).not.toThrow();
      expect(() => session.cancelSession(at)).not.toThrow();

      // Act & Assert - Forbidden operations
      expect(() => session.startSession(at)).toThrow(SessionStateError);
      expect(() => session.pauseSession(at)).not.toThrow(); // Idempotent
      expect(() => session.onJoin(participantId, at)).toThrow(SessionStateError);
      expect(() => session.onLeave(participantId, at)).toThrow(SessionStateError);
      expect(() => session.addRound(roundSpec, at)).toThrow(SessionStateError);
      expect(() => session.startRound(0, at)).toThrow(SessionStateError);
      expect(() => session.endCurrentRound(at)).toThrow(SessionStateError);
      expect(() => session.closeCurrentRound(at)).toThrow(SessionStateError);
      expect(() => session.assignParticipantToRoom(participantId, roomId, at)).toThrow(SessionStateError);
      expect(() => session.makeRoomReady(roomId, at)).toThrow(SessionStateError);
      expect(() => session.makeRoomClosed(roomId, at)).toThrow(SessionStateError);
      expect(() => session.returnAllParticipantsToMain(at)).toThrow(SessionStateError);
      expect(() => session.rotateBreakouts(at)).toThrow(SessionStateError);
      expect(() => session.releaseAllReservedSeats(at)).toThrow(SessionStateError);
      expect(() => session.restoreReservedSeat(participantId, at)).toThrow(SessionStateError);
    });
  });

  describe('COMPLETED state guards', () => {
    it('should allow only idempotent operations from COMPLETED state', () => {
      // Arrange
      const session = SessionTestFactories.createSession({ state: 'COMPLETED' });
      const participantId = SessionTestFactories.createParticipantId();
      const roundSpec = SessionTestFactories.createRoundSpec();
      const roomId = SessionTestFactories.createRoomId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert - Allowed operations (idempotent)
      expect(() => session.completeSession(at)).not.toThrow(); // Idempotent
      expect(() => session.cancelSession(at)).not.toThrow(); // Idempotent

      // Act & Assert - Forbidden operations
      expect(() => session.startSession(at)).toThrow(SessionStateError);
      expect(() => session.pauseSession(at)).toThrow(SessionStateError);
      expect(() => session.resumeSession(at)).toThrow(SessionStateError);
      expect(() => session.onJoin(participantId, at)).toThrow(SessionStateError);
      expect(() => session.onLeave(participantId, at)).toThrow(SessionStateError);
      expect(() => session.addRound(roundSpec, at)).toThrow(SessionStateError);
      expect(() => session.startRound(0, at)).toThrow(SessionStateError);
      expect(() => session.endCurrentRound(at)).toThrow(SessionStateError);
      expect(() => session.closeCurrentRound(at)).toThrow(SessionStateError);
      expect(() => session.assignParticipantToRoom(participantId, roomId, at)).toThrow(SessionStateError);
      expect(() => session.makeRoomReady(roomId, at)).toThrow(SessionStateError);
      expect(() => session.makeRoomClosed(roomId, at)).toThrow(SessionStateError);
      expect(() => session.returnAllParticipantsToMain(at)).toThrow(SessionStateError);
      expect(() => session.rotateBreakouts(at)).toThrow(SessionStateError);
      expect(() => session.releaseAllReservedSeats(at)).toThrow(SessionStateError);
      expect(() => session.restoreReservedSeat(participantId, at)).toThrow(SessionStateError);
    });
  });

  describe('CANCELED state guards', () => {
    it('should allow only idempotent operations from CANCELED state', () => {
      // Arrange
      const session = SessionTestFactories.createSession({ state: 'CANCELED' });
      const participantId = SessionTestFactories.createParticipantId();
      const roundSpec = SessionTestFactories.createRoundSpec();
      const roomId = SessionTestFactories.createRoomId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert - Allowed operations (idempotent)
      expect(() => session.cancelSession(at)).not.toThrow(); // Idempotent

      // Act & Assert - Forbidden operations
      expect(() => session.startSession(at)).toThrow(SessionStateError);
      expect(() => session.pauseSession(at)).toThrow(SessionStateError);
      expect(() => session.resumeSession(at)).toThrow(SessionStateError);
      expect(() => session.completeSession(at)).toThrow(SessionStateError);
      expect(() => session.onJoin(participantId, at)).toThrow(SessionStateError);
      expect(() => session.onLeave(participantId, at)).toThrow(SessionStateError);
      expect(() => session.addRound(roundSpec, at)).toThrow(SessionStateError);
      expect(() => session.startRound(0, at)).toThrow(SessionStateError);
      expect(() => session.endCurrentRound(at)).toThrow(SessionStateError);
      expect(() => session.closeCurrentRound(at)).toThrow(SessionStateError);
      expect(() => session.assignParticipantToRoom(participantId, roomId, at)).toThrow(SessionStateError);
      expect(() => session.makeRoomReady(roomId, at)).toThrow(SessionStateError);
      expect(() => session.makeRoomClosed(roomId, at)).toThrow(SessionStateError);
      expect(() => session.returnAllParticipantsToMain(at)).toThrow(SessionStateError);
      expect(() => session.rotateBreakouts(at)).toThrow(SessionStateError);
      expect(() => session.releaseAllReservedSeats(at)).toThrow(SessionStateError);
      expect(() => session.restoreReservedSeat(participantId, at)).toThrow(SessionStateError);
    });
  });

  describe('state-independent operations', () => {
    it('should allow participant management in any state', () => {
      // Arrange
      const states = ['SCHEDULED', 'RUNNING', 'PAUSED', 'COMPLETED', 'CANCELED'];
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      for (const state of states) {
        const session = SessionTestFactories.createSession({ state: state as any });

        // Act & Assert - Should work in any state
        expect(() => session.addParticipant(participantId, 'MEMBER', at)).not.toThrow();
        
        const sessionWithParticipant = session.addParticipant(participantId, 'MEMBER', at);
        expect(() => sessionWithParticipant.setHost(participantId, at)).not.toThrow();
        expect(() => sessionWithParticipant.clearHost(at)).not.toThrow();
      }
    });

    it('should allow read operations in any state', () => {
      // Arrange
      const states = ['SCHEDULED', 'RUNNING', 'PAUSED', 'COMPLETED', 'CANCELED'];
      const participantId = SessionTestFactories.createParticipantId();

      for (const state of states) {
        const session = SessionTestFactories.createSession({ state: state as any });
        const sessionWithParticipant = session.addParticipant(participantId, 'MEMBER', at);

        // Act & Assert - Read operations should work in any state
        expect(() => session.toPrimitives()).not.toThrow();
        expect(() => session.sessionId).not.toThrow();
        expect(() => session.currentState).not.toThrow();
        expect(() => session.participantCount).not.toThrow();
        expect(() => session.allRooms).not.toThrow();
        expect(() => session.allRounds).not.toThrow();
        expect(() => sessionWithParticipant.getParticipant(participantId)).not.toThrow();
        expect(() => session.getRoom(session.mainRoomId)).not.toThrow();
      }
    });
  });

  describe('guard error messages', () => {
    it('should provide specific error messages for each forbidden operation', () => {
      // Arrange
      const session = SessionTestFactories.createSession({ state: 'COMPLETED' });
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert - Each operation should have a specific error message
      const operations = [
        { fn: () => session.startSession(at), expectedMessage: 'Cannot start session from COMPLETED state' },
        { fn: () => session.pauseSession(at), expectedMessage: 'Cannot pause unless session is RUNNING' },
        { fn: () => session.onJoin(participantId, at), expectedMessage: 'Cannot join unless session is RUNNING' },
        { fn: () => session.addRound(SessionTestFactories.createRoundSpec(), at), expectedMessage: 'Cannot add round unless session is RUNNING' },
        { fn: () => session.rotateBreakouts(at), expectedMessage: 'Cannot rotate breakouts unless session is RUNNING' },
      ];

      for (const { fn, expectedMessage } of operations) {
        expect(fn).toThrow(SessionStateError);
        expect(fn).toThrow(expectedMessage);
      }
    });

    it('should include current state in error context', () => {
      // Arrange
      const session = SessionTestFactories.createSession({ state: 'PAUSED' });
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert
      try {
        session.onJoin(participantId, at);
        fail('Expected SessionStateError to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(SessionStateError);
        expect((error as any).context).toMatchObject({
          currentState: 'PAUSED',
          requiredState: 'RUNNING',
          operation: 'onJoin',
        });
      }
    });
  });

  describe('state transition validation', () => {
    it('should validate state transitions according to business rules', () => {
      // Arrange & Act & Assert - Valid transitions
      const scheduled = SessionTestFactories.createSession({ state: 'SCHEDULED' });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // SCHEDULED -> RUNNING
      expect(() => scheduled.startSession(at)).not.toThrow();
      
      // SCHEDULED -> CANCELED
      expect(() => scheduled.cancelSession(at)).not.toThrow();

      // RUNNING -> PAUSED
      const running = scheduled.startSession(at);
      expect(() => running.pauseSession(at)).not.toThrow();

      // RUNNING -> COMPLETED
      expect(() => running.completeSession(at)).not.toThrow();

      // RUNNING -> CANCELED
      expect(() => running.cancelSession(at)).not.toThrow();

      // PAUSED -> RUNNING
      const paused = running.pauseSession(at);
      expect(() => paused.resumeSession(at)).not.toThrow();

      // PAUSED -> COMPLETED
      expect(() => paused.completeSession(at)).not.toThrow();

      // PAUSED -> CANCELED
      expect(() => paused.cancelSession(at)).not.toThrow();
    });

    it('should prevent invalid state transitions', () => {
      // Arrange
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert - Invalid transitions
      const completed = SessionTestFactories.createSession({ state: 'COMPLETED' });
      expect(() => completed.startSession(at)).toThrow(SessionStateError);
      expect(() => completed.pauseSession(at)).toThrow(SessionStateError);
      expect(() => completed.resumeSession(at)).toThrow(SessionStateError);

      const canceled = SessionTestFactories.createSession({ state: 'CANCELED' });
      expect(() => canceled.startSession(at)).toThrow(SessionStateError);
      expect(() => canceled.pauseSession(at)).toThrow(SessionStateError);
      expect(() => canceled.resumeSession(at)).toThrow(SessionStateError);
      expect(() => canceled.completeSession(at)).toThrow(SessionStateError);

      const scheduled = SessionTestFactories.createSession({ state: 'SCHEDULED' });
      expect(() => scheduled.pauseSession(at)).toThrow(SessionStateError);
      expect(() => scheduled.resumeSession(at)).toThrow(SessionStateError);
      expect(() => scheduled.completeSession(at)).toThrow(SessionStateError);
    });
  });

  describe('comprehensive state guard coverage', () => {
    it('should enforce guards consistently across all operations', () => {
      // Arrange
      const restrictedStates = ['SCHEDULED', 'PAUSED', 'COMPLETED', 'CANCELED'];
      const participantId = SessionTestFactories.createParticipantId();
      const roundSpec = SessionTestFactories.createRoundSpec();
      const roomId = SessionTestFactories.createRoomId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Operations that require RUNNING state
      const runningOnlyOperations = [
        (session: Session) => session.onJoin(participantId, at),
        (session: Session) => session.addRound(roundSpec, at),
        (session: Session) => session.startRound(0, at),
        (session: Session) => session.endCurrentRound(at),
        (session: Session) => session.closeCurrentRound(at),
        (session: Session) => session.assignParticipantToRoom(participantId, roomId, at),
        (session: Session) => session.makeRoomReady(roomId, at),
        (session: Session) => session.makeRoomClosed(roomId, at),
        (session: Session) => session.returnAllParticipantsToMain(at),
        (session: Session) => session.rotateBreakouts(at),
        (session: Session) => session.releaseAllReservedSeats(at),
        (session: Session) => session.restoreReservedSeat(participantId, at),
      ];

      for (const state of restrictedStates) {
        const session = SessionTestFactories.createSession({ state: state as any });
        
        for (const operation of runningOnlyOperations) {
          if (state === 'SCHEDULED' && (
            operation.toString().includes('onLeave') || 
            operation.toString().includes('restoreReservedSeat')
          )) {
            // These operations might be no-ops in SCHEDULED state
            continue;
          }
          
          expect(() => operation(session)).toThrow(SessionStateError);
        }
      }
    });
  });
});
