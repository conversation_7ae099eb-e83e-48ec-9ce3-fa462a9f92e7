import { Session } from '../session.aggregate';
import { SessionTestFactories } from './helpers/factories';

describe('Path 2: Creation & main room config', () => {
  describe('Session.create initializes correctly', () => {
    it('should initialize session in SCHEDULED state', () => {
      // Arrange
      const sessionId = SessionTestFactories.createSessionId();
      const config = SessionTestFactories.createDefaultSessionConfig();
      const adminId = SessionTestFactories.createUuid();
      const mainRoomId = SessionTestFactories.createRoomId();
      const createdAt = SessionTestFactories.createInstant();

      // Act
      const session = Session.create(sessionId, config, adminId, mainRoomId, createdAt);

      // Assert
      expect(session.currentState).toBe('SCHEDULED');
      expect(session.isScheduled).toBe(true);
      expect(session.isRunning).toBe(false);
      expect(session.isPaused).toBe(false);
      expect(session.isCompleted).toBe(false);
      expect(session.isCanceled).toBe(false);
    });

    it('should initialize currentRoundIndex to -1', () => {
      // Arrange
      const sessionId = SessionTestFactories.createSessionId();
      const config = SessionTestFactories.createDefaultSessionConfig();
      const adminId = SessionTestFactories.createUuid();
      const mainRoomId = SessionTestFactories.createRoomId();
      const createdAt = SessionTestFactories.createInstant();

      // Act
      const session = Session.create(sessionId, config, adminId, mainRoomId, createdAt);

      // Assert
      expect(session.toPrimitives().currentRoundIndex).toBe(-1);
      expect(session.currentRound).toBeUndefined();
    });

    it('should initialize with exactly 1 room (main)', () => {
      // Arrange
      const sessionId = SessionTestFactories.createSessionId();
      const config = SessionTestFactories.createDefaultSessionConfig();
      const adminId = SessionTestFactories.createUuid();
      const mainRoomId = SessionTestFactories.createRoomId();
      const createdAt = SessionTestFactories.createInstant();

      // Act
      const session = Session.create(sessionId, config, adminId, mainRoomId, createdAt);

      // Assert
      expect(session.allRooms).toHaveLength(1);
      expect(session.allRooms[0].roomId.toPrimitives()).toBe(mainRoomId.toPrimitives());
      expect(session.mainRoomId.toPrimitives()).toBe(mainRoomId.toPrimitives());
    });

    it('should initialize with 0 participants', () => {
      // Arrange
      const sessionId = SessionTestFactories.createSessionId();
      const config = SessionTestFactories.createDefaultSessionConfig();
      const adminId = SessionTestFactories.createUuid();
      const mainRoomId = SessionTestFactories.createRoomId();
      const createdAt = SessionTestFactories.createInstant();

      // Act
      const session = Session.create(sessionId, config, adminId, mainRoomId, createdAt);

      // Assert
      expect(session.allParticipants).toHaveLength(0);
      expect(session.participantCount).toBe(0);
    });

    it('should initialize with 0 rounds', () => {
      // Arrange
      const sessionId = SessionTestFactories.createSessionId();
      const config = SessionTestFactories.createDefaultSessionConfig();
      const adminId = SessionTestFactories.createUuid();
      const mainRoomId = SessionTestFactories.createRoomId();
      const createdAt = SessionTestFactories.createInstant();

      // Act
      const session = Session.create(sessionId, config, adminId, mainRoomId, createdAt);

      // Assert
      expect(session.allRounds).toHaveLength(0);
    });

    it('should initialize without host', () => {
      // Arrange
      const sessionId = SessionTestFactories.createSessionId();
      const config = SessionTestFactories.createDefaultSessionConfig();
      const adminId = SessionTestFactories.createUuid();
      const mainRoomId = SessionTestFactories.createRoomId();
      const createdAt = SessionTestFactories.createInstant();

      // Act
      const session = Session.create(sessionId, config, adminId, mainRoomId, createdAt);

      // Assert
      expect(session.hasHost).toBe(false);
      expect(session.toPrimitives().hostId).toBeUndefined();
    });

    it('should preserve provided parameters', () => {
      // Arrange
      const sessionId = SessionTestFactories.createSessionId();
      const config = SessionTestFactories.createDefaultSessionConfig();
      const adminId = SessionTestFactories.createUuid();
      const mainRoomId = SessionTestFactories.createRoomId();
      const createdAt = SessionTestFactories.createInstant();

      // Act
      const session = Session.create(sessionId, config, adminId, mainRoomId, createdAt);

      // Assert
      expect(session.sessionId).toBe(sessionId);
      expect(session.config).toBe(config);
      expect(session.createdByUserId).toBe(adminId);
      expect(session.mainRoomId).toBe(mainRoomId);
      expect(session.createdAt).toBe(createdAt);
    });
  });

  describe('Main room config uses correct settings', () => {
    it('should set main room maxSeats to config.maxParticipants', () => {
      // Arrange
      const maxParticipants = 25;
      const config = SessionTestFactories.createDefaultSessionConfig({ maxParticipants });
      const sessionId = SessionTestFactories.createSessionId();
      const adminId = SessionTestFactories.createUuid();
      const mainRoomId = SessionTestFactories.createRoomId();
      const createdAt = SessionTestFactories.createInstant();

      // Act
      const session = Session.create(sessionId, config, adminId, mainRoomId, createdAt);

      // Assert
      const mainRoom = session.allRooms[0];
      expect(mainRoom.config.maxSeats).toBe(maxParticipants);
    });

    it('should copy minSeats from defaultRoomConfig', () => {
      // Arrange
      const minSeats = 3;
      const config = SessionTestFactories.createDefaultSessionConfig({
        defaultRoomConfig: {
          minSeats,
          maxSeats: 8,
          avoidSingleton: true,
          disconnectionPolicy: { holdSeatForMs: 30000 },
        },
      });
      const sessionId = SessionTestFactories.createSessionId();
      const adminId = SessionTestFactories.createUuid();
      const mainRoomId = SessionTestFactories.createRoomId();
      const createdAt = SessionTestFactories.createInstant();

      // Act
      const session = Session.create(sessionId, config, adminId, mainRoomId, createdAt);

      // Assert
      const mainRoom = session.allRooms[0];
      expect(mainRoom.config.minSeats).toBe(minSeats);
    });

    it('should copy avoidSingleton from defaultRoomConfig', () => {
      // Arrange
      const avoidSingleton = false;
      const config = SessionTestFactories.createDefaultSessionConfig({
        defaultRoomConfig: {
          minSeats: 2,
          maxSeats: 8,
          avoidSingleton,
          disconnectionPolicy: { holdSeatForMs: 30000 },
        },
      });
      const sessionId = SessionTestFactories.createSessionId();
      const adminId = SessionTestFactories.createUuid();
      const mainRoomId = SessionTestFactories.createRoomId();
      const createdAt = SessionTestFactories.createInstant();

      // Act
      const session = Session.create(sessionId, config, adminId, mainRoomId, createdAt);

      // Assert
      const mainRoom = session.allRooms[0];
      expect(mainRoom.config.avoidSingleton).toBe(avoidSingleton);
    });

    it('should use disconnectionPolicy from session config', () => {
      // Arrange
      const holdSeatForMs = 180000; // 3 minutes
      const config = SessionTestFactories.createDefaultSessionConfig({
        disconnectionPolicy: { holdSeatForMs },
      });
      const sessionId = SessionTestFactories.createSessionId();
      const adminId = SessionTestFactories.createUuid();
      const mainRoomId = SessionTestFactories.createRoomId();
      const createdAt = SessionTestFactories.createInstant();

      // Act
      const session = Session.create(sessionId, config, adminId, mainRoomId, createdAt);

      // Assert
      const mainRoom = session.allRooms[0];
      expect((mainRoom.config.toPrimitives() as any).disconnectionPolicy?.holdSeatForMs).toBe(holdSeatForMs);
    });

    it('should create main room in FILLING state', () => {
      // Arrange
      const sessionId = SessionTestFactories.createSessionId();
      const config = SessionTestFactories.createDefaultSessionConfig();
      const adminId = SessionTestFactories.createUuid();
      const mainRoomId = SessionTestFactories.createRoomId();
      const createdAt = SessionTestFactories.createInstant();

      // Act
      const session = Session.create(sessionId, config, adminId, mainRoomId, createdAt);

      // Assert
      const mainRoom = session.allRooms[0];
      expect(mainRoom.isFilling).toBe(true);
      expect(mainRoom.isReady).toBe(false);
      expect(mainRoom.isClosed).toBe(false);
    });

    it('should create main room with empty seats', () => {
      // Arrange
      const maxParticipants = 10;
      const config = SessionTestFactories.createDefaultSessionConfig({ maxParticipants });
      const sessionId = SessionTestFactories.createSessionId();
      const adminId = SessionTestFactories.createUuid();
      const mainRoomId = SessionTestFactories.createRoomId();
      const createdAt = SessionTestFactories.createInstant();

      // Act
      const session = Session.create(sessionId, config, adminId, mainRoomId, createdAt);

      // Assert
      const mainRoom = session.allRooms[0];
      expect(mainRoom.allSeats).toHaveLength(maxParticipants);
      expect(mainRoom.availableSeats).toHaveLength(maxParticipants);
      expect(mainRoom.occupiedSeats).toHaveLength(0);
      expect(mainRoom.reservedSeats).toHaveLength(0);
      expect(mainRoom.hasSpace).toBe(true);
      expect(mainRoom.size).toBe(0);
    });

    it('should handle different maxParticipants values', () => {
      // Arrange
      const testCases = [1, 5, 50, 100];

      testCases.forEach((maxParticipants) => {
        const config = SessionTestFactories.createDefaultSessionConfig({ maxParticipants });
        const sessionId = SessionTestFactories.createSessionId();
        const adminId = SessionTestFactories.createUuid();
        const mainRoomId = SessionTestFactories.createRoomId();
        const createdAt = SessionTestFactories.createInstant();

        // Act
        const session = Session.create(sessionId, config, adminId, mainRoomId, createdAt);

        // Assert
        const mainRoom = session.allRooms[0];
        expect(mainRoom.config.maxSeats).toBe(maxParticipants);
        expect(mainRoom.allSeats).toHaveLength(maxParticipants);
      });
    });
  });

  describe('immutability of created session', () => {
    it('should return new instance that cannot be mutated', () => {
      // Arrange
      const sessionId = SessionTestFactories.createSessionId();
      const config = SessionTestFactories.createDefaultSessionConfig();
      const adminId = SessionTestFactories.createUuid();
      const mainRoomId = SessionTestFactories.createRoomId();
      const createdAt = SessionTestFactories.createInstant();

      // Act
      const session = Session.create(sessionId, config, adminId, mainRoomId, createdAt);

      // Assert - Verify defensive copying of collections
      const rooms = session.allRooms;
      const participants = session.allParticipants;
      const rounds = session.allRounds;

      // Mutating returned arrays should not affect internal state
      rooms.push(SessionTestFactories.createRoom());
      participants.push(SessionTestFactories.createParticipantPresence());
      rounds.push(SessionTestFactories.createRoundInstance());

      expect(session.allRooms).toHaveLength(1);
      expect(session.allParticipants).toHaveLength(0);
      expect(session.allRounds).toHaveLength(0);
    });
  });
});
