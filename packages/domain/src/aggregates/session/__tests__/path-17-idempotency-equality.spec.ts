import { Session } from '../session.aggregate';
import { SessionTestFactories } from './helpers/factories';

describe('Path 17: Idempotency & equality checks', () => {
  describe('lifecycle operation idempotency', () => {
    it('should be idempotent when starting already running session', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.startSession(at);

      // Assert
      expect(result).toBe(session); // Same instance for idempotent operation
      expect(result.currentState).toBe('RUNNING');
    });

    it('should be idempotent when pausing already paused session', () => {
      // Arrange
      const session = SessionTestFactories.createSession({ state: 'PAUSED' });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.pauseSession(at);

      // Assert
      expect(result).toBe(session); // Same instance for idempotent operation
      expect(result.currentState).toBe('PAUSED');
    });

    it('should be idempotent when resuming already running session', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.resumeSession(at);

      // Assert
      expect(result).toBe(session); // Same instance for idempotent operation
      expect(result.currentState).toBe('RUNNING');
    });

    it('should be idempotent when completing already completed session', () => {
      // Arrange
      const session = SessionTestFactories.createSession({ state: 'COMPLETED' });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.completeSession(at);

      // Assert
      expect(result).toBe(session); // Same instance for idempotent operation
      expect(result.currentState).toBe('COMPLETED');
    });

    it('should be idempotent when canceling already canceled session', () => {
      // Arrange
      const session = SessionTestFactories.createSession({ state: 'CANCELED' });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.cancelSession(at);

      // Assert
      expect(result).toBe(session); // Same instance for idempotent operation
      expect(result.currentState).toBe('CANCELED');
    });
  });

  describe('participant operation idempotency', () => {
    it('should be idempotent when adding same participant multiple times', () => {
      // Arrange
      const session = SessionTestFactories.createSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result1 = session.addParticipant(participantId, 'MEMBER', at);
      const result2 = result1.addParticipant(participantId, 'MEMBER', at);
      const result3 = result2.addParticipant(participantId, 'HOST', at); // Different role

      // Assert
      expect(result2).toBe(result1); // Same instance for idempotent operation
      expect(result3).toBe(result2); // Same instance even with different role
      expect(result3.participantCount).toBe(1);
      
      // Role should remain as originally set
      const participant = result3.getParticipant(participantId);
      expect(participant?.role).toBe('MEMBER');
    });

    it('should be idempotent when setting same host multiple times', () => {
      // Arrange
      const session = SessionTestFactories.createSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      const sessionWithParticipant = session.addParticipant(participantId, 'MEMBER', at);

      // Act
      const result1 = sessionWithParticipant.setHost(participantId, at);
      const result2 = result1.setHost(participantId, at);

      // Assert
      expect(result2).toBe(result1); // Same instance for idempotent operation
      expect(result2.toPrimitives().hostId).toBe(participantId.toPrimitives());
    });

    it('should be idempotent when clearing host multiple times', () => {
      // Arrange
      const session = SessionTestFactories.createSession();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result1 = session.clearHost(at);
      const result2 = result1.clearHost(at);

      // Assert
      expect(result1).toBe(session); // Same instance for idempotent operation
      expect(result2).toBe(result1); // Same instance for idempotent operation
      expect(result2.toPrimitives().hostId).toBeNull();
    });
  });

  describe('room operation idempotency', () => {
    it('should be idempotent when making already ready room ready', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantIds = [
        SessionTestFactories.createParticipantId(),
        SessionTestFactories.createParticipantId(),
        SessionTestFactories.createParticipantId(),
      ];
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add participants and create breakout room
      let sessionWithParticipants = session;
      for (const participantId of participantIds) {
        sessionWithParticipants = sessionWithParticipants
          .addParticipant(participantId, 'MEMBER', at)
          .onJoin(participantId, at);
      }

      const breakoutRoomId = SessionTestFactories.createRoomId();
      sessionWithParticipants = sessionWithParticipants
        .assignParticipantToRoom(participantIds[0], breakoutRoomId, at)
        .assignParticipantToRoom(participantIds[1], breakoutRoomId, at)
        .assignParticipantToRoom(participantIds[2], breakoutRoomId, at);

      // Make room ready first time
      const sessionWithReadyRoom = sessionWithParticipants.makeRoomReady(breakoutRoomId, at);

      // Act - Make room ready again
      const result = sessionWithReadyRoom.makeRoomReady(breakoutRoomId, at);

      // Assert
      expect(result).toBe(sessionWithReadyRoom); // Same instance for idempotent operation
      
      const room = result.getRoom(breakoutRoomId);
      expect(room?.isReady).toBe(true);
    });

    it('should be idempotent when making already closed room closed', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantIds = [
        SessionTestFactories.createParticipantId(),
        SessionTestFactories.createParticipantId(),
      ];
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add participants and create full breakout room
      let sessionWithParticipants = session;
      for (const participantId of participantIds) {
        sessionWithParticipants = sessionWithParticipants
          .addParticipant(participantId, 'MEMBER', at)
          .onJoin(participantId, at);
      }

      const breakoutRoomId = SessionTestFactories.createRoomId();
      sessionWithParticipants = sessionWithParticipants
        .assignParticipantToRoom(participantIds[0], breakoutRoomId, at)
        .assignParticipantToRoom(participantIds[1], breakoutRoomId, at);

      // Make room closed first time
      const sessionWithClosedRoom = sessionWithParticipants.makeRoomClosed(breakoutRoomId, at);

      // Act - Make room closed again
      const result = sessionWithClosedRoom.makeRoomClosed(breakoutRoomId, at);

      // Assert
      expect(result).toBe(sessionWithClosedRoom); // Same instance for idempotent operation
      
      const room = result.getRoom(breakoutRoomId);
      expect(room?.isClosed).toBe(true);
    });
  });

  describe('no-op operation idempotency', () => {
    it('should be idempotent when leaving non-existent participant', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const nonExistentParticipantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result1 = session.onLeave(nonExistentParticipantId, at);
      const result2 = result1.onLeave(nonExistentParticipantId, at);

      // Assert
      expect(result1).toBe(session); // Same instance for no-op
      expect(result2).toBe(result1); // Same instance for no-op
    });

    it('should be idempotent when returning participants already in main room', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add participant and keep in main room
      const sessionWithParticipant = session
        .addParticipant(participantId, 'MEMBER', at)
        .onJoin(participantId, at);

      // Act
      const result1 = sessionWithParticipant.returnAllParticipantsToMain(at);
      const result2 = result1.returnAllParticipantsToMain(at);

      // Assert
      expect(result1).toBe(sessionWithParticipant); // Same instance for no-op
      expect(result2).toBe(result1); // Same instance for no-op
    });

    it('should be idempotent when releasing seats with no reservations', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result1 = session.releaseAllReservedSeats(at);
      const result2 = result1.releaseAllReservedSeats(at);

      // Assert
      expect(result1).toBe(session); // Same instance for no-op
      expect(result2).toBe(result1); // Same instance for no-op
    });
  });

  describe('equality and identity checks', () => {
    it('should maintain object identity for idempotent operations', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act - Multiple idempotent operations
      const result1 = session.startSession(at);
      const result2 = result1.resumeSession(at);
      const result3 = result2.releaseAllReservedSeats(at);
      const result4 = result3.returnAllParticipantsToMain(at);

      // Assert - All should be the same instance
      expect(result1).toBe(session);
      expect(result2).toBe(result1);
      expect(result3).toBe(result2);
      expect(result4).toBe(result3);
    });

    it('should create new instances for non-idempotent operations', () => {
      // Arrange
      const session = SessionTestFactories.createSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act - Non-idempotent operations
      const result1 = session.addParticipant(participantId, 'MEMBER', at);
      const result2 = result1.startSession(at);
      const result3 = result2.setHost(participantId, at);

      // Assert - All should be different instances
      expect(result1).not.toBe(session);
      expect(result2).not.toBe(result1);
      expect(result3).not.toBe(result2);
    });

    it('should preserve data integrity across idempotent operations', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add participant and set as host
      const sessionWithHost = session
        .addParticipant(participantId, 'MEMBER', at)
        .setHost(participantId, at);

      // Act - Multiple idempotent operations
      const result = sessionWithHost
        .startSession(at)
        .setHost(participantId, at)
        .addParticipant(participantId, 'MEMBER', at);

      // Assert - Data should be preserved
      expect(result.toPrimitives().hostId).toBe(participantId.toPrimitives());
      expect(result.participantCount).toBe(1);
      expect(result.currentState).toBe('RUNNING');
      
      const participant = result.getParticipant(participantId);
      expect(participant?.role).toBe('HOST');
    });
  });

  describe('complex idempotency scenarios', () => {
    it('should handle mixed idempotent and non-idempotent operations correctly', () => {
      // Arrange
      const session = SessionTestFactories.createSession();
      const participantId1 = SessionTestFactories.createParticipantId();
      const participantId2 = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act - Mix of operations
      const result1 = session.addParticipant(participantId1, 'MEMBER', at); // Non-idempotent
      const result2 = result1.addParticipant(participantId1, 'MEMBER', at); // Idempotent
      const result3 = result2.addParticipant(participantId2, 'MEMBER', at); // Non-idempotent
      const result4 = result3.addParticipant(participantId2, 'HOST', at); // Idempotent
      const result5 = result4.startSession(at); // Non-idempotent
      const result6 = result5.startSession(at); // Idempotent

      // Assert
      expect(result1).not.toBe(session);
      expect(result2).toBe(result1); // Idempotent
      expect(result3).not.toBe(result2);
      expect(result4).toBe(result3); // Idempotent
      expect(result5).not.toBe(result4);
      expect(result6).toBe(result5); // Idempotent

      expect(result6.participantCount).toBe(2);
      expect(result6.currentState).toBe('RUNNING');
    });

    it('should maintain idempotency across state transitions', () => {
      // Arrange
      const session = SessionTestFactories.createSession();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act - State transition cycle with idempotent operations
      const running1 = session.startSession(at);
      const running2 = running1.startSession(at); // Idempotent
      const paused1 = running2.pauseSession(at);
      const paused2 = paused1.pauseSession(at); // Idempotent
      const running3 = paused2.resumeSession(at);
      const running4 = running3.resumeSession(at); // Idempotent

      // Assert
      expect(running2).toBe(running1); // Idempotent
      expect(paused2).toBe(paused1); // Idempotent
      expect(running4).toBe(running3); // Idempotent

      expect(running4.currentState).toBe('RUNNING');
    });
  });
});
