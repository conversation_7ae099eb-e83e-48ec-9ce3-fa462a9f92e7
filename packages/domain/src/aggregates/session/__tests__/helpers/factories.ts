import { Uuid } from '../../../../primitives/uuid/uuid.primitive';
import { Instant } from '../../../../primitives/instant/instant.primitive';
import { NonNegativeInt } from '../../../../primitives/non-negative-int/non-negative-int.primitive';
import { SessionId } from '../../../../sessions/value-objects/session-id/session-id.vo';
import { SessionConfig } from '../../../../sessions/value-objects/session-config/session-config.vo';
import { SessionConfigPrimitives } from '../../../../sessions/types/session-config';
import { ParticipantId } from '../../../../participants/value-objects/participant-id/participant-id.vo';
import { ParticipantRole } from '../../../../participants/types/participant-role.enum';
import { RoomId } from '../../../../rooms/value-objects/room-id/room-id.vo';
import { RoomConfig } from '../../../../rooms/value-objects/room-config/room-config.vo';
import { RoomConfigPrimitives } from '../../../../rooms/types/room-config';
import { Room } from '../../../room/room.aggregate';
import { RoundInstance } from '../../../../entities/round-instance/round-instance.entity';
import { RoundSpec, RoundSpecPrimitives } from '../../../../rounds/value-objects/round-spec/round-spec.vo';
import { ParticipantPresence } from '../../../../entities/participant-presence/presence.entity';
import { Session, SessionPrimitives, SessionState } from '../../session.aggregate';
import { AllocationStrategy } from '../../../../policies/autopilot/types/allocation-strategy.enum';
import { LateJoinAllocationMode } from '../../../../policies/late-join/types/late-join-allocation-mode.enum';
import { SessionMode } from '../../../../sessions/types/session-mode.enum';

export class SessionTestFactories {
  static readonly BASE_TIMESTAMP = 1640995200000; // 2022-01-01 00:00:00 UTC
  static readonly LATER_TIMESTAMP = 1640995260000; // 1 minute later
  static readonly EVEN_LATER_TIMESTAMP = 1640995320000; // 2 minutes later

  static createSessionId(id?: string): SessionId {
    return SessionId.fromPrimitives(id || Uuid.generate());
  }

  static createParticipantId(id?: string): ParticipantId {
    return ParticipantId.fromPrimitives(id || Uuid.generate());
  }

  static createRoomId(id?: string): RoomId {
    return RoomId.fromPrimitives(id || Uuid.generate());
  }

  static createUuid(id?: string): Uuid {
    return Uuid.fromPrimitives(id || Uuid.generate());
  }

  static createInstant(timestamp?: number): Instant {
    return Instant.fromPrimitives(timestamp || this.BASE_TIMESTAMP);
  }

  static createDefaultRoomConfig(overrides?: Partial<RoomConfigPrimitives>): RoomConfig {
    const defaults: RoomConfigPrimitives = {
      minSeats: 2,
      maxSeats: 8,
      avoidSingleton: true,
      disconnectionPolicy: { holdSeatForMs: 30000 },
    };
    return RoomConfig.fromPrimitives({ ...defaults, ...overrides });
  }

  static createDefaultSessionConfig(overrides?: Partial<SessionConfigPrimitives>): SessionConfig {
    const defaults: SessionConfigPrimitives = {
      scheduledStartAt: this.BASE_TIMESTAMP,
      estimatedDurationMs: 3600000, // 1 hour
      defaultRoomConfig: {
        minSeats: 2,
        maxSeats: 8,
        avoidSingleton: true,
        disconnectionPolicy: { holdSeatForMs: 30000 },
      },
      maxParticipants: 50,
      defaultRoundDurationMs: 900000, // 15 minutes
      autopilotPolicy: {
        allocationStategy: AllocationStrategy.ROUND_ROBIN,
      },
      lobbyAdmissionPolicy: {
        allowEarlyJoinMs: 300000, // 5 minutes
        requireHostPresent: true,
      },
      disconnectionPolicy: {
        holdSeatForMs: 120000, // 2 minutes
      },
      lateJoinPolicy: {
        allocationMode: LateJoinAllocationMode.BEST_FIT,
      },
      mode: SessionMode.HOSTED,
    };
    return SessionConfig.fromPrimitives({ ...defaults, ...overrides });
  }

  static createRoundSpec(overrides?: Partial<RoundSpecPrimitives>): RoundSpec {
    const defaults: RoundSpecPrimitives = {
      kind: 'MAIN_TOPIC',
      durationMs: 900000, // 15 minutes
      autoCloseGraceMs: 30000, // 30 seconds
    };
    return RoundSpec.fromPrimitives({ ...defaults, ...overrides });
  }

  static createRoundInstance(
    roundId?: Uuid,
    index?: number,
    spec?: RoundSpec
  ): RoundInstance {
    return RoundInstance.create(
      roundId || this.createUuid(),
      NonNegativeInt.fromPrimitives(index || 0),
      spec || this.createRoundSpec()
    );
  }

  static createParticipantPresence(
    participantId?: ParticipantId,
    role?: ParticipantRole
  ): ParticipantPresence {
    return ParticipantPresence.create(
      participantId || this.createParticipantId(),
      role
    );
  }

  static createRoom(
    roomId?: RoomId,
    config?: RoomConfig,
    createdAt?: Instant
  ): Room {
    return Room.create(
      roomId || this.createRoomId(),
      config || this.createDefaultRoomConfig(),
      createdAt || this.createInstant()
    );
  }

  static createSessionPrimitives(overrides?: Partial<SessionPrimitives>): SessionPrimitives {
    const sessionId = Uuid.generate();
    const mainRoomId = Uuid.generate();
    const createdByUserId = Uuid.generate();
    
    const defaults: SessionPrimitives = {
      sessionId,
      config: this.createDefaultSessionConfig().toPrimitives(),
      state: 'SCHEDULED',
      createdByUserId,
      hostId: undefined,
      mainRoomId,
      currentRoundIndex: -1,
      rounds: [],
      participants: [],
      rooms: [
        this.createRoom(
          RoomId.fromPrimitives(mainRoomId),
          RoomConfig.fromPrimitives({
            minSeats: 2,
            maxSeats: 50, // matches maxParticipants
            avoidSingleton: true,
            disconnectionPolicy: { holdSeatForMs: 30000 },
          })
        ).toPrimitives(),
      ],
      createdAt: this.BASE_TIMESTAMP,
    };
    
    return { ...defaults, ...overrides };
  }

  static createSession(overrides?: Partial<SessionPrimitives>): Session {
    return Session.fromPrimitives(this.createSessionPrimitives(overrides));
  }

  static createScheduledSession(overrides?: Partial<SessionPrimitives>): Session {
    return this.createSession({ state: 'SCHEDULED', ...overrides });
  }

  static createRunningSession(overrides?: Partial<SessionPrimitives>): Session {
    return this.createSession({ state: 'RUNNING', ...overrides });
  }

  static createPausedSession(overrides?: Partial<SessionPrimitives>): Session {
    return this.createSession({ state: 'PAUSED', ...overrides });
  }

  static createCompletedSession(overrides?: Partial<SessionPrimitives>): Session {
    return this.createSession({ state: 'COMPLETED', ...overrides });
  }

  static createCanceledSession(overrides?: Partial<SessionPrimitives>): Session {
    return this.createSession({ state: 'CANCELED', ...overrides });
  }

  static createSessionWithParticipants(
    participantCount: number,
    overrides?: Partial<SessionPrimitives>
  ): Session {
    const participants = Array.from({ length: participantCount }, () =>
      this.createParticipantPresence().toPrimitives()
    );
    
    return this.createSession({
      participants,
      ...overrides,
    });
  }

  static createSessionWithRounds(
    roundCount: number,
    overrides?: Partial<SessionPrimitives>
  ): Session {
    const rounds = Array.from({ length: roundCount }, (_, index) =>
      this.createRoundInstance(undefined, index).toPrimitives()
    );
    
    return this.createSession({
      rounds,
      ...overrides,
    });
  }

  static createSessionWithHost(
    hostId?: string,
    overrides?: Partial<SessionPrimitives>
  ): Session {
    const actualHostId = hostId || Uuid.generate();
    const hostParticipant = this.createParticipantPresence(
      ParticipantId.fromPrimitives(actualHostId),
      ParticipantRole.HOST
    );
    
    return this.createSession({
      hostId: actualHostId,
      participants: [hostParticipant.toPrimitives()],
      ...overrides,
    });
  }

  static createSessionAtCapacity(overrides?: Partial<SessionPrimitives>): Session {
    const config = this.createDefaultSessionConfig({ maxParticipants: 3 });
    const participants = Array.from({ length: 3 }, () =>
      this.createParticipantPresence().toPrimitives()
    );
    
    return this.createSession({
      config: config.toPrimitives(),
      participants,
      ...overrides,
    });
  }

  // Helper to create invalid session primitives for error testing
  static createInvalidSessionPrimitives(invalidField: string, invalidValue: any): any {
    const valid = this.createSessionPrimitives();
    return { ...valid, [invalidField]: invalidValue };
  }
}
