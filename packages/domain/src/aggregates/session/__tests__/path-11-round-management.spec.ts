import { Session } from '../session.aggregate';
import { SessionStateError, SessionInvariantError } from '../session.errors';
import { SessionTestFactories } from './helpers/factories';

describe('Path 11: Round management', () => {
  describe('addRound', () => {
    describe('throws SessionStateError if not RUNNING', () => {
      const invalidStates = ['SCHEDULED', 'PAUSED', 'COMPLETED', 'CANCELED'];

      it.each(invalidStates)('should throw when session is %s', (state) => {
        // Arrange
        const session = SessionTestFactories.createSession({ state: state as any });
        const roundSpec = SessionTestFactories.createRoundSpec();
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act & Assert
        expect(() => session.addRound(roundSpec, at)).toThrow(SessionStateError);
        expect(() => session.addRound(roundSpec, at)).toThrow('Cannot add round unless session is RUNNING');
      });
    });

    describe('successful round addition', () => {
      it('should add round to session', () => {
        // Arrange
        const session = SessionTestFactories.createRunningSession();
        const roundSpec = SessionTestFactories.createRoundSpec();
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act
        const result = session.addRound(roundSpec, at);

        // Assert
        expect(result).not.toBe(session);
        expect(result.allRounds).toHaveLength(1);
        expect(result.allRounds[0].spec).toBe(roundSpec);
      });

      it('should add multiple rounds', () => {
        // Arrange
        const session = SessionTestFactories.createRunningSession();
        const roundSpec1 = SessionTestFactories.createRoundSpec({ kind: 'ICE_BREAKER' });
        const roundSpec2 = SessionTestFactories.createRoundSpec({ kind: 'MAIN_TOPIC' });
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act
        const result1 = session.addRound(roundSpec1, at);
        const result2 = result1.addRound(roundSpec2, at);

        // Assert
        expect(result2.allRounds).toHaveLength(2);
        expect(result2.allRounds[0].spec.kind).toBe('ICE_BREAKER');
        expect(result2.allRounds[1].spec.kind).toBe('MAIN_TOPIC');
      });
    });
  });

  describe('startRound', () => {
    describe('throws SessionStateError if not RUNNING', () => {
      const invalidStates = ['SCHEDULED', 'PAUSED', 'COMPLETED', 'CANCELED'];

      it.each(invalidStates)('should throw when session is %s', (state) => {
        // Arrange
        const session = SessionTestFactories.createSession({ state: state as any });
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act & Assert
        expect(() => session.startRound(0, at)).toThrow(SessionStateError);
        expect(() => session.startRound(0, at)).toThrow('Cannot start round unless session is RUNNING');
      });
    });

    describe('throws SessionInvariantError for invalid round index', () => {
      it('should throw when round index is negative', () => {
        // Arrange
        const session = SessionTestFactories.createRunningSession();
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act & Assert
        expect(() => session.startRound(-1, at)).toThrow(SessionInvariantError);
        expect(() => session.startRound(-1, at)).toThrow('Round index out of bounds');
      });

      it('should throw when round index exceeds available rounds', () => {
        // Arrange
        const session = SessionTestFactories.createRunningSession();
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act & Assert
        expect(() => session.startRound(0, at)).toThrow(SessionInvariantError);
        expect(() => session.startRound(0, at)).toThrow('Round index out of bounds');
      });
    });

    describe('successful round start', () => {
      it('should start the specified round', () => {
        // Arrange
        const session = SessionTestFactories.createRunningSession();
        const roundSpec = SessionTestFactories.createRoundSpec();
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);
        
        const sessionWithRound = session.addRound(roundSpec, at);

        // Act
        const result = sessionWithRound.startRound(0, at);

        // Assert
        expect(result).not.toBe(sessionWithRound);
        expect(result.toPrimitives().currentRoundIndex).toBe(0);
        expect(result.allRounds[0].isRunning).toBe(true);
      });

      it('should update currentRoundIndex', () => {
        // Arrange
        const session = SessionTestFactories.createRunningSession();
        const roundSpec1 = SessionTestFactories.createRoundSpec();
        const roundSpec2 = SessionTestFactories.createRoundSpec();
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);
        
        let sessionWithRounds = session.addRound(roundSpec1, at);
        sessionWithRounds = sessionWithRounds.addRound(roundSpec2, at);

        // Act
        const result = sessionWithRounds.startRound(1, at);

        // Assert
        expect(result.toPrimitives().currentRoundIndex).toBe(1);
        expect(result.allRounds[1].isRunning).toBe(true);
      });
    });
  });

  describe('endCurrentRound', () => {
    describe('throws SessionStateError if not RUNNING', () => {
      const invalidStates = ['SCHEDULED', 'PAUSED', 'COMPLETED', 'CANCELED'];

      it.each(invalidStates)('should throw when session is %s', (state) => {
        // Arrange
        const session = SessionTestFactories.createSession({ state: state as any });
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act & Assert
        expect(() => session.endCurrentRound(at)).toThrow(SessionStateError);
        expect(() => session.endCurrentRound(at)).toThrow('Cannot end round unless session is RUNNING');
      });
    });

    describe('throws SessionInvariantError when no current round', () => {
      it('should throw when currentRoundIndex is -1', () => {
        // Arrange
        const session = SessionTestFactories.createRunningSession();
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act & Assert
        expect(() => session.endCurrentRound(at)).toThrow(SessionInvariantError);
        expect(() => session.endCurrentRound(at)).toThrow('No current round to end');
      });
    });

    describe('successful round end', () => {
      it('should end the current round', () => {
        // Arrange
        const session = SessionTestFactories.createRunningSession();
        const roundSpec = SessionTestFactories.createRoundSpec();
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);
        
        let sessionWithRound = session.addRound(roundSpec, at);
        sessionWithRound = sessionWithRound.startRound(0, at);

        // Act
        const result = sessionWithRound.endCurrentRound(at);

        // Assert
        expect(result).not.toBe(sessionWithRound);
        expect(result.allRounds[0].isCompleted).toBe(true);
      });
    });
  });

  describe('closeCurrentRound', () => {
    describe('throws SessionStateError if not RUNNING', () => {
      const invalidStates = ['SCHEDULED', 'PAUSED', 'COMPLETED', 'CANCELED'];

      it.each(invalidStates)('should throw when session is %s', (state) => {
        // Arrange
        const session = SessionTestFactories.createSession({ state: state as any });
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act & Assert
        expect(() => session.closeCurrentRound(at)).toThrow(SessionStateError);
        expect(() => session.closeCurrentRound(at)).toThrow('Cannot close round unless session is RUNNING');
      });
    });

    describe('throws SessionInvariantError when no current round', () => {
      it('should throw when currentRoundIndex is -1', () => {
        // Arrange
        const session = SessionTestFactories.createRunningSession();
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act & Assert
        expect(() => session.closeCurrentRound(at)).toThrow(SessionInvariantError);
        expect(() => session.closeCurrentRound(at)).toThrow('No current round to close');
      });
    });

    describe('successful round close', () => {
      it('should close the current round', () => {
        // Arrange
        const session = SessionTestFactories.createRunningSession();
        const roundSpec = SessionTestFactories.createRoundSpec();
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);
        
        let sessionWithRound = session.addRound(roundSpec, at);
        sessionWithRound = sessionWithRound.startRound(0, at);
        sessionWithRound = sessionWithRound.endCurrentRound(at);

        // Act
        const result = sessionWithRound.closeCurrentRound(at);

        // Assert
        expect(result).not.toBe(sessionWithRound);
        expect(result.allRounds[0].isClosed).toBe(true);
      });
    });
  });

  describe('immutability verification', () => {
    it('should return new instances for all round operations', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const roundSpec = SessionTestFactories.createRoundSpec();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result1 = session.addRound(roundSpec, at);
      const result2 = result1.startRound(0, at);
      const result3 = result2.endCurrentRound(at);
      const result4 = result3.closeCurrentRound(at);

      // Assert
      expect(result1).not.toBe(session);
      expect(result2).not.toBe(result1);
      expect(result3).not.toBe(result2);
      expect(result4).not.toBe(result3);
      
      // Original should be unchanged
      expect(session.allRounds).toHaveLength(0);
      expect(session.toPrimitives().currentRoundIndex).toBe(-1);
    });

    it('should preserve all other properties during round operations', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const roundSpec = SessionTestFactories.createRoundSpec();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.addRound(roundSpec, at);

      // Assert
      expect(result.sessionId).toBe(session.sessionId);
      expect(result.config).toBe(session.config);
      expect(result.currentState).toBe(session.currentState);
      expect(result.createdByUserId).toBe(session.createdByUserId);
      expect(result.mainRoomId).toBe(session.mainRoomId);
      expect(result.createdAt).toBe(session.createdAt);
      expect(result.participantCount).toBe(session.participantCount);
      expect(result.toPrimitives().hostId).toBe(session.toPrimitives().hostId);
    });
  });
});
