import { Session, SessionState } from '../session.aggregate';
import { PersistenceMappingError } from '../../../errors/persistence-mapping-error';
import { SessionTestFactories } from './helpers/factories';

describe('Path 1: Mapping & roundtrip', () => {
  describe('fromPrimitives: happy path builds a valid Session', () => {
    const validStates: SessionState[] = ['SCHEDULED', 'RUNNING', 'PAUSED', 'COMPLETED', 'CANCELED'];

    it.each(validStates)('should create session with state %s', (state) => {
      // Arrange
      const primitives = SessionTestFactories.createSessionPrimitives({ state });

      // Act
      const session = Session.fromPrimitives(primitives);

      // Assert
      expect(session).toBeInstanceOf(Session);
      expect(session.currentState).toBe(state);
      expect(session.sessionId.toPrimitives()).toBe(primitives.sessionId);
      expect(session.config.toPrimitives()).toEqual(primitives.config);
      expect(session.createdByUserId.toPrimitives()).toBe(primitives.createdByUserId);
      expect(session.mainRoomId.toPrimitives()).toBe(primitives.mainRoomId);
      expect(session.createdAt.toPrimitives()).toBe(primitives.createdAt);
      expect(session.allRounds).toHaveLength(primitives.rounds.length);
      expect(session.allParticipants).toHaveLength(primitives.participants.length);
      expect(session.allRooms).toHaveLength(primitives.rooms.length);
    });

    it('should handle session with host', () => {
      // Arrange
      const hostId = SessionTestFactories.createUuid().toPrimitives();
      const primitives = SessionTestFactories.createSessionPrimitives({ hostId });

      // Act
      const session = Session.fromPrimitives(primitives);

      // Assert
      expect(session.hasHost).toBe(true);
    });

    it('should handle session without host', () => {
      // Arrange
      const primitives = SessionTestFactories.createSessionPrimitives({ hostId: undefined });

      // Act
      const session = Session.fromPrimitives(primitives);

      // Assert
      expect(session.hasHost).toBe(false);
    });

    it('should handle session with participants and rounds', () => {
      // Arrange
      const participants = [
        SessionTestFactories.createParticipantPresence().toPrimitives(),
        SessionTestFactories.createParticipantPresence().toPrimitives(),
      ];
      const rounds = [
        SessionTestFactories.createRoundInstance().toPrimitives(),
        SessionTestFactories.createRoundInstance().toPrimitives(),
      ];
      const primitives = SessionTestFactories.createSessionPrimitives({
        participants,
        rounds,
        currentRoundIndex: 0,
      });

      // Act
      const session = Session.fromPrimitives(primitives);

      // Assert
      expect(session.allParticipants).toHaveLength(2);
      expect(session.allRounds).toHaveLength(2);
      expect(session.participantCount).toBe(2);
      expect(session.currentRound).toBeDefined();
    });
  });

  describe('fromPrimitives: throws PersistenceMappingError on null/undefined dto', () => {
    it('should throw on null dto', () => {
      // Act & Assert
      expect(() => Session.fromPrimitives(null as any)).toThrow(PersistenceMappingError);
      expect(() => Session.fromPrimitives(null as any)).toThrow('Session DTO is null or undefined');
    });

    it('should throw on undefined dto', () => {
      // Act & Assert
      expect(() => Session.fromPrimitives(undefined as any)).toThrow(PersistenceMappingError);
      expect(() => Session.fromPrimitives(undefined as any)).toThrow('Session DTO is null or undefined');
    });
  });

  describe('fromPrimitives: throws PersistenceMappingError on invalid state value', () => {
    const invalidStates = [
      'INVALID',
      'invalid',
      'scheduled',
      'running',
      '',
      null,
      undefined,
      123,
      {},
      [],
    ];

    it.each(invalidStates)('should throw on invalid state: %s', (invalidState) => {
      // Arrange
      const primitives = SessionTestFactories.createInvalidSessionPrimitives('state', invalidState);

      // Act & Assert
      expect(() => Session.fromPrimitives(primitives)).toThrow(PersistenceMappingError);
      expect(() => Session.fromPrimitives(primitives)).toThrow('Invalid session state');
    });
  });

  describe('toPrimitives/fromPrimitives roundtrip preserves identity', () => {
    it('should preserve all data in roundtrip for minimal session', () => {
      // Arrange
      const original = SessionTestFactories.createSessionPrimitives();

      // Act
      const session = Session.fromPrimitives(original);
      const result = session.toPrimitives();

      // Assert
      expect(result.sessionId).toBe(original.sessionId);
      expect(result.config).toEqual(original.config);
      expect(result.state).toBe(original.state);
      expect(result.createdByUserId).toBe(original.createdByUserId);
      expect(result.hostId).toBe(original.hostId);
      expect(result.mainRoomId).toBe(original.mainRoomId);
      expect(result.currentRoundIndex).toBe(original.currentRoundIndex);
      expect(result.rounds).toEqual(original.rounds);
      expect(result.participants).toEqual(original.participants);
      expect(result.rooms).toEqual(original.rooms);
      expect(result.createdAt).toBe(original.createdAt);
    });

    it('should preserve all data in roundtrip for complex session', () => {
      // Arrange
      const hostId = SessionTestFactories.createUuid().toPrimitives();
      const participants = [
        SessionTestFactories.createParticipantPresence().toPrimitives(),
        SessionTestFactories.createParticipantPresence().toPrimitives(),
        SessionTestFactories.createParticipantPresence().toPrimitives(),
      ];
      const rounds = [
        SessionTestFactories.createRoundInstance().toPrimitives(),
        SessionTestFactories.createRoundInstance().toPrimitives(),
      ];
      const rooms = [
        SessionTestFactories.createRoom().toPrimitives(),
        SessionTestFactories.createRoom().toPrimitives(),
      ];
      
      const original = SessionTestFactories.createSessionPrimitives({
        state: 'RUNNING',
        hostId,
        participants,
        rounds,
        rooms,
        currentRoundIndex: 1,
      });

      // Act
      const session = Session.fromPrimitives(original);
      const result = session.toPrimitives();

      // Assert - Check identity preservation of IDs and counts
      expect(result.sessionId).toBe(original.sessionId);
      expect(result.hostId).toBe(original.hostId);
      expect(result.mainRoomId).toBe(original.mainRoomId);
      expect(result.currentRoundIndex).toBe(original.currentRoundIndex);
      expect(result.participants).toHaveLength(original.participants.length);
      expect(result.rounds).toHaveLength(original.rounds.length);
      expect(result.rooms).toHaveLength(original.rooms.length);
      
      // Check participant IDs are preserved
      result.participants.forEach((participant, index) => {
        expect(participant.participantId).toBe(original.participants[index].participantId);
      });
      
      // Check round IDs are preserved
      result.rounds.forEach((round, index) => {
        expect(round.roundId).toBe(original.rounds[index].roundId);
        expect(round.index).toBe(original.rounds[index].index);
      });
      
      // Check room IDs are preserved
      result.rooms.forEach((room, index) => {
        expect(room.roomId).toBe(original.rooms[index].roomId);
      });
    });

    it('should preserve currentRoundIndex correctly', () => {
      // Arrange
      const testCases = [-1, 0, 1, 5];
      
      testCases.forEach((currentRoundIndex) => {
        const original = SessionTestFactories.createSessionPrimitives({ currentRoundIndex });

        // Act
        const session = Session.fromPrimitives(original);
        const result = session.toPrimitives();

        // Assert
        expect(result.currentRoundIndex).toBe(currentRoundIndex);
      });
    });

    it('should handle edge case with empty collections', () => {
      // Arrange
      const original = SessionTestFactories.createSessionPrimitives({
        participants: [],
        rounds: [],
        rooms: [SessionTestFactories.createRoom().toPrimitives()], // At least main room
      });

      // Act
      const session = Session.fromPrimitives(original);
      const result = session.toPrimitives();

      // Assert
      expect(result.participants).toEqual([]);
      expect(result.rounds).toEqual([]);
      expect(result.rooms).toHaveLength(1);
      expect(session.participantCount).toBe(0);
    });
  });

  describe('error propagation from nested entities', () => {
    it('should wrap nested mapping errors in PersistenceMappingError', () => {
      // Arrange - Create primitives with invalid nested data
      const primitives = SessionTestFactories.createSessionPrimitives();
      primitives.config = null as any; // This should cause SessionConfig.fromPrimitives to fail

      // Act & Assert
      expect(() => Session.fromPrimitives(primitives)).toThrow(PersistenceMappingError);
      expect(() => Session.fromPrimitives(primitives)).toThrow('Failed to map session from primitives');
    });
  });
});
