import { Session } from '../session.aggregate';
import { SessionCapacityError, SessionInvariantError } from '../session.errors';
import { SessionTestFactories } from './helpers/factories';

describe('Path 15: Capacity constraints across flows', () => {
  describe('maxParticipants constraint in addParticipant', () => {
    it('should enforce maxParticipants limit when adding participants', () => {
      // Arrange
      const config = SessionTestFactories.createDefaultSessionConfig({ maxParticipants: 2 });
      const session = SessionTestFactories.createSession({ config });
      const participantId1 = SessionTestFactories.createParticipantId();
      const participantId2 = SessionTestFactories.createParticipantId();
      const participantId3 = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act - Add participants up to limit
      let sessionWithParticipants = session
        .addParticipant(participantId1, 'MEMBER', at)
        .addParticipant(participantId2, 'MEMBER', at);

      // Assert - Should succeed up to limit
      expect(sessionWithParticipants.participantCount).toBe(2);

      // Act & Assert - Should throw when exceeding limit
      expect(() => sessionWithParticipants.addParticipant(participantId3, 'MEMBER', at))
        .toThrow(SessionCapacityError);
      expect(() => sessionWithParticipants.addParticipant(participantId3, 'MEMBER', at))
        .toThrow('Session has reached maximum participant capacity');
    });

    it('should include context in capacity error', () => {
      // Arrange
      const config = SessionTestFactories.createDefaultSessionConfig({ maxParticipants: 1 });
      const session = SessionTestFactories.createSession({ config });
      const participantId1 = SessionTestFactories.createParticipantId();
      const participantId2 = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      const sessionWithParticipant = session.addParticipant(participantId1, 'MEMBER', at);

      // Act & Assert
      try {
        sessionWithParticipant.addParticipant(participantId2, 'MEMBER', at);
        fail('Expected SessionCapacityError to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(SessionCapacityError);
        expect((error as any).context).toMatchObject({
          currentParticipantCount: 1,
          maxParticipants: 1,
          attemptedParticipantId: participantId2.toPrimitives(),
        });
      }
    });
  });

  describe('maxParticipants constraint in onJoin', () => {
    it('should enforce maxParticipants limit when participants join', () => {
      // Arrange
      const config = SessionTestFactories.createDefaultSessionConfig({ maxParticipants: 2 });
      const session = SessionTestFactories.createRunningSession({ config });
      const participantId1 = SessionTestFactories.createParticipantId();
      const participantId2 = SessionTestFactories.createParticipantId();
      const participantId3 = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add participants (but don't join yet)
      let sessionWithParticipants = session
        .addParticipant(participantId1, 'MEMBER', at)
        .addParticipant(participantId2, 'MEMBER', at)
        .addParticipant(participantId3, 'MEMBER', at); // This should work since we're not at capacity yet

      // Join participants up to capacity
      sessionWithParticipants = sessionWithParticipants
        .onJoin(participantId1, at)
        .onJoin(participantId2, at);

      // Act & Assert - Should throw when exceeding capacity on join
      expect(() => sessionWithParticipants.onJoin(participantId3, at))
        .toThrow(SessionCapacityError);
      expect(() => sessionWithParticipants.onJoin(participantId3, at))
        .toThrow('Session has reached maximum participant capacity');
    });
  });

  describe('room capacity constraints', () => {
    it('should respect maxSeats when seating participants', () => {
      // Arrange
      const roomConfig = SessionTestFactories.createDefaultRoomConfig({ maxSeats: 2 });
      const config = SessionTestFactories.createDefaultSessionConfig({ 
        maxParticipants: 4,
        defaultRoomConfig: roomConfig 
      });
      const session = SessionTestFactories.createRunningSession({ config });
      const participantIds = [
        SessionTestFactories.createParticipantId(),
        SessionTestFactories.createParticipantId(),
        SessionTestFactories.createParticipantId(),
      ];
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add and join participants
      let sessionWithParticipants = session;
      for (const participantId of participantIds) {
        sessionWithParticipants = sessionWithParticipants
          .addParticipant(participantId, 'MEMBER', at)
          .onJoin(participantId, at);
      }

      // Act & Assert - Should create new rooms when main room is full
      expect(sessionWithParticipants.allRooms.length).toBeGreaterThan(1);
      
      // Verify no room exceeds maxSeats
      for (const room of sessionWithParticipants.allRooms) {
        expect(room.occupiedSeats.length).toBeLessThanOrEqual(room.config.maxSeats);
      }
    });

    it('should respect minSeats when creating rooms', () => {
      // Arrange
      const roomConfig = SessionTestFactories.createDefaultRoomConfig({ 
        maxSeats: 4,
        minSeats: 2 
      });
      const config = SessionTestFactories.createDefaultSessionConfig({ 
        maxParticipants: 6,
        defaultRoomConfig: roomConfig 
      });
      const session = SessionTestFactories.createRunningSession({ config });
      const participantIds = Array.from({ length: 5 }, () => SessionTestFactories.createParticipantId());
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add and join participants
      let sessionWithParticipants = session;
      for (const participantId of participantIds) {
        sessionWithParticipants = sessionWithParticipants
          .addParticipant(participantId, 'MEMBER', at)
          .onJoin(participantId, at);
      }

      // Act & Assert - Verify rooms respect minSeats
      const breakoutRooms = sessionWithParticipants.allRooms.filter(
        room => room.roomId.toPrimitives() !== sessionWithParticipants.mainRoomId.toPrimitives()
      );
      
      for (const room of breakoutRooms) {
        expect(room.occupiedSeats.length).toBeGreaterThanOrEqual(room.config.minSeats);
      }
    });
  });

  describe('capacity constraints during room operations', () => {
    it('should enforce capacity when making room ready', () => {
      // Arrange
      const roomConfig = SessionTestFactories.createDefaultRoomConfig({ 
        maxSeats: 4,
        minSeats: 3 
      });
      const config = SessionTestFactories.createDefaultSessionConfig({ 
        maxParticipants: 10,
        defaultRoomConfig: roomConfig 
      });
      const session = SessionTestFactories.createRunningSession({ config });
      const participantIds = [
        SessionTestFactories.createParticipantId(),
        SessionTestFactories.createParticipantId(),
      ];
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add and join participants (less than minSeats)
      let sessionWithParticipants = session;
      for (const participantId of participantIds) {
        sessionWithParticipants = sessionWithParticipants
          .addParticipant(participantId, 'MEMBER', at)
          .onJoin(participantId, at);
      }

      // Create a breakout room with insufficient participants
      const breakoutRoomId = SessionTestFactories.createRoomId();
      sessionWithParticipants = sessionWithParticipants
        .assignParticipantToRoom(participantIds[0], breakoutRoomId, at)
        .assignParticipantToRoom(participantIds[1], breakoutRoomId, at);

      // Act & Assert - Should throw when trying to make room ready with insufficient participants
      expect(() => sessionWithParticipants.makeRoomReady(breakoutRoomId, at))
        .toThrow(); // Could be SessionInvariantError or RoomStateError depending on implementation
    });

    it('should enforce capacity when making room closed', () => {
      // Arrange
      const roomConfig = SessionTestFactories.createDefaultRoomConfig({ maxSeats: 2 });
      const config = SessionTestFactories.createDefaultSessionConfig({ 
        maxParticipants: 10,
        defaultRoomConfig: roomConfig 
      });
      const session = SessionTestFactories.createRunningSession({ config });
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add and join participant
      let sessionWithParticipant = session
        .addParticipant(participantId, 'MEMBER', at)
        .onJoin(participantId, at);

      // Create a breakout room with available seats
      const breakoutRoomId = SessionTestFactories.createRoomId();
      sessionWithParticipant = sessionWithParticipant
        .assignParticipantToRoom(participantId, breakoutRoomId, at);

      // Act & Assert - Should throw when trying to close room with available seats
      expect(() => sessionWithParticipant.makeRoomClosed(breakoutRoomId, at))
        .toThrow(SessionInvariantError);
      expect(() => sessionWithParticipant.makeRoomClosed(breakoutRoomId, at))
        .toThrow('Cannot close room with available seats');
    });
  });

  describe('capacity constraints during breakout operations', () => {
    it('should respect capacity constraints when rotating breakouts', () => {
      // Arrange
      const roomConfig = SessionTestFactories.createDefaultRoomConfig({ 
        maxSeats: 2,
        minSeats: 2 
      });
      const config = SessionTestFactories.createDefaultSessionConfig({ 
        maxParticipants: 6,
        defaultRoomConfig: roomConfig 
      });
      const session = SessionTestFactories.createRunningSession({ config });
      const participantIds = Array.from({ length: 4 }, () => SessionTestFactories.createParticipantId());
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add and join participants
      let sessionWithParticipants = session;
      for (const participantId of participantIds) {
        sessionWithParticipants = sessionWithParticipants
          .addParticipant(participantId, 'MEMBER', at)
          .onJoin(participantId, at);
      }

      // Act
      const result = sessionWithParticipants.rotateBreakouts(at);

      // Assert - All rooms should respect capacity constraints
      const breakoutRooms = result.allRooms.filter(
        room => room.roomId.toPrimitives() !== result.mainRoomId.toPrimitives()
      );
      
      for (const room of breakoutRooms) {
        expect(room.occupiedSeats.length).toBeLessThanOrEqual(room.config.maxSeats);
        expect(room.occupiedSeats.length).toBeGreaterThanOrEqual(room.config.minSeats);
      }
    });
  });

  describe('edge cases with capacity constraints', () => {
    it('should handle capacity of 1 correctly', () => {
      // Arrange
      const config = SessionTestFactories.createDefaultSessionConfig({ maxParticipants: 1 });
      const session = SessionTestFactories.createRunningSession({ config });
      const participantId1 = SessionTestFactories.createParticipantId();
      const participantId2 = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act - Add first participant
      const sessionWithParticipant = session.addParticipant(participantId1, 'MEMBER', at);
      expect(sessionWithParticipant.participantCount).toBe(1);

      // Act & Assert - Should throw when adding second participant
      expect(() => sessionWithParticipant.addParticipant(participantId2, 'MEMBER', at))
        .toThrow(SessionCapacityError);
    });

    it('should handle very large capacity correctly', () => {
      // Arrange
      const config = SessionTestFactories.createDefaultSessionConfig({ maxParticipants: 1000 });
      const session = SessionTestFactories.createSession({ config });
      const participantIds = Array.from({ length: 10 }, () => SessionTestFactories.createParticipantId());
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act - Add multiple participants
      let sessionWithParticipants = session;
      for (const participantId of participantIds) {
        sessionWithParticipants = sessionWithParticipants.addParticipant(participantId, 'MEMBER', at);
      }

      // Assert
      expect(sessionWithParticipants.participantCount).toBe(10);
    });
  });

  describe('immutability verification', () => {
    it('should return new instances when capacity constraints are enforced', () => {
      // Arrange
      const config = SessionTestFactories.createDefaultSessionConfig({ maxParticipants: 2 });
      const session = SessionTestFactories.createSession({ config });
      const participantId1 = SessionTestFactories.createParticipantId();
      const participantId2 = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result1 = session.addParticipant(participantId1, 'MEMBER', at);
      const result2 = result1.addParticipant(participantId2, 'MEMBER', at);

      // Assert
      expect(result1).not.toBe(session);
      expect(result2).not.toBe(result1);
      
      // Original should be unchanged
      expect(session.participantCount).toBe(0);
      expect(result1.participantCount).toBe(1);
      expect(result2.participantCount).toBe(2);
    });
  });
});
