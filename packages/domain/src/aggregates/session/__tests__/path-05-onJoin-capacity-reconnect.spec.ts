import { Session } from '../session.aggregate';
import { SessionStateError, SessionCapacityError } from '../session.errors';
import { ParticipantRole } from '../../../participants/types/participant-role.enum';
import { SessionTestFactories } from './helpers/factories';

describe('Path 5: onJoin – capacity, reconnect, no round vs active round', () => {
  describe('throws SessionStateError if session not RUNNING', () => {
    const invalidStates = ['SCHEDULED', 'PAUSED', 'COMPLETED', 'CANCELED'];

    it.each(invalidStates)('should throw when session is %s', (state) => {
      // Arrange
      const session = SessionTestFactories.createSession({ state: state as any });
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert
      expect(() => session.onJoin(participantId, at)).toThrow(SessionStateError);
      expect(() => session.onJoin(participantId, at)).toThrow('Cannot join participant unless session is RUNNING');
    });
  });

  describe('throws SessionCapacityError when at capacity', () => {
    it('should throw when participantCount equals maxParticipants', () => {
      // Arrange
      const session = SessionTestFactories.createSessionAtCapacity({ state: 'RUNNING' });
      const newParticipantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert
      expect(() => session.onJoin(newParticipantId, at)).toThrow(SessionCapacityError);
      expect(() => session.onJoin(newParticipantId, at)).toThrow('Session capacity exceeded');
    });

    it('should include context in capacity error', () => {
      // Arrange
      const session = SessionTestFactories.createSessionAtCapacity({ state: 'RUNNING' });
      const newParticipantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert
      try {
        session.onJoin(newParticipantId, at);
        fail('Expected SessionCapacityError to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(SessionCapacityError);
        expect((error as any).context).toMatchObject({
          currentCount: 3,
          maxParticipants: 3,
          sessionId: session.sessionId.toPrimitives(),
          participantId: newParticipantId.toPrimitives(),
        });
      }
    });

    it('should allow join when under capacity', () => {
      // Arrange
      const config = SessionTestFactories.createDefaultSessionConfig({ maxParticipants: 5 });
      const participants = Array.from({ length: 3 }, () =>
        SessionTestFactories.createParticipantPresence().toPrimitives()
      );
      const session = SessionTestFactories.createRunningSession({
        config: config.toPrimitives(),
        participants,
      });
      const newParticipantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.onJoin(newParticipantId, at);

      // Assert
      expect(result.participantCount).toBe(4);
      expect(result.allParticipants.some(p => 
        p.participantId.toPrimitives() === newParticipantId.toPrimitives()
      )).toBe(true);
    });
  });

  describe('new participant: adds presence then seats', () => {
    describe('when currentRoundIndex < 0 → assigned to main room', () => {
      it('should add new participant and seat in main room', () => {
        // Arrange
        const session = SessionTestFactories.createRunningSession({
          currentRoundIndex: -1, // No active round
        });
        const participantId = SessionTestFactories.createParticipantId();
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act
        const result = session.onJoin(participantId, at);

        // Assert
        expect(result).not.toBe(session);
        expect(result.participantCount).toBe(1);
        
        // Check participant was added
        const participant = result.findParticipantById(participantId);
        expect(participant).toBeDefined();
        expect(participant!.currentTags).toContain('role-MEMBER');
        
        // Check participant was seated in main room
        expect(participant!.toPrimitives().currentRoomId).toBe(result.mainRoomId.toPrimitives());
        
        // Check main room has the participant
        const mainRoom = result.findRoomById(result.mainRoomId);
        expect(mainRoom!.occupiedSeats).toHaveLength(1);
        expect(mainRoom!.occupiedSeats[0].currentParticipantId).toBe(participantId.toPrimitives());
      });

      it('should use default MEMBER role when not specified', () => {
        // Arrange
        const session = SessionTestFactories.createRunningSession({
          currentRoundIndex: -1,
        });
        const participantId = SessionTestFactories.createParticipantId();
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act
        const result = session.onJoin(participantId, at);

        // Assert
        const participant = result.findParticipantById(participantId);
        expect(participant!.currentTags).toContain('role-MEMBER');
      });

      it('should use specified role', () => {
        // Arrange
        const session = SessionTestFactories.createRunningSession({
          currentRoundIndex: -1,
        });
        const participantId = SessionTestFactories.createParticipantId();
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act
        const result = session.onJoin(participantId, at, ParticipantRole.HOST);

        // Assert
        const participant = result.findParticipantById(participantId);
        expect(participant!.currentTags).toContain('role-HOST');
      });
    });

    describe('when active round exists → selected breakout room with space or new room created', () => {
      it('should seat in existing breakout room with space', () => {
        // Arrange
        const rounds = [SessionTestFactories.createRoundInstance().toPrimitives()];
        const breakoutRoom = SessionTestFactories.createRoom().toPrimitives();
        const rooms = [
          SessionTestFactories.createRoom(
            SessionTestFactories.createRoomId('main-room-id')
          ).toPrimitives(),
          breakoutRoom,
        ];
        
        const session = SessionTestFactories.createRunningSession({
          rounds,
          rooms,
          currentRoundIndex: 0,
          mainRoomId: 'main-room-id',
        });
        
        const participantId = SessionTestFactories.createParticipantId();
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act
        const result = session.onJoin(participantId, at);

        // Assert
        expect(result.participantCount).toBe(1);
        
        const participant = result.findParticipantById(participantId);
        expect(participant).toBeDefined();
        
        // Should be seated in the breakout room (not main)
        expect(participant!.toPrimitives().currentRoomId).toBe(breakoutRoom.roomId);
        
        const targetRoom = result.findRoomById(
          SessionTestFactories.createRoomId(breakoutRoom.roomId)
        );
        expect(targetRoom!.occupiedSeats).toHaveLength(1);
        expect(targetRoom!.occupiedSeats[0].currentParticipantId).toBe(participantId.toPrimitives());
      });

      it('should create new room when no existing room has space', () => {
        // Arrange
        const rounds = [SessionTestFactories.createRoundInstance().toPrimitives()];
        
        // Create a full breakout room
        const fullBreakoutRoom = SessionTestFactories.createRoom(
          undefined,
          SessionTestFactories.createDefaultRoomConfig({ maxSeats: 1 })
        );
        const fullBreakoutRoomWithParticipant = fullBreakoutRoom.assignParticipantToSeat(
          SessionTestFactories.createParticipantId(),
          SessionTestFactories.createInstant()
        );
        
        const rooms = [
          SessionTestFactories.createRoom(
            SessionTestFactories.createRoomId('main-room-id')
          ).toPrimitives(),
          fullBreakoutRoomWithParticipant.toPrimitives(),
        ];
        
        const session = SessionTestFactories.createRunningSession({
          rounds,
          rooms,
          currentRoundIndex: 0,
          mainRoomId: 'main-room-id',
        });
        
        const participantId = SessionTestFactories.createParticipantId();
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act
        const result = session.onJoin(participantId, at);

        // Assert
        expect(result.participantCount).toBe(1);
        expect(result.allRooms).toHaveLength(3); // main + full breakout + new breakout
        
        const participant = result.findParticipantById(participantId);
        expect(participant).toBeDefined();
        
        // Should be seated in a breakout room (not main)
        expect(participant!.toPrimitives().currentRoomId).not.toBe('main-room-id');
        expect(participant!.toPrimitives().currentRoomId).not.toBe(fullBreakoutRoomWithParticipant.roomId.toPrimitives());
        
        // The new room should have the participant
        const newRoomId = participant!.toPrimitives().currentRoomId!;
        const newRoom = result.findRoomById(SessionTestFactories.createRoomId(newRoomId));
        expect(newRoom!.occupiedSeats).toHaveLength(1);
        expect(newRoom!.occupiedSeats[0].currentParticipantId).toBe(participantId.toPrimitives());
      });

      it('should select room with smallest (occupied + reserved) count', () => {
        // Arrange
        const rounds = [SessionTestFactories.createRoundInstance().toPrimitives()];
        
        // Create breakout rooms with different occupancy
        const room1 = SessionTestFactories.createRoom(); // Empty
        const room2WithParticipant = SessionTestFactories.createRoom().assignParticipantToSeat(
          SessionTestFactories.createParticipantId(),
          SessionTestFactories.createInstant()
        ); // Has 1 participant
        
        const rooms = [
          SessionTestFactories.createRoom(
            SessionTestFactories.createRoomId('main-room-id')
          ).toPrimitives(),
          room2WithParticipant.toPrimitives(), // This has 1 occupied
          room1.toPrimitives(), // This is empty - should be selected
        ];
        
        const session = SessionTestFactories.createRunningSession({
          rounds,
          rooms,
          currentRoundIndex: 0,
          mainRoomId: 'main-room-id',
        });
        
        const participantId = SessionTestFactories.createParticipantId();
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act
        const result = session.onJoin(participantId, at);

        // Assert
        const participant = result.findParticipantById(participantId);
        expect(participant!.toPrimitives().currentRoomId).toBe(room1.roomId.toPrimitives());
      });
    });
  });

  describe('reconnect path', () => {
    describe('if no round active → assigned to main room (no restore)', () => {
      it('should assign existing participant to main room when no round active', () => {
        // Arrange
        const participantId = SessionTestFactories.createParticipantId();
        const existingParticipant = SessionTestFactories.createParticipantPresence(participantId);

        const session = SessionTestFactories.createRunningSession({
          participants: [existingParticipant.toPrimitives()],
          currentRoundIndex: -1, // No active round
        });

        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act
        const result = session.onJoin(participantId, at);

        // Assert
        expect(result.participantCount).toBe(1); // Same count, no new participant added

        const participant = result.findParticipantById(participantId);
        expect(participant!.toPrimitives().currentRoomId).toBe(result.mainRoomId.toPrimitives());

        const mainRoom = result.findRoomById(result.mainRoomId);
        expect(mainRoom!.occupiedSeats).toHaveLength(1);
        expect(mainRoom!.occupiedSeats[0].currentParticipantId).toBe(participantId.toPrimitives());
      });
    });

    describe('if round active and seat was reserved → restoreReservedSeat is used', () => {
      it('should restore participant to reserved seat', () => {
        // Arrange
        const participantId = SessionTestFactories.createParticipantId();
        const existingParticipant = SessionTestFactories.createParticipantPresence(participantId);

        const rounds = [SessionTestFactories.createRoundInstance().toPrimitives()];

        // Create a breakout room with a reserved seat for the participant
        const breakoutRoom = SessionTestFactories.createRoom();
        const roomWithReservedSeat = breakoutRoom.reserveSeatForReconnect(
          participantId,
          SessionTestFactories.createInstant()
        );

        const rooms = [
          SessionTestFactories.createRoom(
            SessionTestFactories.createRoomId('main-room-id')
          ).toPrimitives(),
          roomWithReservedSeat.toPrimitives(),
        ];

        const session = SessionTestFactories.createRunningSession({
          participants: [existingParticipant.toPrimitives()],
          rounds,
          rooms,
          currentRoundIndex: 0,
          mainRoomId: 'main-room-id',
        });

        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act
        const result = session.onJoin(participantId, at);

        // Assert
        expect(result.participantCount).toBe(1); // Same count

        const participant = result.findParticipantById(participantId);
        expect(participant!.toPrimitives().currentRoomId).toBe(roomWithReservedSeat.roomId.toPrimitives());

        // Check that seat is now occupied (not reserved)
        const restoredRoom = result.findRoomById(roomWithReservedSeat.roomId);
        expect(restoredRoom!.occupiedSeats).toHaveLength(1);
        expect(restoredRoom!.reservedSeats).toHaveLength(0);
        expect(restoredRoom!.occupiedSeats[0].currentParticipantId).toBe(participantId.toPrimitives());
      });
    });

    describe('if restore fails → behaves like new join (add + seat)', () => {
      it('should fall back to new join behavior when restore fails', () => {
        // Arrange
        const participantId = SessionTestFactories.createParticipantId();
        const existingParticipant = SessionTestFactories.createParticipantPresence(participantId);

        const rounds = [SessionTestFactories.createRoundInstance().toPrimitives()];
        const breakoutRoom = SessionTestFactories.createRoom();

        const rooms = [
          SessionTestFactories.createRoom(
            SessionTestFactories.createRoomId('main-room-id')
          ).toPrimitives(),
          breakoutRoom.toPrimitives(), // No reserved seat for participant
        ];

        const session = SessionTestFactories.createRunningSession({
          participants: [existingParticipant.toPrimitives()],
          rounds,
          rooms,
          currentRoundIndex: 0,
          mainRoomId: 'main-room-id',
        });

        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act
        const result = session.onJoin(participantId, at);

        // Assert
        expect(result.participantCount).toBe(1); // Same count

        const participant = result.findParticipantById(participantId);
        expect(participant!.toPrimitives().currentRoomId).toBe(breakoutRoom.roomId.toPrimitives());

        // Should be seated in the breakout room
        const targetRoom = result.findRoomById(breakoutRoom.roomId);
        expect(targetRoom!.occupiedSeats).toHaveLength(1);
        expect(targetRoom!.occupiedSeats[0].currentParticipantId).toBe(participantId.toPrimitives());
      });
    });

    describe('idempotency: joining already-present participant without reservation', () => {
      it('should not create duplicate presences', () => {
        // Arrange
        const participantId = SessionTestFactories.createParticipantId();
        const existingParticipant = SessionTestFactories.createParticipantPresence(participantId);

        const session = SessionTestFactories.createRunningSession({
          participants: [existingParticipant.toPrimitives()],
          currentRoundIndex: -1,
        });

        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act
        const result = session.onJoin(participantId, at);

        // Assert
        expect(result.participantCount).toBe(1); // No duplicate
        expect(result.allParticipants).toHaveLength(1);

        const participantIds = result.allParticipants.map(p => p.participantId.toPrimitives());
        expect(participantIds.filter(id => id === participantId.toPrimitives())).toHaveLength(1);
      });
    });
  });

  describe('immutability verification', () => {
    it('should return new instance when joining', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.onJoin(participantId, at);

      // Assert
      expect(result).not.toBe(session);
      expect(session.participantCount).toBe(0); // Original unchanged
      expect(result.participantCount).toBe(1);
    });

    it('should preserve all other properties', () => {
      // Arrange
      const rounds = [SessionTestFactories.createRoundInstance().toPrimitives()];
      const session = SessionTestFactories.createRunningSession({
        rounds,
        currentRoundIndex: 0,
      });
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.onJoin(participantId, at);

      // Assert
      expect(result.sessionId).toBe(session.sessionId);
      expect(result.config).toBe(session.config);
      expect(result.currentState).toBe(session.currentState);
      expect(result.createdByUserId).toBe(session.createdByUserId);
      expect(result.mainRoomId).toBe(session.mainRoomId);
      expect(result.createdAt).toBe(session.createdAt);
      expect(result.allRounds).toHaveLength(session.allRounds.length);
      expect(result.toPrimitives().currentRoundIndex).toBe(session.toPrimitives().currentRoundIndex);
      expect(result.toPrimitives().hostId).toBe(session.toPrimitives().hostId);
    });
  });
});
