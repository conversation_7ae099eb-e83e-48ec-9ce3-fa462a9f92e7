import { Session } from '../session.aggregate';
import { SessionStateError, SessionInvariantError } from '../session.errors';
import { SessionTestFactories } from './helpers/factories';

describe('Path 10: Room readiness & closure', () => {
  describe('makeRoomReady', () => {
    describe('throws SessionStateError if not RUNNING', () => {
      const invalidStates = ['SCHEDULED', 'PAUSED', 'COMPLETED', 'CANCELED'];

      it.each(invalidStates)('should throw when session is %s', (state) => {
        // Arrange
        const roomId = SessionTestFactories.createRoomId();
        const room = SessionTestFactories.createRoom(roomId);
        const session = SessionTestFactories.createSession({
          state: state as any,
          rooms: [room.toPrimitives()],
        });
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act & Assert
        expect(() => session.makeRoomReady(roomId, at)).toThrow(SessionStateError);
        expect(() => session.makeRoomReady(roomId, at)).toThrow('Cannot make room ready unless session is RUNNING');
      });
    });

    describe('throws SessionInvariantError if room not found', () => {
      it('should throw when room does not exist', () => {
        // Arrange
        const session = SessionTestFactories.createRunningSession();
        const nonExistentRoomId = SessionTestFactories.createRoomId();
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act & Assert
        expect(() => session.makeRoomReady(nonExistentRoomId, at)).toThrow(SessionInvariantError);
        expect(() => session.makeRoomReady(nonExistentRoomId, at)).toThrow('Room not found');
      });

      it('should include context in room not found error', () => {
        // Arrange
        const session = SessionTestFactories.createRunningSession();
        const nonExistentRoomId = SessionTestFactories.createRoomId();
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act & Assert
        try {
          session.makeRoomReady(nonExistentRoomId, at);
          fail('Expected SessionInvariantError to be thrown');
        } catch (error) {
          expect(error).toBeInstanceOf(SessionInvariantError);
          expect((error as any).context).toMatchObject({
            roomId: nonExistentRoomId.toPrimitives(),
            sessionId: session.sessionId.toPrimitives(),
          });
        }
      });
    });

    describe('throws SessionInvariantError when room does not meet minSeats policy', () => {
      it('should throw when occupied + reserved < minSeats', () => {
        // Arrange
        const roomConfig = SessionTestFactories.createDefaultRoomConfig({ minSeats: 3, maxSeats: 8 });
        const roomId = SessionTestFactories.createRoomId();
        const room = SessionTestFactories.createRoom(roomId, roomConfig);
        
        // Add only 2 participants (less than minSeats of 3)
        const participant1Id = SessionTestFactories.createParticipantId();
        const participant2Id = SessionTestFactories.createParticipantId();
        
        let roomWithParticipants = room.assignParticipantToSeat(participant1Id, SessionTestFactories.createInstant());
        roomWithParticipants = roomWithParticipants.assignParticipantToSeat(participant2Id, SessionTestFactories.createInstant());
        
        const session = SessionTestFactories.createRunningSession({
          rooms: [roomWithParticipants.toPrimitives()],
        });
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act & Assert
        expect(() => session.makeRoomReady(roomId, at)).toThrow(SessionInvariantError);
        expect(() => session.makeRoomReady(roomId, at)).toThrow('Room is not in FILLING state');
      });

      it('should include context in minSeats error', () => {
        // Arrange
        const roomConfig = SessionTestFactories.createDefaultRoomConfig({ minSeats: 3, maxSeats: 8 });
        const roomId = SessionTestFactories.createRoomId();
        const room = SessionTestFactories.createRoom(roomId, roomConfig);
        
        const session = SessionTestFactories.createRunningSession({
          rooms: [room.toPrimitives()],
        });
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act & Assert
        try {
          session.makeRoomReady(roomId, at);
          fail('Expected SessionInvariantError to be thrown');
        } catch (error) {
          expect(error).toBeInstanceOf(SessionInvariantError);
          expect((error as any).context).toMatchObject({
            roomId: roomId.toPrimitives(),
            occupiedSeats: 0,
            reservedSeats: 0,
            sessionId: session.sessionId.toPrimitives(),
          });
        }
      });

      it('should allow when occupied + reserved >= minSeats', () => {
        // Arrange
        const roomConfig = SessionTestFactories.createDefaultRoomConfig({ minSeats: 2, maxSeats: 8 });
        const roomId = SessionTestFactories.createRoomId();
        const room = SessionTestFactories.createRoom(roomId, roomConfig);
        
        // Add 1 occupied + 1 reserved = 2 (meets minSeats)
        const participant1Id = SessionTestFactories.createParticipantId();
        const participant2Id = SessionTestFactories.createParticipantId();
        
        let roomWithParticipants = room.assignParticipantToSeat(participant1Id, SessionTestFactories.createInstant());
        roomWithParticipants = roomWithParticipants.reserveSeatForReconnect(participant2Id, SessionTestFactories.createInstant());
        
        const session = SessionTestFactories.createRunningSession({
          rooms: [roomWithParticipants.toPrimitives()],
        });
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act
        const result = session.makeRoomReady(roomId, at);

        // Assert
        expect(result).not.toBe(session);
        const resultRoom = result.findRoomById(roomId);
        expect(resultRoom!.isReady).toBe(true);
      });
    });

    describe('idempotent if already READY', () => {
      it('should return same instance when room already READY', () => {
        // Arrange
        const roomConfig = SessionTestFactories.createDefaultRoomConfig({ minSeats: 2, maxSeats: 8 });
        const roomId = SessionTestFactories.createRoomId();
        const room = SessionTestFactories.createRoom(roomId, roomConfig);
        
        // Make room meet minSeats and set to READY
        const participant1Id = SessionTestFactories.createParticipantId();
        const participant2Id = SessionTestFactories.createParticipantId();
        
        let roomWithParticipants = room.assignParticipantToSeat(participant1Id, SessionTestFactories.createInstant());
        roomWithParticipants = roomWithParticipants.assignParticipantToSeat(participant2Id, SessionTestFactories.createInstant());
        const readyRoom = roomWithParticipants.makeReady();
        
        const session = SessionTestFactories.createRunningSession({
          rooms: [readyRoom.toPrimitives()],
        });
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act
        const result = session.makeRoomReady(roomId, at);

        // Assert
        expect(result).toBe(session); // Same instance (idempotent)
      });
    });

    describe('successful room ready transition', () => {
      it('should transition room from FILLING to READY', () => {
        // Arrange
        const roomConfig = SessionTestFactories.createDefaultRoomConfig({ minSeats: 2, maxSeats: 8 });
        const roomId = SessionTestFactories.createRoomId();
        const room = SessionTestFactories.createRoom(roomId, roomConfig);
        
        const participant1Id = SessionTestFactories.createParticipantId();
        const participant2Id = SessionTestFactories.createParticipantId();
        
        let roomWithParticipants = room.assignParticipantToSeat(participant1Id, SessionTestFactories.createInstant());
        roomWithParticipants = roomWithParticipants.assignParticipantToSeat(participant2Id, SessionTestFactories.createInstant());
        
        const session = SessionTestFactories.createRunningSession({
          rooms: [roomWithParticipants.toPrimitives()],
        });
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act
        const result = session.makeRoomReady(roomId, at);

        // Assert
        expect(result).not.toBe(session);
        
        const originalRoom = session.findRoomById(roomId);
        const resultRoom = result.findRoomById(roomId);
        
        expect(originalRoom!.isFilling).toBe(true);
        expect(originalRoom!.isReady).toBe(false);
        expect(resultRoom!.isFilling).toBe(false);
        expect(resultRoom!.isReady).toBe(true);
      });
    });
  });

  describe('makeRoomClosed', () => {
    describe('throws SessionStateError if not RUNNING', () => {
      const invalidStates = ['SCHEDULED', 'PAUSED', 'COMPLETED', 'CANCELED'];

      it.each(invalidStates)('should throw when session is %s', (state) => {
        // Arrange
        const roomId = SessionTestFactories.createRoomId();
        const room = SessionTestFactories.createRoom(roomId);
        const session = SessionTestFactories.createSession({
          state: state as any,
          rooms: [room.toPrimitives()],
        });
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act & Assert
        expect(() => session.makeRoomClosed(roomId, at)).toThrow(SessionStateError);
        expect(() => session.makeRoomClosed(roomId, at)).toThrow('Cannot make room closed unless session is RUNNING');
      });
    });

    describe('throws SessionInvariantError if room not found', () => {
      it('should throw when room does not exist', () => {
        // Arrange
        const session = SessionTestFactories.createRunningSession();
        const nonExistentRoomId = SessionTestFactories.createRoomId();
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act & Assert
        expect(() => session.makeRoomClosed(nonExistentRoomId, at)).toThrow(SessionInvariantError);
        expect(() => session.makeRoomClosed(nonExistentRoomId, at)).toThrow('Room not found');
      });
    });

    describe('throws SessionInvariantError when room has available seats', () => {
      it('should throw when room is not full', () => {
        // Arrange
        const roomConfig = SessionTestFactories.createDefaultRoomConfig({ minSeats: 2, maxSeats: 4 });
        const roomId = SessionTestFactories.createRoomId();
        const room = SessionTestFactories.createRoom(roomId, roomConfig);
        
        // Add only 2 participants (room not full)
        const participant1Id = SessionTestFactories.createParticipantId();
        const participant2Id = SessionTestFactories.createParticipantId();
        
        let roomWithParticipants = room.assignParticipantToSeat(participant1Id, SessionTestFactories.createInstant());
        roomWithParticipants = roomWithParticipants.assignParticipantToSeat(participant2Id, SessionTestFactories.createInstant());
        
        const session = SessionTestFactories.createRunningSession({
          rooms: [roomWithParticipants.toPrimitives()],
        });
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act & Assert
        expect(() => session.makeRoomClosed(roomId, at)).toThrow(SessionInvariantError);
        expect(() => session.makeRoomClosed(roomId, at)).toThrow('Room cannot be closed because it is not full');
      });

      it('should include context in not full error', () => {
        // Arrange
        const roomConfig = SessionTestFactories.createDefaultRoomConfig({ minSeats: 2, maxSeats: 4 });
        const roomId = SessionTestFactories.createRoomId();
        const room = SessionTestFactories.createRoom(roomId, roomConfig);
        
        const session = SessionTestFactories.createRunningSession({
          rooms: [room.toPrimitives()],
        });
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act & Assert
        try {
          session.makeRoomClosed(roomId, at);
          fail('Expected SessionInvariantError to be thrown');
        } catch (error) {
          expect(error).toBeInstanceOf(SessionInvariantError);
          expect((error as any).context).toMatchObject({
            roomId: roomId.toPrimitives(),
            availableSeats: 4, // All seats available
            occupiedSeats: 0,
            reservedSeats: 0,
            sessionId: session.sessionId.toPrimitives(),
          });
        }
      });

      it('should allow when room is full (no available seats)', () => {
        // Arrange
        const roomConfig = SessionTestFactories.createDefaultRoomConfig({ minSeats: 2, maxSeats: 2 });
        const roomId = SessionTestFactories.createRoomId();
        const room = SessionTestFactories.createRoom(roomId, roomConfig);
        
        // Fill the room completely
        const participant1Id = SessionTestFactories.createParticipantId();
        const participant2Id = SessionTestFactories.createParticipantId();
        
        let fullRoom = room.assignParticipantToSeat(participant1Id, SessionTestFactories.createInstant());
        fullRoom = fullRoom.assignParticipantToSeat(participant2Id, SessionTestFactories.createInstant());
        
        const session = SessionTestFactories.createRunningSession({
          rooms: [fullRoom.toPrimitives()],
        });
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act
        const result = session.makeRoomClosed(roomId, at);

        // Assert
        expect(result).not.toBe(session);
        const resultRoom = result.findRoomById(roomId);
        expect(resultRoom!.isClosed).toBe(true);
      });
    });

    describe('idempotent if already CLOSED', () => {
      it('should return same instance when room already CLOSED', () => {
        // Arrange
        const roomConfig = SessionTestFactories.createDefaultRoomConfig({ minSeats: 2, maxSeats: 2 });
        const roomId = SessionTestFactories.createRoomId();
        const room = SessionTestFactories.createRoom(roomId, roomConfig);
        
        // Fill and close the room
        const participant1Id = SessionTestFactories.createParticipantId();
        const participant2Id = SessionTestFactories.createParticipantId();
        
        let fullRoom = room.assignParticipantToSeat(participant1Id, SessionTestFactories.createInstant());
        fullRoom = fullRoom.assignParticipantToSeat(participant2Id, SessionTestFactories.createInstant());
        const closedRoom = fullRoom.makeClosed();
        
        const session = SessionTestFactories.createRunningSession({
          rooms: [closedRoom.toPrimitives()],
        });
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act
        const result = session.makeRoomClosed(roomId, at);

        // Assert
        expect(result).toBe(session); // Same instance (idempotent)
      });
    });

    describe('successful room closed transition', () => {
      it('should transition room to CLOSED', () => {
        // Arrange
        const roomConfig = SessionTestFactories.createDefaultRoomConfig({ minSeats: 2, maxSeats: 2 });
        const roomId = SessionTestFactories.createRoomId();
        const room = SessionTestFactories.createRoom(roomId, roomConfig);
        
        const participant1Id = SessionTestFactories.createParticipantId();
        const participant2Id = SessionTestFactories.createParticipantId();
        
        let fullRoom = room.assignParticipantToSeat(participant1Id, SessionTestFactories.createInstant());
        fullRoom = fullRoom.assignParticipantToSeat(participant2Id, SessionTestFactories.createInstant());
        
        const session = SessionTestFactories.createRunningSession({
          rooms: [fullRoom.toPrimitives()],
        });
        const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

        // Act
        const result = session.makeRoomClosed(roomId, at);

        // Assert
        expect(result).not.toBe(session);
        
        const originalRoom = session.findRoomById(roomId);
        const resultRoom = result.findRoomById(roomId);
        
        expect(originalRoom!.isClosed).toBe(false);
        expect(resultRoom!.isClosed).toBe(true);
      });
    });
  });

  describe('immutability verification', () => {
    it('should return new instance when making room ready', () => {
      // Arrange
      const roomConfig = SessionTestFactories.createDefaultRoomConfig({ minSeats: 2, maxSeats: 8 });
      const roomId = SessionTestFactories.createRoomId();
      const room = SessionTestFactories.createRoom(roomId, roomConfig);
      
      const participant1Id = SessionTestFactories.createParticipantId();
      const participant2Id = SessionTestFactories.createParticipantId();
      
      let roomWithParticipants = room.assignParticipantToSeat(participant1Id, SessionTestFactories.createInstant());
      roomWithParticipants = roomWithParticipants.assignParticipantToSeat(participant2Id, SessionTestFactories.createInstant());
      
      const session = SessionTestFactories.createRunningSession({
        rooms: [roomWithParticipants.toPrimitives()],
      });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.makeRoomReady(roomId, at);

      // Assert
      expect(result).not.toBe(session);
      
      const originalRoom = session.findRoomById(roomId);
      const resultRoom = result.findRoomById(roomId);
      
      expect(originalRoom!.isReady).toBe(false);
      expect(resultRoom!.isReady).toBe(true);
    });

    it('should preserve all other properties when making room ready', () => {
      // Arrange
      const roomConfig = SessionTestFactories.createDefaultRoomConfig({ minSeats: 2, maxSeats: 8 });
      const roomId = SessionTestFactories.createRoomId();
      const room = SessionTestFactories.createRoom(roomId, roomConfig);
      
      const participant1Id = SessionTestFactories.createParticipantId();
      const participant2Id = SessionTestFactories.createParticipantId();
      
      let roomWithParticipants = room.assignParticipantToSeat(participant1Id, SessionTestFactories.createInstant());
      roomWithParticipants = roomWithParticipants.assignParticipantToSeat(participant2Id, SessionTestFactories.createInstant());
      
      const rounds = [SessionTestFactories.createRoundInstance().toPrimitives()];
      
      const session = SessionTestFactories.createRunningSession({
        rooms: [roomWithParticipants.toPrimitives()],
        rounds,
        currentRoundIndex: 0,
      });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.makeRoomReady(roomId, at);

      // Assert
      expect(result.sessionId).toBe(session.sessionId);
      expect(result.config).toBe(session.config);
      expect(result.currentState).toBe(session.currentState);
      expect(result.createdByUserId).toBe(session.createdByUserId);
      expect(result.mainRoomId).toBe(session.mainRoomId);
      expect(result.createdAt).toBe(session.createdAt);
      expect(result.allRounds).toHaveLength(session.allRounds.length);
      expect(result.participantCount).toBe(session.participantCount);
      expect(result.toPrimitives().currentRoundIndex).toBe(session.toPrimitives().currentRoundIndex);
      expect(result.toPrimitives().hostId).toBe(session.toPrimitives().hostId);
    });
  });
});
