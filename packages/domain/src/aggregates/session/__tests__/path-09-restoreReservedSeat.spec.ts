import { Session } from '../session.aggregate';
import { SessionStateError, SessionInvariantError } from '../session.errors';
import { SessionTestFactories } from './helpers/factories';

describe('Path 9: restoreReservedSeat – correctness & guards', () => {
  describe('throws SessionStateError if not RUNNING', () => {
    const invalidStates = ['SCHEDULED', 'PAUSED', 'COMPLETED', 'CANCELED'];

    it.each(invalidStates)('should throw when session is %s', (state) => {
      // Arrange
      const participantId = SessionTestFactories.createParticipantId();
      const session = SessionTestFactories.createSession({
        state: state as any,
        participants: [SessionTestFactories.createParticipantPresence(participantId).toPrimitives()],
      });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert
      expect(() => session.restoreReservedSeat(participantId, at)).toThrow(SessionStateError);
      expect(() => session.restoreReservedSeat(participantId, at)).toThrow('Cannot restore seat unless session is RUNNING');
    });
  });

  describe('throws SessionInvariantError if no reserved seat exists', () => {
    it('should throw when participant has no reserved seat', () => {
      // Arrange
      const participantId = SessionTestFactories.createParticipantId();
      const participant = SessionTestFactories.createParticipantPresence(participantId);
      
      const roomId = SessionTestFactories.createRoomId();
      const room = SessionTestFactories.createRoom(roomId); // No reserved seat
      
      const session = SessionTestFactories.createRunningSession({
        participants: [participant.toPrimitives()],
        rooms: [room.toPrimitives()],
      });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert
      expect(() => session.restoreReservedSeat(participantId, at)).toThrow(SessionInvariantError);
      expect(() => session.restoreReservedSeat(participantId, at)).toThrow('Participant current room not found');
    });

    it('should include context in no reservation error', () => {
      // Arrange
      const participantId = SessionTestFactories.createParticipantId();
      const participant = SessionTestFactories.createParticipantPresence(participantId);
      
      const session = SessionTestFactories.createRunningSession({
        participants: [participant.toPrimitives()],
      });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert
      try {
        session.restoreReservedSeat(participantId, at);
        fail('Expected SessionInvariantError to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(SessionInvariantError);
        expect((error as any).context).toMatchObject({
          participantId: participantId.toPrimitives(),
          sessionId: session.sessionId.toPrimitives(),
        });
      }
    });

    it('should throw when participant has occupied seat but no reserved seat', () => {
      // Arrange
      const participantId = SessionTestFactories.createParticipantId();
      const participant = SessionTestFactories.createParticipantPresence(participantId);
      
      const roomId = SessionTestFactories.createRoomId();
      const room = SessionTestFactories.createRoom(roomId).assignParticipantToSeat(
        participantId,
        SessionTestFactories.createInstant()
      ); // Occupied but not reserved
      
      const session = SessionTestFactories.createRunningSession({
        participants: [participant.toPrimitives()],
        rooms: [room.toPrimitives()],
      });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert
      expect(() => session.restoreReservedSeat(participantId, at)).toThrow(SessionInvariantError);
      expect(() => session.restoreReservedSeat(participantId, at)).toThrow('Participant current room not found');
    });
  });

  describe('when reservation exists: room restoreSeatAfterReconnect and presence enterRoom', () => {
    it('should restore participant to reserved seat', () => {
      // Arrange
      const participantId = SessionTestFactories.createParticipantId();
      const participant = SessionTestFactories.createParticipantPresence(participantId);
      
      const roomId = SessionTestFactories.createRoomId();
      const room = SessionTestFactories.createRoom(roomId).reserveSeatForReconnect(
        participantId,
        SessionTestFactories.createInstant()
      );
      
      const session = SessionTestFactories.createRunningSession({
        participants: [participant.toPrimitives()],
        rooms: [room.toPrimitives()],
      });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.restoreReservedSeat(participantId, at);

      // Assert
      expect(result).not.toBe(session);
      
      // Check participant presence updated
      const resultParticipant = result.findParticipantById(participantId);
      expect(resultParticipant!.toPrimitives().currentRoomId).toBe(roomId.toPrimitives());
      
      // Check room seat restored
      const resultRoom = result.findRoomById(roomId);
      expect(resultRoom!.occupiedSeats).toHaveLength(1);
      expect(resultRoom!.reservedSeats).toHaveLength(0);
      expect(resultRoom!.occupiedSeats[0].currentParticipantId).toBe(participantId.toPrimitives());
      expect(resultRoom!.occupiedSeats[0].isReservedForReconnect).toBe(false);
    });

    it('should work with multiple reserved seats in same room', () => {
      // Arrange
      const participantId1 = SessionTestFactories.createParticipantId();
      const participantId2 = SessionTestFactories.createParticipantId();
      
      const participant1 = SessionTestFactories.createParticipantPresence(participantId1);
      const participant2 = SessionTestFactories.createParticipantPresence(participantId2);
      
      const roomId = SessionTestFactories.createRoomId();
      let room = SessionTestFactories.createRoom(roomId);
      room = room.reserveSeatForReconnect(participantId1, SessionTestFactories.createInstant());
      room = room.reserveSeatForReconnect(participantId2, SessionTestFactories.createInstant());
      
      const session = SessionTestFactories.createRunningSession({
        participants: [participant1.toPrimitives(), participant2.toPrimitives()],
        rooms: [room.toPrimitives()],
      });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.restoreReservedSeat(participantId1, at);

      // Assert
      const resultRoom = result.findRoomById(roomId);
      expect(resultRoom!.occupiedSeats).toHaveLength(1);
      expect(resultRoom!.reservedSeats).toHaveLength(1);
      
      // Participant1 should be occupied
      expect(resultRoom!.occupiedSeats[0].currentParticipantId).toBe(participantId1.toPrimitives());
      
      // Participant2 should still be reserved
      expect(resultRoom!.reservedSeats[0].currentParticipantId).toBe(participantId2.toPrimitives());
    });

    it('should work with reserved seats in different rooms', () => {
      // Arrange
      const participantId1 = SessionTestFactories.createParticipantId();
      const participantId2 = SessionTestFactories.createParticipantId();
      
      const participant1 = SessionTestFactories.createParticipantPresence(participantId1);
      const participant2 = SessionTestFactories.createParticipantPresence(participantId2);
      
      const room1Id = SessionTestFactories.createRoomId();
      const room2Id = SessionTestFactories.createRoomId();
      
      const room1 = SessionTestFactories.createRoom(room1Id).reserveSeatForReconnect(
        participantId1,
        SessionTestFactories.createInstant()
      );
      const room2 = SessionTestFactories.createRoom(room2Id).reserveSeatForReconnect(
        participantId2,
        SessionTestFactories.createInstant()
      );
      
      const session = SessionTestFactories.createRunningSession({
        participants: [participant1.toPrimitives(), participant2.toPrimitives()],
        rooms: [room1.toPrimitives(), room2.toPrimitives()],
      });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.restoreReservedSeat(participantId1, at);

      // Assert
      const resultRoom1 = result.findRoomById(room1Id);
      const resultRoom2 = result.findRoomById(room2Id);
      
      // Room1 should have participant1 occupied
      expect(resultRoom1!.occupiedSeats).toHaveLength(1);
      expect(resultRoom1!.reservedSeats).toHaveLength(0);
      expect(resultRoom1!.occupiedSeats[0].currentParticipantId).toBe(participantId1.toPrimitives());
      
      // Room2 should still have participant2 reserved
      expect(resultRoom2!.occupiedSeats).toHaveLength(0);
      expect(resultRoom2!.reservedSeats).toHaveLength(1);
      expect(resultRoom2!.reservedSeats[0].currentParticipantId).toBe(participantId2.toPrimitives());
    });

    it('should update participant presence with correct room and timing', () => {
      // Arrange
      const participantId = SessionTestFactories.createParticipantId();
      const participant = SessionTestFactories.createParticipantPresence(participantId);
      
      const roomId = SessionTestFactories.createRoomId();
      const room = SessionTestFactories.createRoom(roomId).reserveSeatForReconnect(
        participantId,
        SessionTestFactories.createInstant()
      );
      
      const session = SessionTestFactories.createRunningSession({
        participants: [participant.toPrimitives()],
        rooms: [room.toPrimitives()],
      });
      const restoreTime = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.restoreReservedSeat(participantId, restoreTime);

      // Assert
      const resultParticipant = result.findParticipantById(participantId);
      expect(resultParticipant!.toPrimitives().currentRoomId).toBe(roomId.toPrimitives());
      
      // The participant should be considered as entering the room at restore time
      // (exact timestamp handling depends on ParticipantPresence implementation)
    });

    it('should not affect other rooms', () => {
      // Arrange
      const participantId = SessionTestFactories.createParticipantId();
      const otherParticipantId = SessionTestFactories.createParticipantId();
      
      const participant = SessionTestFactories.createParticipantPresence(participantId);
      const otherParticipant = SessionTestFactories.createParticipantPresence(otherParticipantId);
      
      const room1Id = SessionTestFactories.createRoomId();
      const room2Id = SessionTestFactories.createRoomId();
      
      const room1 = SessionTestFactories.createRoom(room1Id).reserveSeatForReconnect(
        participantId,
        SessionTestFactories.createInstant()
      );
      const room2 = SessionTestFactories.createRoom(room2Id).assignParticipantToSeat(
        otherParticipantId,
        SessionTestFactories.createInstant()
      );
      
      const session = SessionTestFactories.createRunningSession({
        participants: [participant.toPrimitives(), otherParticipant.toPrimitives()],
        rooms: [room1.toPrimitives(), room2.toPrimitives()],
      });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.restoreReservedSeat(participantId, at);

      // Assert
      const resultRoom1 = result.findRoomById(room1Id);
      const resultRoom2 = result.findRoomById(room2Id);
      
      // Room1 should have restored seat
      expect(resultRoom1!.occupiedSeats).toHaveLength(1);
      expect(resultRoom1!.reservedSeats).toHaveLength(0);
      
      // Room2 should remain unchanged
      expect(resultRoom2!.occupiedSeats).toHaveLength(1);
      expect(resultRoom2!.reservedSeats).toHaveLength(0);
      expect(resultRoom2!.occupiedSeats[0].currentParticipantId).toBe(otherParticipantId.toPrimitives());
    });
  });

  describe('immutability verification', () => {
    it('should return new instance when restoring seat', () => {
      // Arrange
      const participantId = SessionTestFactories.createParticipantId();
      const participant = SessionTestFactories.createParticipantPresence(participantId);
      
      const roomId = SessionTestFactories.createRoomId();
      const room = SessionTestFactories.createRoom(roomId).reserveSeatForReconnect(
        participantId,
        SessionTestFactories.createInstant()
      );
      
      const session = SessionTestFactories.createRunningSession({
        participants: [participant.toPrimitives()],
        rooms: [room.toPrimitives()],
      });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.restoreReservedSeat(participantId, at);

      // Assert
      expect(result).not.toBe(session);
      
      // Original should be unchanged
      const originalParticipant = session.findParticipantById(participantId);
      expect(originalParticipant!.toPrimitives().currentRoomId).toBeUndefined();
      
      const originalRoom = session.findRoomById(roomId);
      expect(originalRoom!.occupiedSeats).toHaveLength(0);
      expect(originalRoom!.reservedSeats).toHaveLength(1);
      
      // Result should have changes
      const resultParticipant = result.findParticipantById(participantId);
      expect(resultParticipant!.toPrimitives().currentRoomId).toBe(roomId.toPrimitives());
      
      const resultRoom = result.findRoomById(roomId);
      expect(resultRoom!.occupiedSeats).toHaveLength(1);
      expect(resultRoom!.reservedSeats).toHaveLength(0);
    });

    it('should preserve all other properties', () => {
      // Arrange
      const participantId = SessionTestFactories.createParticipantId();
      const participant = SessionTestFactories.createParticipantPresence(participantId);
      
      const roomId = SessionTestFactories.createRoomId();
      const room = SessionTestFactories.createRoom(roomId).reserveSeatForReconnect(
        participantId,
        SessionTestFactories.createInstant()
      );
      
      const rounds = [SessionTestFactories.createRoundInstance().toPrimitives()];
      
      const session = SessionTestFactories.createRunningSession({
        participants: [participant.toPrimitives()],
        rooms: [room.toPrimitives()],
        rounds,
        currentRoundIndex: 0,
      });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.restoreReservedSeat(participantId, at);

      // Assert
      expect(result.sessionId).toBe(session.sessionId);
      expect(result.config).toBe(session.config);
      expect(result.currentState).toBe(session.currentState);
      expect(result.createdByUserId).toBe(session.createdByUserId);
      expect(result.mainRoomId).toBe(session.mainRoomId);
      expect(result.createdAt).toBe(session.createdAt);
      expect(result.allRounds).toHaveLength(session.allRounds.length);
      expect(result.participantCount).toBe(session.participantCount);
      expect(result.toPrimitives().currentRoundIndex).toBe(session.toPrimitives().currentRoundIndex);
      expect(result.toPrimitives().hostId).toBe(session.toPrimitives().hostId);
    });
  });
});
