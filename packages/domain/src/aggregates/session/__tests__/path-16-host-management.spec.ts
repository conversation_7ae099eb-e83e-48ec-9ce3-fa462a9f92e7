import { Session } from '../session.aggregate';
import { SessionInvariantError } from '../session.errors';
import { SessionTestFactories } from './helpers/factories';

describe('Path 16: Host management (setHost)', () => {
  describe('setHost with valid participant', () => {
    it('should set host when participant exists in session', () => {
      // Arrange
      const session = SessionTestFactories.createSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add participant first
      const sessionWithParticipant = session.addParticipant(participantId, 'MEMBER', at);

      // Act
      const result = sessionWithParticipant.setHost(participantId, at);

      // Assert
      expect(result).not.toBe(sessionWithParticipant);
      expect(result.toPrimitives().hostId).toBe(participantId.toPrimitives());
    });

    it('should update existing host to new participant', () => {
      // Arrange
      const session = SessionTestFactories.createSession();
      const participantId1 = SessionTestFactories.createParticipantId();
      const participantId2 = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add participants and set first as host
      let sessionWithParticipants = session
        .addParticipant(participantId1, 'MEMBER', at)
        .addParticipant(participantId2, 'MEMBER', at)
        .setHost(participantId1, at);

      // Verify first host is set
      expect(sessionWithParticipants.toPrimitives().hostId).toBe(participantId1.toPrimitives());

      // Act - Change host to second participant
      const result = sessionWithParticipants.setHost(participantId2, at);

      // Assert
      expect(result).not.toBe(sessionWithParticipants);
      expect(result.toPrimitives().hostId).toBe(participantId2.toPrimitives());
    });

    it('should work in any session state', () => {
      // Arrange
      const states = ['SCHEDULED', 'RUNNING', 'PAUSED', 'COMPLETED', 'CANCELED'];
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      for (const state of states) {
        const session = SessionTestFactories.createSession({ state: state as any });
        const sessionWithParticipant = session.addParticipant(participantId, 'MEMBER', at);

        // Act
        const result = sessionWithParticipant.setHost(participantId, at);

        // Assert
        expect(result.toPrimitives().hostId).toBe(participantId.toPrimitives());
      }
    });

    it('should promote participant role to HOST when setting host', () => {
      // Arrange
      const session = SessionTestFactories.createSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add participant as MEMBER
      const sessionWithParticipant = session.addParticipant(participantId, 'MEMBER', at);
      
      // Verify participant is MEMBER
      const originalParticipant = sessionWithParticipant.getParticipant(participantId);
      expect(originalParticipant?.role).toBe('MEMBER');

      // Act
      const result = sessionWithParticipant.setHost(participantId, at);

      // Assert
      const hostParticipant = result.getParticipant(participantId);
      expect(hostParticipant?.role).toBe('HOST');
    });

    it('should demote previous host to MEMBER when changing host', () => {
      // Arrange
      const session = SessionTestFactories.createSession();
      const participantId1 = SessionTestFactories.createParticipantId();
      const participantId2 = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add participants and set first as host
      let sessionWithParticipants = session
        .addParticipant(participantId1, 'MEMBER', at)
        .addParticipant(participantId2, 'MEMBER', at)
        .setHost(participantId1, at);

      // Verify first participant is HOST
      const firstHost = sessionWithParticipants.getParticipant(participantId1);
      expect(firstHost?.role).toBe('HOST');

      // Act - Change host to second participant
      const result = sessionWithParticipants.setHost(participantId2, at);

      // Assert
      const formerHost = result.getParticipant(participantId1);
      const newHost = result.getParticipant(participantId2);
      
      expect(formerHost?.role).toBe('MEMBER');
      expect(newHost?.role).toBe('HOST');
    });
  });

  describe('setHost with invalid participant', () => {
    it('should throw SessionInvariantError when participant does not exist', () => {
      // Arrange
      const session = SessionTestFactories.createSession();
      const nonExistentParticipantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert
      expect(() => session.setHost(nonExistentParticipantId, at))
        .toThrow(SessionInvariantError);
      expect(() => session.setHost(nonExistentParticipantId, at))
        .toThrow('Cannot set host: participant not found in session');
    });

    it('should include context in participant not found error', () => {
      // Arrange
      const session = SessionTestFactories.createSession();
      const nonExistentParticipantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert
      try {
        session.setHost(nonExistentParticipantId, at);
        fail('Expected SessionInvariantError to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(SessionInvariantError);
        expect((error as any).context).toMatchObject({
          participantId: nonExistentParticipantId.toPrimitives(),
          sessionId: session.sessionId.toPrimitives(),
        });
      }
    });
  });

  describe('clearHost functionality', () => {
    it('should clear host when setting to null', () => {
      // Arrange
      const session = SessionTestFactories.createSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add participant and set as host
      const sessionWithHost = session
        .addParticipant(participantId, 'MEMBER', at)
        .setHost(participantId, at);

      // Verify host is set
      expect(sessionWithHost.toPrimitives().hostId).toBe(participantId.toPrimitives());

      // Act - Clear host
      const result = sessionWithHost.clearHost(at);

      // Assert
      expect(result).not.toBe(sessionWithHost);
      expect(result.toPrimitives().hostId).toBeNull();
    });

    it('should demote host participant to MEMBER when clearing host', () => {
      // Arrange
      const session = SessionTestFactories.createSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add participant and set as host
      const sessionWithHost = session
        .addParticipant(participantId, 'MEMBER', at)
        .setHost(participantId, at);

      // Verify participant is HOST
      const hostParticipant = sessionWithHost.getParticipant(participantId);
      expect(hostParticipant?.role).toBe('HOST');

      // Act - Clear host
      const result = sessionWithHost.clearHost(at);

      // Assert
      const formerHostParticipant = result.getParticipant(participantId);
      expect(formerHostParticipant?.role).toBe('MEMBER');
    });

    it('should be no-op when no host is set', () => {
      // Arrange
      const session = SessionTestFactories.createSession();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Verify no host is set
      expect(session.toPrimitives().hostId).toBeNull();

      // Act
      const result = session.clearHost(at);

      // Assert
      expect(result).toBe(session); // Same instance for no-op
    });
  });

  describe('idempotency', () => {
    it('should be idempotent when setting same participant as host', () => {
      // Arrange
      const session = SessionTestFactories.createSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add participant and set as host
      const sessionWithHost = session
        .addParticipant(participantId, 'MEMBER', at)
        .setHost(participantId, at);

      // Act - Set same participant as host again
      const result = sessionWithHost.setHost(participantId, at);

      // Assert
      expect(result).toBe(sessionWithHost); // Same instance for no-op
      expect(result.toPrimitives().hostId).toBe(participantId.toPrimitives());
    });

    it('should be idempotent when clearing host multiple times', () => {
      // Arrange
      const session = SessionTestFactories.createSession();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Verify no host is set
      expect(session.toPrimitives().hostId).toBeNull();

      // Act - Clear host multiple times
      const result1 = session.clearHost(at);
      const result2 = result1.clearHost(at);

      // Assert
      expect(result1).toBe(session); // Same instance for no-op
      expect(result2).toBe(result1); // Same instance for no-op
      expect(result2.toPrimitives().hostId).toBeNull();
    });
  });

  describe('host management with participant lifecycle', () => {
    it('should preserve host when participant leaves and rejoins', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add participant, set as host, join, then leave
      let sessionWithHost = session
        .addParticipant(participantId, 'MEMBER', at)
        .setHost(participantId, at)
        .onJoin(participantId, at)
        .onLeave(participantId, at);

      // Verify host is still set and participant has reservation
      expect(sessionWithHost.toPrimitives().hostId).toBe(participantId.toPrimitives());
      expect(sessionWithHost.getParticipant(participantId)).toBeDefined();

      // Act - Participant rejoins
      const result = sessionWithHost.onJoin(participantId, at);

      // Assert
      expect(result.toPrimitives().hostId).toBe(participantId.toPrimitives());
      const hostParticipant = result.getParticipant(participantId);
      expect(hostParticipant?.role).toBe('HOST');
    });

    it('should clear host when host participant is removed from session', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add participant, set as host, join, leave, then release reservation
      let sessionWithHost = session
        .addParticipant(participantId, 'MEMBER', at)
        .setHost(participantId, at)
        .onJoin(participantId, at)
        .onLeave(participantId, at);

      // Verify host is set
      expect(sessionWithHost.toPrimitives().hostId).toBe(participantId.toPrimitives());

      // Act - Release all reserved seats (removes participant from session)
      const result = sessionWithHost.releaseAllReservedSeats(at);

      // Assert
      expect(result.toPrimitives().hostId).toBeNull();
      expect(result.getParticipant(participantId)).toBeUndefined();
    });
  });

  describe('immutability verification', () => {
    it('should return new instance when setting host', () => {
      // Arrange
      const session = SessionTestFactories.createSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      const sessionWithParticipant = session.addParticipant(participantId, 'MEMBER', at);

      // Act
      const result = sessionWithParticipant.setHost(participantId, at);

      // Assert
      expect(result).not.toBe(sessionWithParticipant);
      
      // Original should be unchanged
      expect(sessionWithParticipant.toPrimitives().hostId).toBeNull();
      const originalParticipant = sessionWithParticipant.getParticipant(participantId);
      expect(originalParticipant?.role).toBe('MEMBER');
    });

    it('should preserve all other properties when setting host', () => {
      // Arrange
      const session = SessionTestFactories.createSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      const sessionWithParticipant = session.addParticipant(participantId, 'MEMBER', at);

      // Act
      const result = sessionWithParticipant.setHost(participantId, at);

      // Assert
      expect(result.sessionId).toBe(sessionWithParticipant.sessionId);
      expect(result.config).toBe(sessionWithParticipant.config);
      expect(result.currentState).toBe(sessionWithParticipant.currentState);
      expect(result.createdByUserId).toBe(sessionWithParticipant.createdByUserId);
      expect(result.mainRoomId).toBe(sessionWithParticipant.mainRoomId);
      expect(result.createdAt).toBe(sessionWithParticipant.createdAt);
      expect(result.participantCount).toBe(sessionWithParticipant.participantCount);
      expect(result.allRooms.length).toBe(sessionWithParticipant.allRooms.length);
    });
  });
});
