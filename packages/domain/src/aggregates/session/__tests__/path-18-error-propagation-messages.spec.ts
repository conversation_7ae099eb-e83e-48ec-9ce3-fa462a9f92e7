import { Session } from '../session.aggregate';
import { 
  SessionStateError, 
  SessionInvariantError, 
  SessionCapacityError, 
  SessionParticipantNotFoundError 
} from '../session.errors';
import { PersistenceMappingError } from '../../../errors/persistence-mapping-error';
import { SessionTestFactories } from './helpers/factories';

describe('Path 18: Error propagation & messages', () => {
  describe('SessionStateError propagation', () => {
    it('should throw SessionStateError with correct message for invalid lifecycle transitions', () => {
      // Arrange
      const session = SessionTestFactories.createSession({ state: 'COMPLETED' });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert
      expect(() => session.startSession(at)).toThrow(SessionStateError);
      expect(() => session.startSession(at)).toThrow('Cannot start session from COMPLETED state');
    });

    it('should throw SessionStateError with context for state-dependent operations', () => {
      // Arrange
      const session = SessionTestFactories.createSession({ state: 'SCHEDULED' });
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert
      try {
        session.onJoin(participantId, at);
        fail('Expected SessionStateError to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(SessionStateError);
        expect(error.message).toContain('Cannot join unless session is RUNNING');
        expect((error as any).context).toMatchObject({
          currentState: 'SCHEDULED',
          requiredState: 'RUNNING',
          operation: 'onJoin',
        });
      }
    });

    it('should propagate SessionStateError from nested operations', () => {
      // Arrange
      const session = SessionTestFactories.createSession({ state: 'PAUSED' });
      const roundSpec = SessionTestFactories.createRoundSpec();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert
      expect(() => session.addRound(roundSpec, at)).toThrow(SessionStateError);
      expect(() => session.addRound(roundSpec, at)).toThrow('Cannot add round unless session is RUNNING');
    });
  });

  describe('SessionInvariantError propagation', () => {
    it('should throw SessionInvariantError with descriptive messages for business rule violations', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const nonExistentRoomId = SessionTestFactories.createRoomId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert
      expect(() => session.makeRoomReady(nonExistentRoomId, at)).toThrow(SessionInvariantError);
      expect(() => session.makeRoomReady(nonExistentRoomId, at)).toThrow('Room not found in session');
    });

    it('should include detailed context in SessionInvariantError', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const nonExistentParticipantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert
      try {
        session.setHost(nonExistentParticipantId, at);
        fail('Expected SessionInvariantError to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(SessionInvariantError);
        expect(error.message).toContain('Cannot set host: participant not found in session');
        expect((error as any).context).toMatchObject({
          participantId: nonExistentParticipantId.toPrimitives(),
          sessionId: session.sessionId.toPrimitives(),
        });
      }
    });

    it('should propagate SessionInvariantError from room operations', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Add participant and create breakout room with insufficient participants
      let sessionWithParticipant = session
        .addParticipant(participantId, 'MEMBER', at)
        .onJoin(participantId, at);

      const breakoutRoomId = SessionTestFactories.createRoomId();
      sessionWithParticipant = sessionWithParticipant
        .assignParticipantToRoom(participantId, breakoutRoomId, at);

      // Act & Assert - Room doesn't meet minSeats requirement
      expect(() => sessionWithParticipant.makeRoomReady(breakoutRoomId, at)).toThrow();
    });
  });

  describe('SessionCapacityError propagation', () => {
    it('should throw SessionCapacityError with clear message when exceeding participant limit', () => {
      // Arrange
      const config = SessionTestFactories.createDefaultSessionConfig({ maxParticipants: 1 });
      const session = SessionTestFactories.createSession({ config });
      const participantId1 = SessionTestFactories.createParticipantId();
      const participantId2 = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      const sessionWithParticipant = session.addParticipant(participantId1, 'MEMBER', at);

      // Act & Assert
      expect(() => sessionWithParticipant.addParticipant(participantId2, 'MEMBER', at))
        .toThrow(SessionCapacityError);
      expect(() => sessionWithParticipant.addParticipant(participantId2, 'MEMBER', at))
        .toThrow('Session has reached maximum participant capacity');
    });

    it('should include capacity details in SessionCapacityError context', () => {
      // Arrange
      const config = SessionTestFactories.createDefaultSessionConfig({ maxParticipants: 2 });
      const session = SessionTestFactories.createRunningSession({ config });
      const participantIds = [
        SessionTestFactories.createParticipantId(),
        SessionTestFactories.createParticipantId(),
        SessionTestFactories.createParticipantId(),
      ];
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Fill to capacity
      let sessionWithParticipants = session;
      for (let i = 0; i < 2; i++) {
        sessionWithParticipants = sessionWithParticipants
          .addParticipant(participantIds[i], 'MEMBER', at)
          .onJoin(participantIds[i], at);
      }

      // Act & Assert
      try {
        sessionWithParticipants.onJoin(participantIds[2], at);
        fail('Expected SessionCapacityError to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(SessionCapacityError);
        expect((error as any).context).toMatchObject({
          currentJoinedCount: 2,
          maxParticipants: 2,
          attemptedParticipantId: participantIds[2].toPrimitives(),
        });
      }
    });
  });

  describe('SessionParticipantNotFoundError propagation', () => {
    it('should throw SessionParticipantNotFoundError for operations on non-existent participants', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const nonExistentParticipantId = SessionTestFactories.createParticipantId();
      const roomId = SessionTestFactories.createRoomId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert
      expect(() => session.assignParticipantToRoom(nonExistentParticipantId, roomId, at))
        .toThrow(SessionParticipantNotFoundError);
      expect(() => session.assignParticipantToRoom(nonExistentParticipantId, roomId, at))
        .toThrow('Participant not found in session');
    });

    it('should include participant details in SessionParticipantNotFoundError context', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const nonExistentParticipantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert
      try {
        session.restoreReservedSeat(nonExistentParticipantId, at);
        fail('Expected SessionParticipantNotFoundError to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(SessionParticipantNotFoundError);
        expect((error as any).context).toMatchObject({
          participantId: nonExistentParticipantId.toPrimitives(),
          sessionId: session.sessionId.toPrimitives(),
        });
      }
    });
  });

  describe('PersistenceMappingError propagation', () => {
    it('should throw PersistenceMappingError for invalid session primitives', () => {
      // Arrange
      const invalidPrimitives = {
        sessionId: 'invalid-uuid',
        state: 'INVALID_STATE',
        config: null,
        createdByUserId: 'invalid-uuid',
        createdAt: 'invalid-timestamp',
        mainRoomId: 'invalid-uuid',
        hostId: null,
        currentRoundIndex: -1,
        participants: [],
        rooms: [],
        rounds: [],
      };

      // Act & Assert
      expect(() => Session.fromPrimitives(invalidPrimitives as any))
        .toThrow(PersistenceMappingError);
    });

    it('should wrap nested mapping errors in PersistenceMappingError', () => {
      // Arrange
      const primitivesWithInvalidConfig = {
        sessionId: SessionTestFactories.createSessionId().toPrimitives(),
        state: 'SCHEDULED',
        config: {
          maxParticipants: -1, // Invalid
          defaultRoundDurationMs: 900000,
          scheduledStartAt: SessionTestFactories.createInstant().toPrimitives(),
          defaultRoomConfig: SessionTestFactories.createDefaultRoomConfig().toPrimitives(),
          autopilotPolicy: SessionTestFactories.createAutopilotPolicy().toPrimitives(),
          lateJoinPolicy: SessionTestFactories.createLateJoinPolicy().toPrimitives(),
          lobbyAdmissionPolicy: SessionTestFactories.createLobbyAdmissionPolicy().toPrimitives(),
        },
        createdByUserId: SessionTestFactories.createParticipantId().toPrimitives(),
        createdAt: SessionTestFactories.createInstant().toPrimitives(),
        mainRoomId: SessionTestFactories.createRoomId().toPrimitives(),
        hostId: null,
        currentRoundIndex: -1,
        participants: [],
        rooms: [SessionTestFactories.createRoom().toPrimitives()],
        rounds: [],
      };

      // Act & Assert
      expect(() => Session.fromPrimitives(primitivesWithInvalidConfig as any))
        .toThrow(PersistenceMappingError);
    });
  });

  describe('error message consistency', () => {
    it('should provide consistent error messages for similar operations', () => {
      // Arrange
      const session = SessionTestFactories.createSession({ state: 'COMPLETED' });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert - All state-dependent operations should have consistent messaging
      const operations = [
        () => session.pauseSession(at),
        () => session.resumeSession(at),
        () => session.onJoin(SessionTestFactories.createParticipantId(), at),
        () => session.addRound(SessionTestFactories.createRoundSpec(), at),
      ];

      for (const operation of operations) {
        expect(operation).toThrow(SessionStateError);
        try {
          operation();
        } catch (error) {
          expect(error.message).toMatch(/Cannot .+ unless session is RUNNING/);
        }
      }
    });

    it('should provide actionable error messages', () => {
      // Arrange
      const session = SessionTestFactories.createSession({ state: 'SCHEDULED' });
      const participantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert
      try {
        session.onJoin(participantId, at);
        fail('Expected SessionStateError to be thrown');
      } catch (error) {
        expect(error.message).toContain('Cannot join unless session is RUNNING');
        expect(error.message).not.toContain('undefined');
        expect(error.message).not.toContain('null');
        expect(error.message.length).toBeGreaterThan(10); // Meaningful message
      }
    });
  });

  describe('error context completeness', () => {
    it('should include all relevant context in capacity errors', () => {
      // Arrange
      const config = SessionTestFactories.createDefaultSessionConfig({ maxParticipants: 1 });
      const session = SessionTestFactories.createSession({ config });
      const participantId1 = SessionTestFactories.createParticipantId();
      const participantId2 = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      const sessionWithParticipant = session.addParticipant(participantId1, 'MEMBER', at);

      // Act & Assert
      try {
        sessionWithParticipant.addParticipant(participantId2, 'MEMBER', at);
        fail('Expected SessionCapacityError to be thrown');
      } catch (error) {
        const context = (error as any).context;
        expect(context).toHaveProperty('currentParticipantCount');
        expect(context).toHaveProperty('maxParticipants');
        expect(context).toHaveProperty('attemptedParticipantId');
        expect(context.currentParticipantCount).toBe(1);
        expect(context.maxParticipants).toBe(1);
        expect(context.attemptedParticipantId).toBe(participantId2.toPrimitives());
      }
    });

    it('should include session and operation context in invariant errors', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const nonExistentRoomId = SessionTestFactories.createRoomId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert
      try {
        session.makeRoomReady(nonExistentRoomId, at);
        fail('Expected SessionInvariantError to be thrown');
      } catch (error) {
        const context = (error as any).context;
        expect(context).toHaveProperty('roomId');
        expect(context).toHaveProperty('sessionId');
        expect(context.roomId).toBe(nonExistentRoomId.toPrimitives());
        expect(context.sessionId).toBe(session.sessionId.toPrimitives());
      }
    });
  });

  describe('error chaining and stack traces', () => {
    it('should preserve original error information when wrapping errors', () => {
      // Arrange
      const invalidPrimitives = {
        sessionId: 'not-a-uuid',
        state: 'SCHEDULED',
        config: SessionTestFactories.createDefaultSessionConfig().toPrimitives(),
        createdByUserId: SessionTestFactories.createParticipantId().toPrimitives(),
        createdAt: SessionTestFactories.createInstant().toPrimitives(),
        mainRoomId: SessionTestFactories.createRoomId().toPrimitives(),
        hostId: null,
        currentRoundIndex: -1,
        participants: [],
        rooms: [SessionTestFactories.createRoom().toPrimitives()],
        rounds: [],
      };

      // Act & Assert
      try {
        Session.fromPrimitives(invalidPrimitives as any);
        fail('Expected PersistenceMappingError to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(PersistenceMappingError);
        expect(error.stack).toBeDefined();
        expect(error.stack.length).toBeGreaterThan(0);
      }
    });

    it('should maintain error hierarchy for debugging', () => {
      // Arrange
      const session = SessionTestFactories.createSession({ state: 'COMPLETED' });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert
      try {
        session.startSession(at);
        fail('Expected SessionStateError to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(SessionStateError);
        expect(error.name).toBe('SessionStateError');
        expect(error.constructor.name).toBe('SessionStateError');
      }
    });
  });
});
