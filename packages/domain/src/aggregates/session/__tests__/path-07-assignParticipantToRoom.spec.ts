import { Session } from '../session.aggregate';
import { SessionStateError, SessionInvariantError } from '../session.errors';
import { SessionTestFactories } from './helpers/factories';

describe('Path 7: assignParticipantToRoom – invariants & transitions', () => {
  describe('throws SessionStateError if not RUNNING', () => {
    const invalidStates = ['SCHEDULED', 'PAUSED', 'COMPLETED', 'CANCELED'];

    it.each(invalidStates)('should throw when session is %s', (state) => {
      // Arrange
      const participantId = SessionTestFactories.createParticipantId();
      const targetRoomId = SessionTestFactories.createRoomId();
      const session = SessionTestFactories.createSession({
        state: state as any,
        participants: [SessionTestFactories.createParticipantPresence(participantId).toPrimitives()],
      });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert
      expect(() => session.assignParticipantToRoom(participantId, targetRoomId, at)).toThrow(SessionStateError);
      expect(() => session.assignParticipantToRoom(participantId, targetRoomId, at)).toThrow('Cannot assign participant to room unless session is RUNNING');
    });
  });

  describe('throws SessionInvariantError if participant has ANY reserved seat', () => {
    it('should throw when participant has reserved seat in any room', () => {
      // Arrange
      const participantId = SessionTestFactories.createParticipantId();
      const participant = SessionTestFactories.createParticipantPresence(participantId);
      
      // Create room with reserved seat for participant
      const roomWithReservation = SessionTestFactories.createRoom().reserveSeatForReconnect(
        participantId,
        SessionTestFactories.createInstant()
      );
      
      const targetRoomId = SessionTestFactories.createRoomId();
      const targetRoom = SessionTestFactories.createRoom(targetRoomId);
      
      const rooms = [
        roomWithReservation.toPrimitives(),
        targetRoom.toPrimitives(),
      ];
      
      const session = SessionTestFactories.createRunningSession({
        participants: [participant.toPrimitives()],
        rooms,
      });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert
      expect(() => session.assignParticipantToRoom(participantId, targetRoomId, at)).toThrow(SessionInvariantError);
      expect(() => session.assignParticipantToRoom(participantId, targetRoomId, at)).toThrow('Cannot move participant to another room while their seat is reserved for reconnect');
    });

    it('should include context in reservation error', () => {
      // Arrange
      const participantId = SessionTestFactories.createParticipantId();
      const participant = SessionTestFactories.createParticipantPresence(participantId);
      
      const roomWithReservation = SessionTestFactories.createRoom().reserveSeatForReconnect(
        participantId,
        SessionTestFactories.createInstant()
      );
      
      const targetRoomId = SessionTestFactories.createRoomId();
      const targetRoom = SessionTestFactories.createRoom(targetRoomId);
      
      const rooms = [
        roomWithReservation.toPrimitives(),
        targetRoom.toPrimitives(),
      ];
      
      const session = SessionTestFactories.createRunningSession({
        participants: [participant.toPrimitives()],
        rooms,
      });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act & Assert
      try {
        session.assignParticipantToRoom(participantId, targetRoomId, at);
        fail('Expected SessionInvariantError to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(SessionInvariantError);
        expect((error as any).context).toMatchObject({
          participantId: participantId.toPrimitives(),
          sessionId: session.sessionId.toPrimitives(),
        });
      }
    });
  });

  describe('if participant already in target room → no-op', () => {
    it('should return same instance when participant already in target room', () => {
      // Arrange
      const participantId = SessionTestFactories.createParticipantId();
      const participant = SessionTestFactories.createParticipantPresence(participantId);
      
      const targetRoomId = SessionTestFactories.createRoomId();
      const targetRoom = SessionTestFactories.createRoom(targetRoomId).assignParticipantToSeat(
        participantId,
        SessionTestFactories.createInstant()
      );
      
      const rooms = [targetRoom.toPrimitives()];
      
      const session = SessionTestFactories.createRunningSession({
        participants: [participant.toPrimitives()],
        rooms,
      });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.assignParticipantToRoom(participantId, targetRoomId, at);

      // Assert
      expect(result).toBe(session); // Same instance (no-op)
    });
  });

  describe('moving from A→B: presence exits A and enters B, rooms update seats', () => {
    it('should move participant from source room to target room', () => {
      // Arrange
      const participantId = SessionTestFactories.createParticipantId();
      const participant = SessionTestFactories.createParticipantPresence(participantId);
      
      const sourceRoomId = SessionTestFactories.createRoomId();
      const targetRoomId = SessionTestFactories.createRoomId();
      
      // Participant starts in source room
      const sourceRoom = SessionTestFactories.createRoom(sourceRoomId).assignParticipantToSeat(
        participantId,
        SessionTestFactories.createInstant()
      );
      const targetRoom = SessionTestFactories.createRoom(targetRoomId);
      
      // Update participant presence to reflect being in source room
      const participantInSourceRoom = participant.enterRoom(sourceRoomId, SessionTestFactories.createInstant());
      
      const rooms = [
        sourceRoom.toPrimitives(),
        targetRoom.toPrimitives(),
      ];
      
      const session = SessionTestFactories.createRunningSession({
        participants: [participantInSourceRoom.toPrimitives()],
        rooms,
      });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.assignParticipantToRoom(participantId, targetRoomId, at);

      // Assert
      expect(result).not.toBe(session);
      
      // Check participant presence updated
      const resultParticipant = result.findParticipantById(participantId);
      expect(resultParticipant!.toPrimitives().currentRoomId).toBe(targetRoomId.toPrimitives());
      
      // Check source room released seat
      const resultSourceRoom = result.findRoomById(sourceRoomId);
      expect(resultSourceRoom!.occupiedSeats).toHaveLength(0);
      
      // Check target room assigned seat
      const resultTargetRoom = result.findRoomById(targetRoomId);
      expect(resultTargetRoom!.occupiedSeats).toHaveLength(1);
      expect(resultTargetRoom!.occupiedSeats[0].currentParticipantId).toBe(participantId.toPrimitives());
    });

    it('should update presence with correct timestamps', () => {
      // Arrange
      const participantId = SessionTestFactories.createParticipantId();
      const participant = SessionTestFactories.createParticipantPresence(participantId);
      
      const sourceRoomId = SessionTestFactories.createRoomId();
      const targetRoomId = SessionTestFactories.createRoomId();
      
      const sourceRoom = SessionTestFactories.createRoom(sourceRoomId).assignParticipantToSeat(
        participantId,
        SessionTestFactories.createInstant()
      );
      const targetRoom = SessionTestFactories.createRoom(targetRoomId);
      
      const enterTime = SessionTestFactories.createInstant(SessionTestFactories.BASE_TIMESTAMP);
      const participantInSourceRoom = participant.enterRoom(sourceRoomId, enterTime);
      
      const rooms = [
        sourceRoom.toPrimitives(),
        targetRoom.toPrimitives(),
      ];
      
      const session = SessionTestFactories.createRunningSession({
        participants: [participantInSourceRoom.toPrimitives()],
        rooms,
      });
      const moveTime = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.assignParticipantToRoom(participantId, targetRoomId, moveTime);

      // Assert
      const resultParticipant = result.findParticipantById(participantId);
      const primitives = resultParticipant!.toPrimitives();
      
      // Should have entered target room at move time
      expect(primitives.currentRoomId).toBe(targetRoomId.toPrimitives());
      // Note: The exact timestamp tracking depends on ParticipantPresence implementation
      // We verify the room assignment is correct
    });
  });

  describe('moving from undefined→B: only enters B, no source release', () => {
    it('should assign participant to room when not previously seated', () => {
      // Arrange
      const participantId = SessionTestFactories.createParticipantId();
      const participant = SessionTestFactories.createParticipantPresence(participantId); // Not seated anywhere
      
      const targetRoomId = SessionTestFactories.createRoomId();
      const targetRoom = SessionTestFactories.createRoom(targetRoomId);
      
      const rooms = [targetRoom.toPrimitives()];
      
      const session = SessionTestFactories.createRunningSession({
        participants: [participant.toPrimitives()],
        rooms,
      });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.assignParticipantToRoom(participantId, targetRoomId, at);

      // Assert
      expect(result).not.toBe(session);
      
      // Check participant presence updated
      const resultParticipant = result.findParticipantById(participantId);
      expect(resultParticipant!.toPrimitives().currentRoomId).toBe(targetRoomId.toPrimitives());
      
      // Check target room assigned seat
      const resultTargetRoom = result.findRoomById(targetRoomId);
      expect(resultTargetRoom!.occupiedSeats).toHaveLength(1);
      expect(resultTargetRoom!.occupiedSeats[0].currentParticipantId).toBe(participantId.toPrimitives());
    });

    it('should not affect other rooms when participant was not seated', () => {
      // Arrange
      const participantId = SessionTestFactories.createParticipantId();
      const participant = SessionTestFactories.createParticipantPresence(participantId);
      
      const otherRoomId = SessionTestFactories.createRoomId();
      const targetRoomId = SessionTestFactories.createRoomId();
      
      const otherRoom = SessionTestFactories.createRoom(otherRoomId);
      const targetRoom = SessionTestFactories.createRoom(targetRoomId);
      
      const rooms = [
        otherRoom.toPrimitives(),
        targetRoom.toPrimitives(),
      ];
      
      const session = SessionTestFactories.createRunningSession({
        participants: [participant.toPrimitives()],
        rooms,
      });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.assignParticipantToRoom(participantId, targetRoomId, at);

      // Assert
      // Other room should remain unchanged
      const resultOtherRoom = result.findRoomById(otherRoomId);
      expect(resultOtherRoom!.occupiedSeats).toHaveLength(0);
      
      // Target room should have the participant
      const resultTargetRoom = result.findRoomById(targetRoomId);
      expect(resultTargetRoom!.occupiedSeats).toHaveLength(1);
      expect(resultTargetRoom!.occupiedSeats[0].currentParticipantId).toBe(participantId.toPrimitives());
    });
  });

  describe('invariant: participant cannot occupy two rooms at once', () => {
    it('should ensure participant is only in target room after assignment', () => {
      // Arrange
      const participantId = SessionTestFactories.createParticipantId();
      const participant = SessionTestFactories.createParticipantPresence(participantId);
      
      const sourceRoomId = SessionTestFactories.createRoomId();
      const targetRoomId = SessionTestFactories.createRoomId();
      const otherRoomId = SessionTestFactories.createRoomId();
      
      const sourceRoom = SessionTestFactories.createRoom(sourceRoomId).assignParticipantToSeat(
        participantId,
        SessionTestFactories.createInstant()
      );
      const targetRoom = SessionTestFactories.createRoom(targetRoomId);
      const otherRoom = SessionTestFactories.createRoom(otherRoomId);
      
      const participantInSourceRoom = participant.enterRoom(sourceRoomId, SessionTestFactories.createInstant());
      
      const rooms = [
        sourceRoom.toPrimitives(),
        targetRoom.toPrimitives(),
        otherRoom.toPrimitives(),
      ];
      
      const session = SessionTestFactories.createRunningSession({
        participants: [participantInSourceRoom.toPrimitives()],
        rooms,
      });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.assignParticipantToRoom(participantId, targetRoomId, at);

      // Assert
      let participantFoundInRooms = 0;
      let participantRoomId: string | undefined;
      
      result.allRooms.forEach(room => {
        const hasParticipant = room.occupiedSeats.some(seat => 
          seat.currentParticipantId === participantId.toPrimitives()
        );
        if (hasParticipant) {
          participantFoundInRooms++;
          participantRoomId = room.roomId.toPrimitives();
        }
      });
      
      expect(participantFoundInRooms).toBe(1); // Exactly one room
      expect(participantRoomId).toBe(targetRoomId.toPrimitives());
    });
  });

  describe('immutability verification', () => {
    it('should return new instance when assigning participant', () => {
      // Arrange
      const participantId = SessionTestFactories.createParticipantId();
      const participant = SessionTestFactories.createParticipantPresence(participantId);
      
      const targetRoomId = SessionTestFactories.createRoomId();
      const targetRoom = SessionTestFactories.createRoom(targetRoomId);
      
      const rooms = [targetRoom.toPrimitives()];
      
      const session = SessionTestFactories.createRunningSession({
        participants: [participant.toPrimitives()],
        rooms,
      });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.assignParticipantToRoom(participantId, targetRoomId, at);

      // Assert
      expect(result).not.toBe(session);
      
      // Original should be unchanged
      const originalParticipant = session.findParticipantById(participantId);
      expect(originalParticipant!.toPrimitives().currentRoomId).toBeUndefined();
      
      const originalRoom = session.findRoomById(targetRoomId);
      expect(originalRoom!.occupiedSeats).toHaveLength(0);
      
      // Result should have changes
      const resultParticipant = result.findParticipantById(participantId);
      expect(resultParticipant!.toPrimitives().currentRoomId).toBe(targetRoomId.toPrimitives());
      
      const resultRoom = result.findRoomById(targetRoomId);
      expect(resultRoom!.occupiedSeats).toHaveLength(1);
    });

    it('should preserve all other properties', () => {
      // Arrange
      const participantId = SessionTestFactories.createParticipantId();
      const participant = SessionTestFactories.createParticipantPresence(participantId);
      
      const targetRoomId = SessionTestFactories.createRoomId();
      const targetRoom = SessionTestFactories.createRoom(targetRoomId);
      
      const rounds = [SessionTestFactories.createRoundInstance().toPrimitives()];
      const rooms = [targetRoom.toPrimitives()];
      
      const session = SessionTestFactories.createRunningSession({
        participants: [participant.toPrimitives()],
        rounds,
        rooms,
        currentRoundIndex: 0,
      });
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.assignParticipantToRoom(participantId, targetRoomId, at);

      // Assert
      expect(result.sessionId).toBe(session.sessionId);
      expect(result.config).toBe(session.config);
      expect(result.currentState).toBe(session.currentState);
      expect(result.createdByUserId).toBe(session.createdByUserId);
      expect(result.mainRoomId).toBe(session.mainRoomId);
      expect(result.createdAt).toBe(session.createdAt);
      expect(result.allRounds).toHaveLength(session.allRounds.length);
      expect(result.toPrimitives().currentRoundIndex).toBe(session.toPrimitives().currentRoundIndex);
      expect(result.toPrimitives().hostId).toBe(session.toPrimitives().hostId);
    });
  });
});
