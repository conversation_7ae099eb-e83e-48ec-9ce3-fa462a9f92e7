import { Session } from '../session.aggregate';
import { SessionTestFactories } from './helpers/factories';

describe('Path 8: onLeave – reservation behavior', () => {
  describe('leaving a non-existent participant is no-op', () => {
    it('should return same instance when participant does not exist', () => {
      // Arrange
      const session = SessionTestFactories.createRunningSession();
      const nonExistentParticipantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.onLeave(nonExistentParticipantId, at);

      // Assert
      expect(result).toBe(session); // Same instance (no-op)
    });

    it('should not affect any rooms when participant does not exist', () => {
      // Arrange
      const existingParticipantId = SessionTestFactories.createParticipantId();
      const existingParticipant = SessionTestFactories.createParticipantPresence(existingParticipantId);
      
      const roomId = SessionTestFactories.createRoomId();
      const room = SessionTestFactories.createRoom(roomId).assignParticipantToSeat(
        existingParticipantId,
        SessionTestFactories.createInstant()
      );
      
      const session = SessionTestFactories.createRunningSession({
        participants: [existingParticipant.toPrimitives()],
        rooms: [room.toPrimitives()],
      });
      
      const nonExistentParticipantId = SessionTestFactories.createParticipantId();
      const at = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.onLeave(nonExistentParticipantId, at);

      // Assert
      expect(result).toBe(session);
      
      // Room should remain unchanged
      const resultRoom = result.findRoomById(roomId);
      expect(resultRoom!.occupiedSeats).toHaveLength(1);
      expect(resultRoom!.reservedSeats).toHaveLength(0);
      expect(resultRoom!.occupiedSeats[0].currentParticipantId).toBe(existingParticipantId.toPrimitives());
    });
  });

  describe('leaving updates presence (exitRoom) but keeps seat reserved', () => {
    it('should update participant presence with exit time', () => {
      // Arrange
      const participantId = SessionTestFactories.createParticipantId();
      const participant = SessionTestFactories.createParticipantPresence(participantId);
      
      const roomId = SessionTestFactories.createRoomId();
      const room = SessionTestFactories.createRoom(roomId).assignParticipantToSeat(
        participantId,
        SessionTestFactories.createInstant()
      );
      
      const participantInRoom = participant.enterRoom(roomId, SessionTestFactories.createInstant());
      
      const session = SessionTestFactories.createRunningSession({
        participants: [participantInRoom.toPrimitives()],
        rooms: [room.toPrimitives()],
      });
      
      const leaveTime = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.onLeave(participantId, leaveTime);

      // Assert
      expect(result).not.toBe(session);
      
      const resultParticipant = result.findParticipantById(participantId);
      expect(resultParticipant!.toPrimitives().leftAt).toBe(leaveTime.toPrimitives());
    });

    it('should reserve seat for reconnect in current room', () => {
      // Arrange
      const participantId = SessionTestFactories.createParticipantId();
      const participant = SessionTestFactories.createParticipantPresence(participantId);
      
      const roomId = SessionTestFactories.createRoomId();
      const room = SessionTestFactories.createRoom(roomId).assignParticipantToSeat(
        participantId,
        SessionTestFactories.createInstant()
      );
      
      const participantInRoom = participant.enterRoom(roomId, SessionTestFactories.createInstant());
      
      const session = SessionTestFactories.createRunningSession({
        participants: [participantInRoom.toPrimitives()],
        rooms: [room.toPrimitives()],
      });
      
      const leaveTime = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.onLeave(participantId, leaveTime);

      // Assert
      const resultRoom = result.findRoomById(roomId);
      expect(resultRoom!.occupiedSeats).toHaveLength(0); // No longer occupied
      expect(resultRoom!.reservedSeats).toHaveLength(1); // Now reserved
      expect(resultRoom!.reservedSeats[0].currentParticipantId).toBe(participantId.toPrimitives());
      expect(resultRoom!.reservedSeats[0].isReservedForReconnect).toBe(true);
    });

    it('should preserve participant in roster', () => {
      // Arrange
      const participantId = SessionTestFactories.createParticipantId();
      const participant = SessionTestFactories.createParticipantPresence(participantId);
      
      const roomId = SessionTestFactories.createRoomId();
      const room = SessionTestFactories.createRoom(roomId).assignParticipantToSeat(
        participantId,
        SessionTestFactories.createInstant()
      );
      
      const participantInRoom = participant.enterRoom(roomId, SessionTestFactories.createInstant());
      
      const session = SessionTestFactories.createRunningSession({
        participants: [participantInRoom.toPrimitives()],
        rooms: [room.toPrimitives()],
      });
      
      const leaveTime = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.onLeave(participantId, leaveTime);

      // Assert
      expect(result.participantCount).toBe(1); // Still in roster
      expect(result.findParticipantById(participantId)).toBeDefined();
    });

    it('should work with multiple participants', () => {
      // Arrange
      const participantId1 = SessionTestFactories.createParticipantId();
      const participantId2 = SessionTestFactories.createParticipantId();
      
      const participant1 = SessionTestFactories.createParticipantPresence(participantId1);
      const participant2 = SessionTestFactories.createParticipantPresence(participantId2);
      
      const roomId = SessionTestFactories.createRoomId();
      let room = SessionTestFactories.createRoom(roomId);
      room = room.assignParticipantToSeat(participantId1, SessionTestFactories.createInstant());
      room = room.assignParticipantToSeat(participantId2, SessionTestFactories.createInstant());
      
      const participant1InRoom = participant1.enterRoom(roomId, SessionTestFactories.createInstant());
      const participant2InRoom = participant2.enterRoom(roomId, SessionTestFactories.createInstant());
      
      const session = SessionTestFactories.createRunningSession({
        participants: [participant1InRoom.toPrimitives(), participant2InRoom.toPrimitives()],
        rooms: [room.toPrimitives()],
      });
      
      const leaveTime = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result1 = session.onLeave(participantId1, leaveTime);
      const result2 = result1.onLeave(participantId2, leaveTime);

      // Assert
      expect(result2.participantCount).toBe(2); // Both still in roster
      
      const finalRoom = result2.findRoomById(roomId);
      expect(finalRoom!.occupiedSeats).toHaveLength(0);
      expect(finalRoom!.reservedSeats).toHaveLength(2);
      
      const reservedParticipantIds = finalRoom!.reservedSeats.map(seat => seat.currentParticipantId);
      expect(reservedParticipantIds).toContain(participantId1.toPrimitives());
      expect(reservedParticipantIds).toContain(participantId2.toPrimitives());
    });
  });

  describe('if participant was not seated (no currentRoomId) no reservations created', () => {
    it('should not create reservations when participant was not seated', () => {
      // Arrange
      const participantId = SessionTestFactories.createParticipantId();
      const participant = SessionTestFactories.createParticipantPresence(participantId); // Not seated
      
      const roomId = SessionTestFactories.createRoomId();
      const room = SessionTestFactories.createRoom(roomId);
      
      const session = SessionTestFactories.createRunningSession({
        participants: [participant.toPrimitives()],
        rooms: [room.toPrimitives()],
      });
      
      const leaveTime = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.onLeave(participantId, leaveTime);

      // Assert
      expect(result).not.toBe(session);
      
      // Participant should have exit time updated
      const resultParticipant = result.findParticipantById(participantId);
      expect(resultParticipant!.toPrimitives().leftAt).toBe(leaveTime.toPrimitives());
      
      // Room should remain unchanged (no reservations)
      const resultRoom = result.findRoomById(roomId);
      expect(resultRoom!.occupiedSeats).toHaveLength(0);
      expect(resultRoom!.reservedSeats).toHaveLength(0);
    });

    it('should not affect any rooms when participant was not seated', () => {
      // Arrange
      const participantId = SessionTestFactories.createParticipantId();
      const participant = SessionTestFactories.createParticipantPresence(participantId);
      
      const room1Id = SessionTestFactories.createRoomId();
      const room2Id = SessionTestFactories.createRoomId();
      const room1 = SessionTestFactories.createRoom(room1Id);
      const room2 = SessionTestFactories.createRoom(room2Id);
      
      const session = SessionTestFactories.createRunningSession({
        participants: [participant.toPrimitives()],
        rooms: [room1.toPrimitives(), room2.toPrimitives()],
      });
      
      const leaveTime = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.onLeave(participantId, leaveTime);

      // Assert
      const resultRoom1 = result.findRoomById(room1Id);
      const resultRoom2 = result.findRoomById(room2Id);
      
      expect(resultRoom1!.occupiedSeats).toHaveLength(0);
      expect(resultRoom1!.reservedSeats).toHaveLength(0);
      expect(resultRoom2!.occupiedSeats).toHaveLength(0);
      expect(resultRoom2!.reservedSeats).toHaveLength(0);
    });
  });

  describe('returned session contains unchanged seat count except reservation converted', () => {
    it('should maintain total seat usage (occupied + reserved)', () => {
      // Arrange
      const participantId = SessionTestFactories.createParticipantId();
      const participant = SessionTestFactories.createParticipantPresence(participantId);
      
      const roomId = SessionTestFactories.createRoomId();
      const room = SessionTestFactories.createRoom(roomId).assignParticipantToSeat(
        participantId,
        SessionTestFactories.createInstant()
      );
      
      const participantInRoom = participant.enterRoom(roomId, SessionTestFactories.createInstant());
      
      const session = SessionTestFactories.createRunningSession({
        participants: [participantInRoom.toPrimitives()],
        rooms: [room.toPrimitives()],
      });
      
      const leaveTime = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.onLeave(participantId, leaveTime);

      // Assert
      const originalRoom = session.findRoomById(roomId);
      const resultRoom = result.findRoomById(roomId);
      
      const originalTotalUsed = originalRoom!.occupiedSeats.length + originalRoom!.reservedSeats.length;
      const resultTotalUsed = resultRoom!.occupiedSeats.length + resultRoom!.reservedSeats.length;
      
      expect(resultTotalUsed).toBe(originalTotalUsed); // Same total usage
      expect(originalRoom!.occupiedSeats.length).toBe(1);
      expect(originalRoom!.reservedSeats.length).toBe(0);
      expect(resultRoom!.occupiedSeats.length).toBe(0);
      expect(resultRoom!.reservedSeats.length).toBe(1);
    });

    it('should not affect other rooms seat counts', () => {
      // Arrange
      const participantId = SessionTestFactories.createParticipantId();
      const otherParticipantId = SessionTestFactories.createParticipantId();
      
      const participant = SessionTestFactories.createParticipantPresence(participantId);
      const otherParticipant = SessionTestFactories.createParticipantPresence(otherParticipantId);
      
      const room1Id = SessionTestFactories.createRoomId();
      const room2Id = SessionTestFactories.createRoomId();
      
      const room1 = SessionTestFactories.createRoom(room1Id).assignParticipantToSeat(
        participantId,
        SessionTestFactories.createInstant()
      );
      const room2 = SessionTestFactories.createRoom(room2Id).assignParticipantToSeat(
        otherParticipantId,
        SessionTestFactories.createInstant()
      );
      
      const participantInRoom1 = participant.enterRoom(room1Id, SessionTestFactories.createInstant());
      const otherParticipantInRoom2 = otherParticipant.enterRoom(room2Id, SessionTestFactories.createInstant());
      
      const session = SessionTestFactories.createRunningSession({
        participants: [participantInRoom1.toPrimitives(), otherParticipantInRoom2.toPrimitives()],
        rooms: [room1.toPrimitives(), room2.toPrimitives()],
      });
      
      const leaveTime = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.onLeave(participantId, leaveTime);

      // Assert
      const resultRoom1 = result.findRoomById(room1Id);
      const resultRoom2 = result.findRoomById(room2Id);
      
      // Room1 should have reservation
      expect(resultRoom1!.occupiedSeats).toHaveLength(0);
      expect(resultRoom1!.reservedSeats).toHaveLength(1);
      
      // Room2 should remain unchanged
      expect(resultRoom2!.occupiedSeats).toHaveLength(1);
      expect(resultRoom2!.reservedSeats).toHaveLength(0);
      expect(resultRoom2!.occupiedSeats[0].currentParticipantId).toBe(otherParticipantId.toPrimitives());
    });
  });

  describe('immutability verification', () => {
    it('should return new instance when participant leaves', () => {
      // Arrange
      const participantId = SessionTestFactories.createParticipantId();
      const participant = SessionTestFactories.createParticipantPresence(participantId);
      
      const roomId = SessionTestFactories.createRoomId();
      const room = SessionTestFactories.createRoom(roomId).assignParticipantToSeat(
        participantId,
        SessionTestFactories.createInstant()
      );
      
      const participantInRoom = participant.enterRoom(roomId, SessionTestFactories.createInstant());
      
      const session = SessionTestFactories.createRunningSession({
        participants: [participantInRoom.toPrimitives()],
        rooms: [room.toPrimitives()],
      });
      
      const leaveTime = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.onLeave(participantId, leaveTime);

      // Assert
      expect(result).not.toBe(session);
      
      // Original should be unchanged
      const originalParticipant = session.findParticipantById(participantId);
      expect(originalParticipant!.toPrimitives().leftAt).toBeUndefined();
      
      const originalRoom = session.findRoomById(roomId);
      expect(originalRoom!.occupiedSeats).toHaveLength(1);
      expect(originalRoom!.reservedSeats).toHaveLength(0);
      
      // Result should have changes
      const resultParticipant = result.findParticipantById(participantId);
      expect(resultParticipant!.toPrimitives().leftAt).toBe(leaveTime.toPrimitives());
      
      const resultRoom = result.findRoomById(roomId);
      expect(resultRoom!.occupiedSeats).toHaveLength(0);
      expect(resultRoom!.reservedSeats).toHaveLength(1);
    });

    it('should preserve all other properties', () => {
      // Arrange
      const participantId = SessionTestFactories.createParticipantId();
      const participant = SessionTestFactories.createParticipantPresence(participantId);
      
      const roomId = SessionTestFactories.createRoomId();
      const room = SessionTestFactories.createRoom(roomId).assignParticipantToSeat(
        participantId,
        SessionTestFactories.createInstant()
      );
      
      const participantInRoom = participant.enterRoom(roomId, SessionTestFactories.createInstant());
      
      const rounds = [SessionTestFactories.createRoundInstance().toPrimitives()];
      
      const session = SessionTestFactories.createRunningSession({
        participants: [participantInRoom.toPrimitives()],
        rooms: [room.toPrimitives()],
        rounds,
        currentRoundIndex: 0,
      });
      
      const leaveTime = SessionTestFactories.createInstant(SessionTestFactories.LATER_TIMESTAMP);

      // Act
      const result = session.onLeave(participantId, leaveTime);

      // Assert
      expect(result.sessionId).toBe(session.sessionId);
      expect(result.config).toBe(session.config);
      expect(result.currentState).toBe(session.currentState);
      expect(result.createdByUserId).toBe(session.createdByUserId);
      expect(result.mainRoomId).toBe(session.mainRoomId);
      expect(result.createdAt).toBe(session.createdAt);
      expect(result.allRounds).toHaveLength(session.allRounds.length);
      expect(result.participantCount).toBe(session.participantCount);
      expect(result.toPrimitives().currentRoundIndex).toBe(session.toPrimitives().currentRoundIndex);
      expect(result.toPrimitives().hostId).toBe(session.toPrimitives().hostId);
    });
  });
});
