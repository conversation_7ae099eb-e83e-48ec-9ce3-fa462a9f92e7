import { Clock } from './clock';

/**
 * System clock implementation that uses the system's current time
 * 
 * This implementation delegates to JavaScript's Date.now() to get
 * the actual system time. It should be used in production environments
 * where real-time behavior is required.
 * 
 * Usage:
 * ```typescript
 * const clock = new SystemClock();
 * const now = clock.now(); // Returns current system time
 * ```
 */
export class SystemClock implements Clock {
  /**
   * Get the current system time in milliseconds since Unix epoch
   * 
   * @returns The current system time as returned by Date.now()
   */
  now(): number {
    return Date.now();
  }
}
