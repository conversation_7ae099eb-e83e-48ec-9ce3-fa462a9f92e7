/**
 * Clock abstraction for server-authoritative time source
 * 
 * This interface provides a consistent way to get the current time
 * across the domain layer. It enables dependency injection and testing
 * by allowing different implementations (system time, fixed time, etc.).
 * 
 * The time is returned as a number representing milliseconds since
 * the Unix epoch (January 1, 1970 UTC), which is compatible with
 * JavaScript's Date.now() and the Instant primitive.
 */
export interface Clock {
  /**
   * Get the current time in milliseconds since Unix epoch
   * 
   * @returns The current time as a number (milliseconds since epoch)
   */
  now(): number;
}
