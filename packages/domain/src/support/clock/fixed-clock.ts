import { Clock } from './clock';

/**
 * Fixed clock implementation that always returns the same time
 * 
 * This implementation is useful for testing scenarios where you need
 * deterministic time behavior. It always returns the same fixed time
 * value that was provided during construction.
 * 
 * Usage:
 * ```typescript
 * const fixedTime = 1640995200000; // January 1, 2022 00:00:00 UTC
 * const clock = new FixedClock(fixedTime);
 * const now = clock.now(); // Always returns 1640995200000
 * ```
 */
export class FixedClock implements Clock {
  /**
   * Create a fixed clock with the specified time
   * 
   * @param time The fixed time in milliseconds since Unix epoch
   */
  constructor(private readonly time: number) {}

  /**
   * Get the fixed time that was set during construction
   * 
   * @returns The fixed time in milliseconds since Unix epoch
   */
  now(): number {
    return this.time;
  }
}
