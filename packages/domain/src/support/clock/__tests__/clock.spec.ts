import { Clock, SystemClock, FixedClock } from '../index';

describe('Clock Abstraction', () => {
  describe('Clock interface', () => {
    it('should be implementable by different clock types', () => {
      const systemClock: Clock = new SystemClock();
      const fixedClock: Clock = new FixedClock(1640995200000);

      // Both should implement the Clock interface
      expect(typeof systemClock.now).toBe('function');
      expect(typeof fixedClock.now).toBe('function');

      // Both should return numbers
      expect(typeof systemClock.now()).toBe('number');
      expect(typeof fixedClock.now()).toBe('number');
    });
  });

  describe('SystemClock', () => {
    let systemClock: SystemClock;

    beforeEach(() => {
      systemClock = new SystemClock();
    });

    it('should return current system time', () => {
      const beforeCall = Date.now();
      const clockTime = systemClock.now();
      const afterCall = Date.now();

      // Clock time should be between before and after the call
      expect(clockTime).toBeGreaterThanOrEqual(beforeCall);
      expect(clockTime).toBeLessThanOrEqual(afterCall);
    });

    it('should return non-negative numbers', () => {
      const time = systemClock.now();
      expect(time).toBeGreaterThanOrEqual(0);
    });

    it('should return increasing values over time', async () => {
      const time1 = systemClock.now();
      
      // Wait a small amount to ensure time progresses
      await new Promise(resolve => setTimeout(resolve, 1));
      
      const time2 = systemClock.now();
      
      expect(time2).toBeGreaterThan(time1);
    });

    it('should return values consistent with Date.now()', () => {
      // Mock Date.now to return a specific value
      const mockTime = 1640995200000;
      const originalDateNow = Date.now;
      Date.now = jest.fn(() => mockTime);

      try {
        const clockTime = systemClock.now();
        expect(clockTime).toBe(mockTime);
        expect(Date.now).toHaveBeenCalled();
      } finally {
        // Restore original Date.now
        Date.now = originalDateNow;
      }
    });

    it('should be usable as Clock interface', () => {
      const clock: Clock = systemClock;
      const time = clock.now();
      
      expect(typeof time).toBe('number');
      expect(time).toBeGreaterThanOrEqual(0);
    });
  });

  describe('FixedClock', () => {
    const fixedTime = 1640995200000; // January 1, 2022 00:00:00 UTC
    let fixedClock: FixedClock;

    beforeEach(() => {
      fixedClock = new FixedClock(fixedTime);
    });

    it('should return the fixed time provided in constructor', () => {
      const time = fixedClock.now();
      expect(time).toBe(fixedTime);
    });

    it('should always return the same value', () => {
      const time1 = fixedClock.now();
      const time2 = fixedClock.now();
      const time3 = fixedClock.now();

      expect(time1).toBe(fixedTime);
      expect(time2).toBe(fixedTime);
      expect(time3).toBe(fixedTime);
      expect(time1).toBe(time2);
      expect(time2).toBe(time3);
    });

    it('should work with different fixed times', () => {
      const time1 = 1000000000000; // September 9, 2001
      const time2 = 2000000000000; // May 18, 2033
      const time3 = 0; // January 1, 1970

      const clock1 = new FixedClock(time1);
      const clock2 = new FixedClock(time2);
      const clock3 = new FixedClock(time3);

      expect(clock1.now()).toBe(time1);
      expect(clock2.now()).toBe(time2);
      expect(clock3.now()).toBe(time3);
    });

    it('should handle negative times', () => {
      const negativeTime = -1000000000000; // Before Unix epoch
      const clock = new FixedClock(negativeTime);
      
      expect(clock.now()).toBe(negativeTime);
    });

    it('should handle zero time', () => {
      const clock = new FixedClock(0);
      expect(clock.now()).toBe(0);
    });

    it('should handle very large times', () => {
      const largeTime = Number.MAX_SAFE_INTEGER;
      const clock = new FixedClock(largeTime);
      
      expect(clock.now()).toBe(largeTime);
    });

    it('should be usable as Clock interface', () => {
      const clock: Clock = fixedClock;
      const time = clock.now();
      
      expect(time).toBe(fixedTime);
    });

    it('should be deterministic for testing', () => {
      // This demonstrates the main use case for FixedClock in testing
      const testTime = 1640995200000;
      const clock = new FixedClock(testTime);
      
      // Simulate multiple operations that need the same time
      const operation1Time = clock.now();
      const operation2Time = clock.now();
      const operation3Time = clock.now();
      
      // All operations get the same deterministic time
      expect(operation1Time).toBe(testTime);
      expect(operation2Time).toBe(testTime);
      expect(operation3Time).toBe(testTime);
    });
  });

  describe('Clock implementations comparison', () => {
    it('should have different behavior between SystemClock and FixedClock', () => {
      const fixedTime = 1640995200000;
      const systemClock = new SystemClock();
      const fixedClock = new FixedClock(fixedTime);

      const systemTime = systemClock.now();
      const fixedClockTime = fixedClock.now();

      // System time should be current (much larger than our fixed time from 2022)
      expect(systemTime).toBeGreaterThan(fixedTime);
      
      // Fixed clock should return exactly the fixed time
      expect(fixedClockTime).toBe(fixedTime);
    });

    it('should both implement Clock interface correctly', () => {
      const clocks: Clock[] = [
        new SystemClock(),
        new FixedClock(1640995200000)
      ];

      for (const clock of clocks) {
        const time = clock.now();
        expect(typeof time).toBe('number');
        expect(Number.isFinite(time)).toBe(true);
      }
    });
  });

  describe('Usage patterns', () => {
    it('should support dependency injection pattern', () => {
      // Simulate a service that depends on a clock
      class TimeService {
        constructor(private clock: Clock) {}
        
        getCurrentTime(): number {
          return this.clock.now();
        }
        
        isAfter(time: number): boolean {
          return this.clock.now() > time;
        }
      }

      // Can inject different clock implementations
      const systemService = new TimeService(new SystemClock());
      const testService = new TimeService(new FixedClock(1640995200000));

      expect(typeof systemService.getCurrentTime()).toBe('number');
      expect(testService.getCurrentTime()).toBe(1640995200000);
      
      // Test service has predictable behavior
      expect(testService.isAfter(1640995100000)).toBe(true);
      expect(testService.isAfter(1640995300000)).toBe(false);
    });

    it('should enable time-based testing scenarios', () => {
      const baseTime = 1640995200000; // January 1, 2022 00:00:00 UTC
      
      // Test scenario: check if events are in correct order
      const events = [
        { name: 'event1', clock: new FixedClock(baseTime) },
        { name: 'event2', clock: new FixedClock(baseTime + 1000) },
        { name: 'event3', clock: new FixedClock(baseTime + 2000) }
      ];

      const timestamps = events.map(event => ({
        name: event.name,
        timestamp: event.clock.now()
      }));

      expect(timestamps[0].timestamp).toBe(baseTime);
      expect(timestamps[1].timestamp).toBe(baseTime + 1000);
      expect(timestamps[2].timestamp).toBe(baseTime + 2000);
      
      // Verify chronological order
      expect(timestamps[1].timestamp).toBeGreaterThan(timestamps[0].timestamp);
      expect(timestamps[2].timestamp).toBeGreaterThan(timestamps[1].timestamp);
    });
  });
});
